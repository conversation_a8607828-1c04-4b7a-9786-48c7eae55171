/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { afterEach, beforeAll, beforeEach, describe, expect, it } from 'vitest';
import { createClusterContainer } from '../../src/infrastructure/di/Container.js';
import { TYPES } from '../../src/shared/types/TYPES.js';
import { Configuration } from '../../src/infrastructure/config/Configuration.js';
import type { IEventBus } from '../../src/infrastructure/events/TestInterfaces.js';
import { HubCreatedEvent, CommandExecutedEvent } from '../../src/domain/events/DomainEvents.js';
import type { Container } from 'inversify';

// Set up test environment variables
beforeAll(() => {
  process.env.BOT_TOKEN = 'test_token_123';
  process.env.CLIENT_ID = 'test_client_123';
  process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/test_db';
  process.env.REDIS_URL = 'redis://localhost:6379';
  process.env.CLUSTER_ID = 'test-cluster';
  process.env.NODE_ENV = 'development'; // Set to development to match Configuration logic
});

describe('Architecture Foundation Tests', () => {
  let container: Container;

  beforeEach(async () => {
    // Create a fresh container for each test
    container = await createClusterContainer('test-cluster');
  });

  afterEach(() => {
    // Cleanup
    container.unbindAll();
  });

  describe('Dependency Injection Container', () => {
    it('should create container successfully', () => {
      expect(container).toBeDefined();
    });

    it('should provide configuration service', () => {
      const config = container.get<Configuration>(TYPES.Configuration);
      expect(config).toBeDefined();
      expect(config.isDevelopment).toBe(true); // Default in test
    });

    it('should provide event bus service', () => {
      const eventBus = container.get<IEventBus>(TYPES.EventBus);
      expect(eventBus).toBeDefined();
      expect(eventBus.getClusterId()).toBe('test-cluster');
    });
  });

  describe('Configuration System', () => {
    it('should load configuration with defaults', () => {
      const config = container.get<Configuration>(TYPES.Configuration);

      // Test some default values
      expect(config['api'].port).toBe(3000);
      expect(config['logging'].level).toBe('error');
    });

    it('should validate required configuration', () => {
      // This test would fail if required env vars are missing
      // In a real scenario, you'd set test env vars
      expect(() => container.get<Configuration>(TYPES.Configuration)).not.toThrow();
    });
  });

  describe('Event Bus System', () => {
    let eventBus: IEventBus;

    beforeEach(() => {
      eventBus = container.get<IEventBus>(TYPES.EventBus);
    });

    it('should handle local events', async () => {
      const receivedEvents: HubCreatedEvent[] = [];

      // Create a simple event handler
      const handler = {
        eventType: 'hub.created',
        handlerId: 'test-handler',
        handle: async (event: HubCreatedEvent) => {
          receivedEvents.push(event);
        },
      };

      // Subscribe to events
      eventBus.subscribe('hub.created', handler);

      // Publish an event
      const event = new HubCreatedEvent('hub-123', 'Test Hub', 'user-456', 0, 'test-cluster');
      await eventBus.publish(event);

      // Verify event was received
      expect(receivedEvents).toHaveLength(1);
      const receivedEvent = receivedEvents[0];
      expect(receivedEvent.hubId).toBe('hub-123');
      expect(receivedEvent.hubName).toBe('Test Hub');
      expect(receivedEvent.shouldBroadcast).toBe(true);
    });

    it('should distinguish between broadcast and local events', async () => {
      const hubEvent = new HubCreatedEvent('hub-123', 'Test Hub', 'user-456');
      const commandEvent = new CommandExecutedEvent('test', 'user-123', 'guild-456', 100, true);

      expect(hubEvent.shouldBroadcast).toBe(true);
      expect(commandEvent.shouldBroadcast).toBe(false);
    });

    it('should maintain event history', async () => {
      const event1 = new HubCreatedEvent('hub-1', 'Hub 1', 'user-1');
      const event2 = new CommandExecutedEvent('test', 'user-2', 'guild-1', 50, true);

      await eventBus.publish(event1);
      await eventBus.publish(event2);

      const history = eventBus.getLocalEventHistory();
      expect(history).toHaveLength(2);
      expect(history[0].type).toBe('hub.created');
      expect(history[1].type).toBe('command.executed');
    });

    it('should handle multiple subscribers to same event', async () => {
      let handler1Called = false;
      let handler2Called = false;

      const handler1 = {
        eventType: 'hub.created',
        handlerId: 'handler-1',
        handle: async () => {
          handler1Called = true;
        },
      };

      const handler2 = {
        eventType: 'hub.created',
        handlerId: 'handler-2',
        handle: async () => {
          handler2Called = true;
        },
      };

      eventBus.subscribe('hub.created', handler1);
      eventBus.subscribe('hub.created', handler2);

      const event = new HubCreatedEvent('hub-123', 'Test Hub', 'user-456');
      await eventBus.publish(event);

      expect(handler1Called).toBe(true);
      expect(handler2Called).toBe(true);
    });

    it('should check health status', async () => {
      const isHealthy = await eventBus.isHealthy();
      expect(isHealthy).toBe(true);
    });
  });

  describe('Error Handling', () => {
    it('should handle event handler errors gracefully', async () => {
      const eventBus = container.get<IEventBus>(TYPES.EventBus);

      const faultyHandler = {
        eventType: 'hub.created',
        handlerId: 'faulty-handler',
        handle: async () => {
          throw new Error('Handler error');
        },
      };

      const goodHandler = {
        eventType: 'hub.created',
        handlerId: 'good-handler',
        handle: async () => {
          // This should still execute
        },
      };

      eventBus.subscribe('hub.created', faultyHandler);
      eventBus.subscribe('hub.created', goodHandler);

      const event = new HubCreatedEvent('hub-123', 'Test Hub', 'user-456');

      // Should not throw despite handler error
      await expect(eventBus.publish(event)).resolves.not.toThrow();
    });
  });

  describe('Cluster Awareness', () => {
    it('should have correct cluster ID', () => {
      const eventBus = container.get<IEventBus>(TYPES.EventBus);
      expect(eventBus.getClusterId()).toBe('test-cluster');
    });

    it('should include cluster ID in events', async () => {
      const event = new HubCreatedEvent('hub-123', 'Test Hub', 'user-456', 0, 'test-cluster');
      expect(event.clusterId).toBe('test-cluster');
    });
  });
});
