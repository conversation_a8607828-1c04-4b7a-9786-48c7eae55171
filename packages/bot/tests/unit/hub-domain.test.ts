/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { describe, it, expect } from 'vitest';
import { Hub } from '../../src/domain/entities/Hub.js';

describe('Hub Domain Entity', () => {
  describe('Hub Creation', () => {
    it('should create a hub with valid data', () => {
      const hub = Hub.create({
        name: 'Test Hub',
        description: 'A test hub for unit testing',
        ownerId: 'user123',
        iconUrl: 'https://example.com/icon.png',
      });

      expect(hub.name).toBe('Test Hub');
      expect(hub.description).toBe('A test hub for unit testing');
      expect(hub.ownerId).toBe('user123');
      expect(hub.iconUrl).toBe('https://example.com/icon.png');
      expect(hub.isPrivate).toBe(true); // Default behavior
      expect(hub.isLocked).toBe(false);
      expect(hub.isNsfw).toBe(false);
    });

    it('should create a hub without optional fields', () => {
      const hub = Hub.create({
        name: 'Simple Hub',
        description: 'A simple hub',
        ownerId: 'user456',
      });

      expect(hub.name).toBe('Simple Hub');
      expect(hub.description).toBe('A simple hub');
      expect(hub.ownerId).toBe('user456');
      expect(hub.iconUrl).toBe(''); // Default empty string
      expect(hub.bannerUrl).toBe(null);
    });
  });

  describe('Hub Ownership', () => {
    it('should correctly identify hub owner', () => {
      const hub = Hub.create({
        name: 'Test Hub',
        description: 'Test description',
        ownerId: 'user123',
      });

      expect(hub.isOwner('user123')).toBe(true);
      expect(hub.isOwner('user456')).toBe(false);
      expect(hub.isOwner('')).toBe(false);
    });

    it('should transfer ownership correctly', () => {
      const hub = Hub.create({
        name: 'Test Hub',
        description: 'Test description',
        ownerId: 'user123',
      });

      expect(hub.isOwner('user123')).toBe(true);

      hub.transferOwnership('user456');

      expect(hub.isOwner('user123')).toBe(false);
      expect(hub.isOwner('user456')).toBe(true);
    });
  });

  describe('Hub Updates', () => {
    it('should update hub properties', () => {
      const hub = Hub.create({
        name: 'Test Hub',
        description: 'Original description',
        ownerId: 'user123',
      });

      hub.update({
        description: 'Updated description',
        private: false,
        nsfw: true,
      });

      expect(hub.description).toBe('Updated description');
      expect(hub.isPrivate).toBe(false);
      expect(hub.isNsfw).toBe(true);
    });

    it('should update rules correctly', () => {
      const hub = Hub.create({
        name: 'Test Hub',
        description: 'Test description',
        ownerId: 'user123',
      });

      const rules = ['No spam', 'Be respectful', 'Follow Discord ToS'];
      hub.updateRules(rules);

      expect(hub.rules).toEqual(rules);
      expect(hub.rules).toHaveLength(3);
    });
  });

  describe('Domain Events', () => {
    it('should generate domain events on creation', () => {
      const hub = Hub.create({
        name: 'Test Hub',
        description: 'Test description',
        ownerId: 'user123',
      });

      const events = hub.getDomainEvents();
      expect(events).toHaveLength(1);
      expect(events[0].type).toBe('hub.created');
      expect(events[0].aggregateId).toBe(hub.id);
    });

    it('should generate events on updates', () => {
      const hub = Hub.create({
        name: 'Test Hub',
        description: 'Test description',
        ownerId: 'user123',
      });

      hub.clearDomainEvents(); // Clear creation event

      hub.update({
        description: 'Updated description',
        private: false,
      });

      const events = hub.getDomainEvents();
      expect(events.length).toBeGreaterThan(0);

      // Should have both updated and visibility changed events
      const updateEvents = events.filter((e) => e.type === 'hub.updated');
      const visibilityEvents = events.filter((e) => e.type === 'hub.visibility.changed');

      expect(updateEvents).toHaveLength(1);
      expect(visibilityEvents).toHaveLength(1);
    });

    it('should clear domain events', () => {
      const hub = Hub.create({
        name: 'Test Hub',
        description: 'Test description',
        ownerId: 'user123',
      });

      expect(hub.getDomainEvents()).toHaveLength(1);

      hub.clearDomainEvents();

      expect(hub.getDomainEvents()).toHaveLength(0);
    });
  });

  describe('Business Rules', () => {
    it('should validate if hub can be made public', () => {
      const hub = Hub.create({
        name: 'Test Hub',
        description: 'Short', // Less than 10 chars
        ownerId: 'user123',
      });

      // Cannot be made public with short description and no rules
      expect(hub.canBeMadePublic()).toBe(false);

      // Update description to meet requirements
      hub.update({ description: 'This is a longer description that meets the requirements' });
      expect(hub.canBeMadePublic()).toBe(false); // Still no rules

      // Add rules
      hub.updateRules(['Rule 1', 'Rule 2']);
      expect(hub.canBeMadePublic()).toBe(true);
    });

    it('should validate if hub can be deleted', () => {
      const hub = Hub.create({
        name: 'Test Hub',
        description: 'Test description',
        ownerId: 'user123',
      });

      // For now, any hub can be deleted (basic implementation)
      expect(hub.canBeDeleted()).toBe(true);
    });
  });

  describe('Activity Tracking', () => {
    it('should mark hub as active', () => {
      const hub = Hub.create({
        name: 'Test Hub',
        description: 'Test description',
        ownerId: 'user123',
      });

      const originalLastActive = hub.lastActive;

      // Wait a tiny bit to ensure timestamp difference
      setTimeout(() => {
        hub.markActive();
        expect(hub.lastActive.getTime()).toBeGreaterThan(originalLastActive.getTime());
      }, 1);
    });
  });

  describe('Persistence', () => {
    it('should convert to persistence format', () => {
      const hub = Hub.create({
        name: 'Test Hub',
        description: 'Test description',
        ownerId: 'user123',
        iconUrl: 'https://example.com/icon.png',
      });

      const persistenceData = hub.toPersistence();

      expect(persistenceData.name).toBe('Test Hub');
      expect(persistenceData.description).toBe('Test description');
      expect(persistenceData.ownerId).toBe('user123');
      expect(persistenceData.iconUrl).toBe('https://example.com/icon.png');
    });

    it('should reconstruct from persistence data', () => {
      const persistenceData = {
        id: 'hub-123',
        name: 'Persisted Hub',
        description: 'A hub from persistence',
        ownerId: 'user123',
        iconUrl: 'https://example.com/icon.png',
        bannerUrl: null,
        shortDescription: null,
        welcomeMessage: null,
        private: false,
        locked: false,
        nsfw: false,
        appealCooldownHours: 24,
        settings: 0,
        rules: ['Rule 1'],
        createdAt: new Date(),
        updatedAt: new Date(),
        lastActive: new Date(),
        verified: false,
        partnered: false,
        featured: false,
        language: null,
        region: null,
        weeklyMessageCount: 0,
        activityLevel: 'LOW' as const,
      };

      const hub = Hub.fromPersistence(persistenceData);

      expect(hub.id).toBe('hub-123');
      expect(hub.name).toBe('Persisted Hub');
      expect(hub.description).toBe('A hub from persistence');
      expect(hub.ownerId).toBe('user123');
      expect(hub.isPrivate).toBe(false);
      expect(hub.rules).toEqual(['Rule 1']);
    });
  });
});
