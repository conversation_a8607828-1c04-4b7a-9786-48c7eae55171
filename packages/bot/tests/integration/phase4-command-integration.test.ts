/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { describe, it, expect, beforeEach, afterEach, beforeAll } from 'vitest';
import { createClusterContainer } from '../../src/infrastructure/di/Container.js';
import { TYPES } from '../../src/shared/types/TYPES.js';
import type { IEventBus } from '../../src/infrastructure/events/TestInterfaces.js';
import { CommandRegistry } from '../../src/presentation/commands/CommandRegistry.js';
import { EventHandlerRegistryService } from '../../src/presentation/services/EventHandlerRegistryService.js';
import { CreateDonationUseCase } from '../../src/application/use-cases/donations/CreateDonationUseCase.js';
import { DonationCreatedEvent, PremiumGrantedEvent } from '../../src/domain/events/DomainEvents.js';
import type { Container } from 'inversify';

// Set up test environment variables
beforeAll(() => {
  process.env.BOT_TOKEN = 'test_token_123';
  process.env.CLIENT_ID = 'test_client_123';
  process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/test_db';
  process.env.REDIS_URL = 'redis://localhost:6379';
  process.env.CLUSTER_ID = 'test-cluster';
  process.env.NODE_ENV = 'development';
});

describe('Phase 4: Command System Integration Tests', () => {
  let container: Container;
  let eventBus: IEventBus;
  let commandRegistry: CommandRegistry;
  let eventHandlerRegistry: EventHandlerRegistryService;

  beforeEach(async () => {
    container = await createClusterContainer('test-cluster');
    eventBus = container.get<IEventBus>(TYPES.EventBus);
    commandRegistry = container.get<CommandRegistry>(TYPES.CommandRegistry);
    eventHandlerRegistry = container.get<EventHandlerRegistryService>(TYPES.EventHandlerRegistryService);
  });

  afterEach(() => {
    container.unbindAll();
  });

  describe('4.1 Discord Command Handlers (Presentation Layer)', () => {
    it('should create and register command handlers successfully', () => {
      expect(commandRegistry).toBeDefined();
      expect(commandRegistry.getAllCommands()).toBeDefined();

      const commandNames = commandRegistry.getAllCommands().map((cmd) => cmd.metadata.name);
      expect(commandNames).toContain('donation-create');
      expect(commandNames).toContain('premium-status');
    });

    it('should have command handlers with proper metadata', () => {
      const createDonationHandler = commandRegistry.getCommand('donation-create');
      expect(createDonationHandler).toBeDefined();
      expect(createDonationHandler?.metadata.name).toBe('donation-create');
      expect(createDonationHandler?.metadata.description).toContain('Process a new donation');

      const premiumStatusHandler = commandRegistry.getCommand('premium-status');
      expect(premiumStatusHandler).toBeDefined();
      expect(premiumStatusHandler?.metadata.name).toBe('premium-status');
      expect(premiumStatusHandler?.metadata.description).toContain('Check premium status');
    });
  });

  describe('4.2 Presentation Layer Integration', () => {
    it('should have DTOs for Discord interactions available', () => {
      // Test that the DTO types are accessible
      const createDonationHandler = commandRegistry.getCommand('donation-create');
      expect(createDonationHandler).toBeDefined();

      const premiumStatusHandler = commandRegistry.getCommand('premium-status');
      expect(premiumStatusHandler).toBeDefined();
    });

    it('should handle command permission validation', () => {
      const createDonationHandler = commandRegistry.getCommand('donation-create');
      expect(createDonationHandler?.metadata.ownerOnly).toBe(false);

      const premiumStatusHandler = commandRegistry.getCommand('premium-status');
      expect(premiumStatusHandler?.metadata.ownerOnly).toBe(false);
    });
  });

  describe('4.3 Event Handlers Implementation', () => {
    it('should initialize event handler registry successfully', async () => {
      expect(eventHandlerRegistry).toBeDefined();
      await expect(eventHandlerRegistry.initialize()).resolves.toBeUndefined();
    });

    it('should handle domain events with presentation layer handlers', async () => {
      // Initialize the event handlers
      await eventHandlerRegistry.initialize();

      // Create a mock console.log to capture output
      const consoleLogs: string[] = [];
      const originalLog = console.log;
      console.log = (...args: any[]) => {
        consoleLogs.push(args.join(' '));
      };

      try {
        // Publish a donation created event
        const donationEvent = new DonationCreatedEvent(
          'donation-123',
          'user-456',
          25.00,
          'USD',
          1,
          true,
          'test-cluster',
        );

        await eventBus.publish(donationEvent);

        // Wait for async event processing
        await new Promise((resolve) => setTimeout(resolve, 100));

        // Check that the notification handler logged the event
        expect(consoleLogs.some((log) =>
          log.includes('[DonationCreatedNotificationHandler] Processing donation created event'),
        )).toBe(true);

        expect(consoleLogs.some((log) =>
          log.includes('✅ Donation donation-123 created successfully for user user-456'),
        )).toBe(true);

      }
      finally {
        console.log = originalLog;
      }
    });

    it('should handle premium granted events', async () => {
      // Initialize the event handlers
      await eventHandlerRegistry.initialize();

      // Create a mock console.log to capture output
      const consoleLogs: string[] = [];
      const originalLog = console.log;
      console.log = (...args: any[]) => {
        consoleLogs.push(args.join(' '));
      };

      try {
        // Publish a premium granted event
        const premiumEvent = new PremiumGrantedEvent(
          'user-789',
          1,
          new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
          'donation-system',
          'Ko-fi donation processed',
          'test-cluster',
        );

        await eventBus.publish(premiumEvent);

        // Wait for async event processing
        await new Promise((resolve) => setTimeout(resolve, 100));

        // Check that the notification handler logged the event
        expect(consoleLogs.some((log) =>
          log.includes('[PremiumGrantedNotificationHandler] Processing premium granted event'),
        )).toBe(true);

        expect(consoleLogs.some((log) =>
          log.includes('🎉 Premium tier 1 granted to user user-789'),
        )).toBe(true);

      }
      finally {
        console.log = originalLog;
      }
    });

    it('should properly dispose event handlers', async () => {
      await eventHandlerRegistry.initialize();
      await expect(eventHandlerRegistry.dispose()).resolves.toBeUndefined();
    });
  });

  describe('4.4 End-to-End Integration', () => {
    it('should handle complete donation flow with events and notifications', async () => {
      // Initialize event handlers
      await eventHandlerRegistry.initialize();

      // Get the use case for creating donations
      const createDonationUseCase = container.get<CreateDonationUseCase>(TYPES.CreateDonationUseCase);

      // Create a mock console.log to capture output
      const consoleLogs: string[] = [];
      const originalLog = console.log;
      console.log = (...args: any[]) => {
        consoleLogs.push(args.join(' '));
      };

      try {
        // Create a donation through the use case (which should emit events)
        const donationDto = {
          donorId: 'test-user-123',
          amount: 50.0,
          currency: 'USD',
          tier: 1,
          kofiTransactionId: 'kofi-tx-789',
        };

        await createDonationUseCase.execute(donationDto);

        // Wait for async event processing
        await new Promise((resolve) => setTimeout(resolve, 200));

        console.log(consoleLogs);
        // Verify that donation created events were handled
        expect(consoleLogs.some((log) =>
          log.includes('[DonationCreatedNotificationHandler] Processing donation created event'),
        )).toBe(true);

        // Verify that premium granted events were handled if premium was granted
        const hasPremiumEvent = consoleLogs.some((log) =>
          log.includes('[PremiumGrantedNotificationHandler] Processing premium granted event'),
        );

        // Should have premium granted for $50 donation (above $25 threshold)
        expect(hasPremiumEvent).toBe(true);

      }
      finally {
        console.log = originalLog;
      }
    });
  });
});
