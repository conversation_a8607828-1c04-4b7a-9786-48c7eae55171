/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import type { Container } from 'inversify';
import { afterEach, beforeAll, beforeEach, describe, expect, it, vi } from 'vitest';
import { createClusterContainer } from '../../src/infrastructure/di/Container.js';
import { CommandRegistry } from '../../src/presentation/commands/CommandRegistry.js';
import { CreateDonationCommandHandler } from '../../src/presentation/commands/donation/CreateDonationCommandHandler.js';
import { PremiumStatusCommandHandler } from '../../src/presentation/commands/donation/PremiumStatusCommandHandler.js';
import { TYPES } from '../../src/shared/types/TYPES.js';

// Mock Discord.js classes
class MockUser {
  constructor(
    public id: string,
    public username: string,
  ) {}
  displayAvatarURL() {
    return 'https://example.com/avatar.png';
  }
}

class MockContext {
  public user = new MockUser('123456789', 'testuser');
  public commandName = 'test-command';
  public replied = false;
  public deferred = false;
  public guild = null;
  public member = null;
  public userId = '123456789';
  public channelId = '123456789';
  public guildId = '123456789';
  public channel = {
    send: vi.fn(),
  };

  options = {
    getUser: vi.fn(),
    getNumber: vi.fn(),
    getString: vi.fn(),
    getInteger: vi.fn(),
  };

  reply = vi.fn();
  editReply = vi.fn();
  editOrReply = vi.fn();
  deferReply = vi.fn();
  deleteReply = vi.fn();
  showModal = vi.fn();
}

// Set up test environment variables
beforeAll(() => {
  process.env.BOT_TOKEN = 'test_token_123';
  process.env.CLIENT_ID = 'test_client_123';
  process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/test_db';
  process.env.REDIS_URL = 'redis://localhost:6379';
  process.env.CLUSTER_ID = 'test-cluster';
  process.env.NODE_ENV = 'development';
});

describe('Presentation Layer Tests', () => {
  let container: Container;
  let createDonationHandler: CreateDonationCommandHandler;
  let premiumStatusHandler: PremiumStatusCommandHandler;
  let commandRegistry: CommandRegistry;

  beforeEach(async () => {
    container = await createClusterContainer('test-cluster');

    // Get command registry and find specific handlers
    commandRegistry = container.get<CommandRegistry>(TYPES.CommandRegistry);
    const allCommands = commandRegistry.getAllCommands();

    // Find command handlers by their command names
    createDonationHandler = allCommands.find(
      (cmd) => cmd.metadata.name === 'donation-create',
    ) as CreateDonationCommandHandler;
    premiumStatusHandler = allCommands.find(
      (cmd) => cmd.metadata.name === 'premium-status',
    ) as PremiumStatusCommandHandler;
  });

  afterEach(() => {
    container.unbindAll();
  });

  describe('Command Handler Registration', () => {
    it('should create command handlers from container', () => {
      expect(createDonationHandler).toBeDefined();
      expect(premiumStatusHandler).toBeDefined();
      expect(commandRegistry).toBeDefined();
    });

    it('should register commands in registry', () => {
      const commands = commandRegistry.getAllCommands();
      expect(commands.length).toBeGreaterThan(0);

      const commandNames = commands.map((cmd) => cmd.metadata.name);
      expect(commandNames).toContain('donation-create');
      expect(commandNames).toContain('premium-status');
    });

    it('should build valid slash command data', () => {
      const commandData = commandRegistry.getCommandData();
      expect(commandData.length).toBeGreaterThan(0);

      commandData.forEach((command) => {
        expect(command.name).toBeDefined();
        expect(command.description).toBeDefined();
      });
    });
  });

  describe('Command Metadata', () => {
    it('should have correct donation command metadata', () => {
      const metadata = createDonationHandler.metadata;
      expect(metadata.name).toBe('donation-create');
      expect(metadata.description).toContain('donation');
      expect(metadata.category).toBe('donation');
      expect(metadata.cooldown).toBe(10);
    });

    it('should have correct premium status metadata', () => {
      const metadata = premiumStatusHandler.metadata;
      expect(metadata.name).toBe('premium-status');
      expect(metadata.description).toContain('premium');
      expect(metadata.category).toBe('donation');
      expect(metadata.cooldown).toBe(5);
    });
  });

  describe('Permission Checking', () => {
    it('should check permissions correctly', async () => {
      const mockInteraction = new MockContext();

      // Test public command (premium status)
      const hasPermission = await premiumStatusHandler.checkPermissions(mockInteraction);
      expect(hasPermission).toBe(true);
    });

    it('should handle owner-only commands', async () => {
      new MockContext();

      // Test with metadata that has owner-only flag
      const metadata = {
        ...createDonationHandler.metadata,
        ownerOnly: true,
      };

      // Since OWNER_ID is not set in test environment, should return false
      expect(metadata.ownerOnly).toBe(true);
    });
  });

  describe('Command Building', () => {
    it('should build donation command with correct options', () => {
      const commandBuilder = createDonationHandler.buildCommand();
      expect(commandBuilder.name).toBe('donation-create');
      expect(commandBuilder.description).toContain('donation');
    });

    it('should build premium status command with correct options', () => {
      const commandBuilder = premiumStatusHandler.buildCommand();
      expect(commandBuilder.name).toBe('premium-status');
      expect(commandBuilder.description).toContain('premium');
    });
  });

  describe('Error Handling', () => {
    it('should handle errors gracefully', () => {
      const mockInteraction = new MockContext();
      const error = new Error('Test error');

      const result = createDonationHandler['handleError'](error, mockInteraction);

      expect(result.success).toBe(false);
      expect(result.embed).toBeDefined();
      expect(result.ephemeral).toBe(true);
    });
  });

  describe('Command Registry Functionality', () => {
    it('should handle unknown commands', async () => {
      const mockInteraction = new MockContext();
      mockInteraction.commandName = 'non-existent-command';

      // This should not throw an error
      await commandRegistry.executeCommand(mockInteraction);

      expect(mockInteraction.reply).toHaveBeenCalledWith(
        expect.objectContaining({
          content: expect.stringContaining('Unknown command'),
          flags: [MessageFlags.Ephemeral],
        }),
      );
    });

    it('should get commands by category', () => {
      const donationCommands = commandRegistry.getCommandsByCategory('donation');
      expect(donationCommands.length).toBeGreaterThan(0);

      donationCommands.forEach((command) => {
        expect(command.metadata.category).toBe('donation');
      });
    });
  });

  describe('Embed Creation', () => {
    it('should create success embeds', () => {
      const embed = createDonationHandler['createSuccessEmbed']('Test Title', 'Test Description');
      expect(embed.data.title).toContain('Test Title');
      expect(embed.data.description).toBe('Test Description');
      expect(embed.data.color).toBe(0x00ff00);
    });

    it('should create error embeds', () => {
      const embed = createDonationHandler['createErrorEmbed']('Error Title', 'Error Description');
      expect(embed.data.title).toContain('Error Title');
      expect(embed.data.description).toBe('Error Description');
      expect(embed.data.color).toBe(0xff0000);
    });

    it('should create info embeds', () => {
      const embed = createDonationHandler['createInfoEmbed']('Info Title', 'Info Description');
      expect(embed.data.title).toContain('Info Title');
      expect(embed.data.description).toBe('Info Description');
      expect(embed.data.color).toBe(0x0099ff);
    });
  });
});
