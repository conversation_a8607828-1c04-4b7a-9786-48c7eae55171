/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { Container } from 'inversify';
import { CreateHubUseCase } from '../../../application/use-cases/hub/CreateHubUseCase.js';
import { GetHubUseCase } from '../../../application/use-cases/hub/GetHubUseCase.js';
import { UpdateHubUseCase } from '../../../application/use-cases/hub/UpdateHubUseCase.js';
import { DeleteHubUseCase } from '../../../application/use-cases/hub/DeleteHubUseCase.js';
import { HubRepository } from '../../../infrastructure/database/repositories/HubRepository.js';
import { ClusterEventBus } from '../../../infrastructure/events/EventBus.js';
import { TYPES } from '../../../shared/types/TYPES.js';
import { Hub } from '../../../domain/entities/Hub.js';

describe('Hub System Integration', () => {
  let container: Container;
  let createHubUseCase: CreateHubUseCase;
  let _getHubUseCase: GetHubUseCase;
  let _updateHubUseCase: UpdateHubUseCase;
  let _deleteHubUseCase: DeleteHubUseCase;
  let _hubRepository: HubRepository;

  beforeEach(async () => {
    // Create a test container
    container = new Container();

    // Mock the Prisma client for testing
    const mockPrismaClient = {
      hub: {
        create: async () => ({ id: 'test-hub-id' }),
        findUnique: async () => null,
        findFirst: async () => null,
        findMany: async () => [],
        update: async () => ({ id: 'test-hub-id' }),
        delete: async () => ({ id: 'test-hub-id' }),
        count: async () => 0,
      },
    };

    // Set up the container with mocked dependencies
    container.bind(TYPES.PrismaClient).toConstantValue(mockPrismaClient);
    container.bind(TYPES.HubRepository).to(HubRepository).inSingletonScope();
    container.bind(TYPES.EventBus).to(ClusterEventBus).inSingletonScope();
    container.bind(TYPES.CreateHubUseCase).to(CreateHubUseCase).inSingletonScope();
    container.bind(TYPES.GetHubUseCase).to(GetHubUseCase).inSingletonScope();
    container.bind(TYPES.UpdateHubUseCase).to(UpdateHubUseCase).inSingletonScope();
    container.bind(TYPES.DeleteHubUseCase).to(DeleteHubUseCase).inSingletonScope();

    // Get the use cases
    createHubUseCase = container.get(TYPES.CreateHubUseCase);
    _getHubUseCase = container.get(TYPES.GetHubUseCase);
    _updateHubUseCase = container.get(TYPES.UpdateHubUseCase);
    _deleteHubUseCase = container.get(TYPES.DeleteHubUseCase);
    _hubRepository = container.get(TYPES.HubRepository);
  });

  describe('Hub Creation', () => {
    it('should create a hub successfully with valid data', async () => {
      const request = {
        name: 'Test Hub',
        description: 'A test hub for integration testing',
        ownerId: 'user123',
        iconUrl: 'https://example.com/icon.png',
      };

      const result = await createHubUseCase.execute(request);

      expect(result.success).toBe(true);
      expect(result.hub).toBeDefined();
      expect(result.hub?.name).toBe('Test Hub');
      expect(result.hub?.description).toBe('A test hub for integration testing');
      expect(result.hub?.ownerId).toBe('user123');
      expect(result.hub?.iconUrl).toBe('https://example.com/icon.png');
      expect(result.hub?.isPrivate).toBe(true); // Default behavior
    });

    it('should fail to create a hub with invalid name', async () => {
      const request = {
        name: 'x', // Too short
        description: 'A test hub for integration testing',
        ownerId: 'user123',
      };

      const result = await createHubUseCase.execute(request);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Hub name must be between 3 and 32 characters');
    });

    it('should fail to create a hub with invalid description', async () => {
      const request = {
        name: 'Test Hub',
        description: '', // Empty description
        ownerId: 'user123',
      };

      const result = await createHubUseCase.execute(request);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Hub description is required');
    });
  });

  describe('Hub Domain Logic', () => {
    it('should correctly identify hub owner', () => {
      const hub = Hub.create({
        name: 'Test Hub',
        description: 'Test description',
        ownerId: 'user123',
      });

      expect(hub.isOwner('user123')).toBe(true);
      expect(hub.isOwner('user456')).toBe(false);
    });

    it('should generate domain events on creation', () => {
      const hub = Hub.create({
        name: 'Test Hub',
        description: 'Test description',
        ownerId: 'user123',
      });

      const events = hub.getDomainEvents();
      expect(events).toHaveLength(1);
      expect(events[0].type).toBe('hub.created');
    });

    it('should update hub properties correctly', () => {
      const hub = Hub.create({
        name: 'Test Hub',
        description: 'Test description',
        ownerId: 'user123',
      });

      hub.clearDomainEvents(); // Clear creation event

      hub.update({
        description: 'Updated description',
        private: false,
      });

      expect(hub.description).toBe('Updated description');
      expect(hub.isPrivate).toBe(false);

      const events = hub.getDomainEvents();
      expect(events.length).toBeGreaterThan(0);
    });

    it('should validate hub visibility rules', () => {
      const hub = Hub.create({
        name: 'Test Hub',
        description: 'Short desc', // Less than 10 chars
        ownerId: 'user123',
      });

      // Hub cannot be made public without proper description and rules
      expect(hub.canBeMadePublic()).toBe(false);

      hub.update({ description: 'This is a longer description that meets requirements' });
      hub.updateRules(['Rule 1', 'Rule 2']);

      expect(hub.canBeMadePublic()).toBe(true);
    });
  });
});
