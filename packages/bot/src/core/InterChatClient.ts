/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { Client, GatewayIntentBits, Partials, ActivityType } from 'discord.js';
import { ClusterClient, getInfo } from 'discord-hybrid-sharding';
import { Container } from 'inversify';
import { initializeContainer } from '../infrastructure/di/Container.js';
import { TYPES } from '../shared/types/TYPES.js';
import { CommandRegistry } from '../presentation/commands/CommandRegistry.js';
import { CommandDeploymentService } from '../services/CommandDeploymentService.js';
// import { ClusterEventBus } from '../infrastructure/events/EventBus.js';
import { Logger } from '../shared/utils/Logger.js';

import { DynamicEventLoader } from '../infrastructure/loaders/DynamicEventLoader.js';
import type { IEventBus } from '../infrastructure/events/TestInterfaces.js';
import {
  ClientReadyEvent,
  ClientShutdownEvent,
  GuildJoinedEvent,
  GuildLeftEvent,
} from '../domain/events/ClientEvents.js';
import { SentryService } from '../infrastructure/observability/SentryService.js';

export class InterChatClient extends Client {
  public cluster: ClusterClient<InterChatClient>;
  private container?: Container;
  private eventBus?: IEventBus;

  constructor() {
    // Get shard information from the cluster manager
    const clusterInfo = getInfo();

    super({
      intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent,
        GatewayIntentBits.GuildMessageReactions,
        GatewayIntentBits.DirectMessages,
        GatewayIntentBits.GuildWebhooks,
        GatewayIntentBits.GuildIntegrations,
      ],
      partials: [
        Partials.Message,
        Partials.Channel,
        Partials.Reaction,
        Partials.User,
        Partials.GuildMember,
      ],
      // Use the specific shards assigned to this cluster
      shards: clusterInfo.SHARD_LIST,
      shardCount: clusterInfo.TOTAL_SHARDS,
      allowedMentions: {
        parse: ['users', 'roles'],
        repliedUser: false,
      },
      failIfNotExists: false,
    });

    // Initialize cluster client for hybrid sharding
    this.cluster = new ClusterClient(this);
  }

  /**
   * Start the client with full dependency injection
   */
  async start(): Promise<void> {
    try {
      // Initialize the DI container
      await this.initializeContainer();

      // Setup the command system
      await this.setupCommandSystem();

      // Deploy commands to Discord
      await this.deployCommands();

      // Setup event handlers
      await this.setupEventHandlers();

      // Setup Discord.js event listeners
      this.setupDiscordEvents();

      // Connect to Discord
      await this.login(process.env.DISCORD_TOKEN);

      Logger.info('✅ Client started successfully!');
    }
    catch (error) {
      Logger?.error('❌ Failed to start Client:', error);
      throw error;
    }
  }

  /**
   * Initialize the dependency injection container
   */
  private async initializeContainer(): Promise<void> {
    Logger.debug('🔧 Initializing DI container...');

    this.container = await initializeContainer(this.cluster?.id?.toString());

    // Bind the client instance to the container
    this.container.bind<InterChatClient>(TYPES.DiscordClient).toConstantValue(this);

    // Initialize Sentry for error tracking and performance monitoring
    const sentryService = this.container.get<SentryService>(TYPES.SentryService);
    sentryService.initialize();

    Logger.debug('✅ DI container initialized');
  }

  /**
   * Setup the command system (commands are already loaded during container initialization)
   */
  private async setupCommandSystem(): Promise<void> {
    Logger.debug('⚙️ Setting up command system...');

    // Commands are already loaded during container initialization in createClusterContainer()
    // Just get the registry to report the count
    const commandRegistry = this.container?.get<CommandRegistry>(TYPES.CommandRegistry);

    Logger.info(`✅ Command system ready with ${commandRegistry?.getAllCommands().length} commands`);
  }

  /**
   * Deploy commands to Discord with intelligent caching
   */
  private async deployCommands(): Promise<void> {
    Logger.debug('🚀 Deploying commands to Discord...');

    try {
      const deploymentService = this.container!.get<CommandDeploymentService>(
        TYPES.CommandDeploymentService,
      );
      const result = await deploymentService.deployCommands();

      if (!result.success) {
        Logger.error(
          `❌ Command deployment failed: ${result.reason}${result.error ? ` - ${result.error}` : ''}`,
        );
      }
    }
    catch (error) {
      Logger.error('❌ Unexpected error during command deployment:', error);
    }
  }

  /**
   * Setup event handling system with dynamic loading
   */
  private async setupEventHandlers(): Promise<void> {
    Logger.debug('📡 Setting up event handlers...');

    this.eventBus = this.container!.get<IEventBus>(TYPES.EventBus);

    // Load all event handlers dynamically
    const eventHandlers = await DynamicEventLoader.loadEventHandlers(this.container!);

    // Register each event handler with Discord.js
    for (const { eventName, handler, once } of eventHandlers) {
      if (once) {
        this.once(eventName, async (...args) => {
          try {
            await handler.execute(...args);
          }
          catch (error) {
            Logger.error(`Error in ${eventName} event handler:`, error);
          }
        });
      }
      else {
        this.on(eventName, async (...args) => {
          try {
            await handler.execute(...args);
          }
          catch (error) {
            Logger.error(`Error in ${eventName} event handler:`, error);
          }
        });
      }
    }

    Logger.info(`✅ Event handlers setup complete with ${eventHandlers.length} handlers`);
  }

  /**
   * Setup standard Discord.js event listeners
   */
  private setupDiscordEvents(): void {
    Logger.debug('🎭 Setting up Discord events...');

    this.once('ready', async () => {
      Logger.info(`🤖 Logged in as ${this.user!.tag} (Cluster ${this.cluster?.id})`);
      Logger.info(
        `📊 Serving ${this.guilds.cache.size} guilds with ${this.users.cache.size} users`,
      );

      // Set bot activity
      this.user!.setActivity('🌐 Connecting servers worldwide', {
        type: ActivityType.Custom,
      });

      // Emit ready event to event bus for other systems using domain events
      const readyEvent = new ClientReadyEvent(
        this.cluster?.id?.toString() || '0',
        this.guilds.cache.size,
        this.users.cache.size,
        this.cluster?.info?.SHARD_LIST || [],
      );
      await this.eventBus?.publish(readyEvent);
    });

    this.on('error', (error) => {
      Logger.error('Discord.js client error:', error);
    });

    this.on('warn', (warning) => {
      Logger.warn('Discord.js client warning:', warning);
    });

    this.on('shardError', (error, shardId) => {
      Logger.error(`Shard ${shardId} error:`, error);
    });

    this.on('shardReady', (shardId) => {
      Logger.info(`✅ Shard ${shardId} ready`);
    });

    this.on('shardDisconnect', (event, shardId) => {
      Logger.warn(`🔌 Shard ${shardId} disconnected:`, event);
    });

    this.on('shardReconnecting', (shardId) => {
      Logger.info(`🔄 Shard ${shardId} reconnecting...`);
    });

    // Guild events for statistics
    this.on('guildCreate', async (guild) => {
      Logger.info(`➕ Joined guild: ${guild.name} (${guild.id})`);

      const guildJoinedEvent = new GuildJoinedEvent(
        guild.id,
        guild.name,
        guild.memberCount || 0,
        this.cluster?.id?.toString() || '0',
      );
      await this.eventBus?.publish(guildJoinedEvent);
    });

    this.on('guildDelete', async (guild) => {
      Logger.info(`➖ Left guild: ${guild.name} (${guild.id})`);

      const guildLeftEvent = new GuildLeftEvent(
        guild.id,
        guild.name || 'Unknown',
        this.cluster?.id?.toString() || '0',
      );
      await this.eventBus?.publish(guildLeftEvent);
    });

    Logger.debug('✅ Discord events setup complete');
  }

  /**
   * Get the DI container instance
   */
  getContainer(): Container {
    if (!this.container) {
      throw new Error('Container not initialized. Call start() first.');
    }
    return this.container;
  }

  /**
   * Get the event bus instance
   */
  getEventBus(): IEventBus {
    if (!this.eventBus) {
      throw new Error('EventBus not initialized. Call start() first.');
    }
    return this.eventBus;
  }

  /**
   * Graceful shutdown
   */
  async shutdown(): Promise<void> {
    Logger.info('🛑 Shutting down Client...');

    try {
      // Emit shutdown domain event
      const shutdownEvent = new ClientShutdownEvent(
        this.cluster?.id?.toString() || '0',
        'Graceful shutdown',
      );
      await this.eventBus?.publish(shutdownEvent);

      // Flush Sentry events before shutdown
      try {
        const sentryService = this.container?.get<SentryService>(TYPES.SentryService);
        if (sentryService) {
          Logger.debug('🔍 Flushing Sentry events...');
          await sentryService.flush(5000);
          Logger.debug('✅ Sentry events flushed');
        }
      }
      catch (error) {
        Logger.debug('Sentry flush skipped:', error);
      }

      // Destroy the client connection
      this.destroy();

      Logger.info('✅ Client shutdown complete');
    }
    catch (error) {
      Logger.error('❌ Error during shutdown:', error);
      throw error;
    }
  }
}
