/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

/**
 * Request for getting server connections
 */
export interface GetConnectionsRequest {
  readonly serverId: string;
}

/**
 * Result of getting server connections
 */
export interface GetConnectionsResult {
  readonly success: boolean;
  readonly connections: ReadonlyArray<ConnectionDto>;
  readonly totalCount: number;
}

/**
 * Data transfer object for connection
 */
export interface ConnectionDto {
  readonly id: string;
  readonly channelId: string;
  readonly hubId: string;
  readonly hubName: string;
  readonly connected: boolean;
  readonly compact: boolean;
  readonly embedColor: string | null;
  readonly invite: string | null;
  readonly createdAt: Date;
  readonly lastActive: Date;
}

/**
 * Request for configuring a connection
 */
export interface ConfigureConnectionRequest {
  readonly channelId: string;
  readonly userId: string;
}

/**
 * Result of configuring a connection
 */
export interface ConfigureConnectionResult {
  readonly success: boolean;
  readonly message?: string;
}

/**
 * Request for managing user preferences
 */
export interface UpdatePreferencesRequest {
  readonly userId: string;
  readonly locale?: string;
  readonly compactMode?: boolean;
  readonly notifications?: boolean;
  readonly theme?: string;
}

/**
 * Result of updating user preferences
 */
export interface UpdatePreferencesResult {
  readonly success: boolean;
  readonly message?: string;
}

/**
 * User preferences data transfer object
 */
export interface UserPreferencesDto {
  readonly userId: string;
  readonly locale: string;
  readonly compactMode: boolean;
  readonly notifications: boolean;
  readonly theme: string;
  readonly updatedAt: Date;
}
