/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

/**
 * Application Events
 *
 * These are higher-level events that coordinate between multiple use cases
 * and handle cross-cutting concerns. They're different from Domain Events
 * which represent business state changes.
 */

/**
 * Event fired when a user completes registration
 * Triggers welcome message, setup wizards, etc.
 */
export interface UserRegisteredEvent {
  readonly userId: string;
  readonly guildId: string;
  readonly registeredAt: Date;
  readonly source: 'command' | 'auto' | 'invite';
}

/**
 * Event fired when a payment is successfully processed
 * Triggers premium activation, receipts, notifications
 */
export interface PaymentProcessedEvent {
  readonly paymentId: string;
  readonly userId: string;
  readonly amount: number;
  readonly currency: string;
  readonly tier: string;
  readonly processedAt: Date;
  readonly provider: 'kofi';
}

/**
 * Event fired when a hub reaches capacity
 * Triggers notifications to admins, auto-scaling, etc.
 */
export interface HubCapacityReachedEvent {
  readonly hubId: string;
  readonly currentUsers: number;
  readonly maxCapacity: number;
  readonly reachedAt: Date;
}

/**
 * Event fired when moderation action is taken
 * Triggers logging, notifications, appeals process
 */
export interface ModerationActionTakenEvent {
  readonly actionId: string;
  readonly targetUserId: string;
  readonly moderatorId: string;
  readonly action: 'warn' | 'mute' | 'kick' | 'ban';
  readonly reason: string;
  readonly hubId?: string;
  readonly guildId?: string;
  readonly actionTakenAt: Date;
}
