/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { injectable, inject } from 'inversify';
import { PrismaClient } from '../../../../../build/generated/prisma/client/index.js';
import { TYPES } from '../../shared/types/TYPES.js';
import { Logger } from '../../shared/utils/Logger.js';
import type {
  ServerRepository as IServerRepository,
  ServerConnection,
  ServerConnectionData,
} from '../../domain/repositories/ServerRepository.js';

/**
 * Server Repository Implementation
 *
 * Implements server connection operations using Prisma database client.
 * This repository manages the relationship between Discord servers and InterChat hubs.
 */
@injectable()
export class ServerRepository implements IServerRepository {
  constructor(
    @inject(TYPES.PrismaClient) private readonly prisma: PrismaClient,
  ) {}

  async getServerConnection(
    guildId: string,
    channelId: string,
  ): Promise<ServerConnection | null> {
    try {
      const connection = await this.prisma.connection.findFirst({
        where: {
          serverId: guildId,
          channelId,
          connected: true,
        },
        include: {
          hub: {
            select: {
              id: true,
              name: true,
              description: true,
              ownerId: true,
            },
          },
        },
      });

      if (!connection) {
        return null;
      }

      return {
        id: connection.id,
        guildId: connection.serverId,
        channelId: connection.channelId,
        hubId: connection.hubId,
        hubName: connection.hub.name,
        connectedBy: connection.hub.ownerId, // Using hub owner as fallback for connectedBy
        connectedAt: connection.createdAt,
      };
    }
    catch (error) {
      Logger.error('Error fetching server connection:', error);
      return null;
    }
  }

  async createServerConnection(data: ServerConnectionData): Promise<void> {
    try {
      await this.prisma.connection.create({
        data: {
          channelId: data.channelId,
          hubId: data.hubId,
          serverId: data.guildId,
          webhookURL: data.webhookURL || '', // Use provided webhook URL or empty string
          connected: true,
          compact: true,
          embedColor: null,
          parentId: null,
        },
      });

      Logger.info(`Created server connection: ${data.guildId}/${data.channelId} -> ${data.hubId} with webhook`);
    }
    catch (error) {
      Logger.error('Error creating server connection:', error);
      throw new Error('Failed to create server connection');
    }
  }

  async getServerConnections(guildId: string): Promise<ServerConnection[]> {
    try {
      const connections = await this.prisma.connection.findMany({
        where: {
          serverId: guildId,
          connected: true,
        },
        include: {
          hub: {
            select: {
              id: true,
              name: true,
              description: true,
              ownerId: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      return connections.map((connection) => ({
        id: connection.id,
        guildId: connection.serverId,
        channelId: connection.channelId,
        hubId: connection.hubId,
        hubName: connection.hub.name,
        connectedBy: connection.hub.ownerId, // Using hub owner as fallback for connectedBy
        connectedAt: connection.createdAt,
      }));
    }
    catch (error) {
      Logger.error('Error fetching server connections:', error);
      return [];
    }
  }

  async removeServerConnection(guildId: string, channelId: string): Promise<void> {
    try {
      const result = await this.prisma.connection.updateMany({
        where: {
          serverId: guildId,
          channelId,
        },
        data: {
          connected: false,
        },
      });

      if (result.count > 0) {
        Logger.info(`Disconnected server connection: ${guildId}/${channelId}`);
      }
      else {
        Logger.warn(`No connection found to disconnect: ${guildId}/${channelId}`);
      }
    }
    catch (error) {
      Logger.error('Error removing server connection:', error);
      throw new Error('Failed to remove server connection');
    }
  }
}
