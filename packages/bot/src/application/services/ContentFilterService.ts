/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { injectable, inject } from 'inversify';
import type { Message } from 'discord.js';
import { Logger } from '../../shared/utils/Logger.js';
import { TYPES } from '../../shared/types/TYPES.js';
import type { PrismaClient, BlockWordAction, BlockWord } from '../../../../../build/generated/prisma/client/index.js';

/**
 * Result of content filtering with enhanced anti-swear integration
 */
export interface ContentFilterResult {
  blocked: boolean;
  reason?: string;
  matchedTerm?: string;
  severity?: 'low' | 'medium' | 'high';
  filteredContent?: string; // For soft filtering (censored version)
  filterType?: 'soft' | 'hard';
  actions?: BlockWordAction[]; // Actions to take from anti-swear rules
  ruleId?: string; // ID of the triggered rule
  ruleName?: string; // Name of the triggered rule
  matches?: string[]; // All matched patterns
}

/**
 * Content filtering categories for built-in filters
 */
export enum FilterCategory {
  SPAM = 'spam',
  HARMFUL = 'harmful',
  ADVERTISEMENT = 'advertisement',
  INVITE_LINKS = 'invite_links',
  ATTACHMENT = 'attachment',
}

/**
 * Compiled anti-swear rule for efficient matching
 */
interface CompiledAntiSwearRule {
  id: string;
  hubId: string;
  name: string;
  actions: BlockWordAction[];
  exactMatches: string[];
  patternStrings: string[];
  regexPatterns: RegExp[];
}

/**
 * Modern Content Filter Service integrated with database-driven anti-swear system
 *
 * This service combines:
 * - Hub-specific anti-swear rules from the database (AntiSwearRule/AntiSwearPattern)
 * - Hub-specific block words (BlockWord)
 * - Built-in content filters for spam, harmful content, etc.
 * - Advanced pattern matching with caching
 */
@injectable()
export class ContentFilterService {
  private readonly logger = Logger;

  // Cache for compiled anti-swear rules per hub
  private readonly ruleCache = new Map<string, {
    rules: CompiledAntiSwearRule[];
    blockWords: BlockWord[];
    timestamp: number;
  }>();

  private readonly CACHE_TTL_MS = 5 * 60 * 1000; // 5 minutes
  private readonly MAX_REGEX_EXECUTION_TIME_MS = 50;

  constructor(
    @inject(TYPES.PrismaClient) private readonly prisma: PrismaClient,
  ) {
    // Clean up cache periodically
    setInterval(() => this.cleanupCache(), 10 * 60 * 1000);
  }

  /**
   * Check if a message contains prohibited content using hub-specific rules
   */
  async checkMessage(
    message: Message<true>,
    hubId: string,
    bypassFiltering = false,
  ): Promise<ContentFilterResult> {
    try {
      const content = message.content.trim();

      if (!content && message.attachments.size === 0) {
        return { blocked: false };
      }

      // Allow bypass for certain users (e.g., moderators)
      if (bypassFiltering) {
        return { blocked: false };
      }

      // 1. Check hub-specific anti-swear rules (highest priority)
      const antiSwearResult = await this.checkAntiSwearRules(content, hubId);
      if (antiSwearResult.blocked) {
        return antiSwearResult;
      }

      // 2. Check hub-specific block words
      const blockWordResult = await this.checkBlockWords(content, hubId);
      if (blockWordResult.blocked) {
        return blockWordResult;
      }

      // 3. Check built-in content filters
      const builtInResult = await this.checkBuiltInFilters(content, message);
      if (builtInResult.blocked) {
        return builtInResult;
      }

      return { blocked: false };
    }
    catch (error) {
      this.logger.error('Error in content filter:', error);
      return { blocked: false }; // Fail open for better UX
    }
  }

  /**
   * Check hub-specific anti-swear rules from the database
   */
  private async checkAntiSwearRules(content: string, hubId: string): Promise<ContentFilterResult> {
    try {
      const rules = await this.getAntiSwearRulesForHub(hubId);
      if (rules.length === 0) {
        return { blocked: false };
      }

      const normalizedContent = content.toLowerCase();
      const wordsInMessage = new Set<string>(
        (normalizedContent.match(/\b\w+\b/g) || [])
          .map((word) => word.replace(/[^\w]/g, ''))
          .filter(Boolean),
      );

      for (const rule of rules) {
        // Check exact matches first (fastest)
        for (const exactMatch of rule.exactMatches) {
          if (wordsInMessage.has(exactMatch)) {
            return {
              blocked: true,
              reason: `Violated anti-swear rule: ${rule.name}`,
              matchedTerm: exactMatch,
              severity: 'medium',
              actions: rule.actions,
              ruleId: rule.id,
              ruleName: rule.name,
              matches: [exactMatch],
            };
          }
        }

        // Check regex patterns
        for (const pattern of rule.regexPatterns) {
          try {
            const startTime = Date.now();
            const match = normalizedContent.match(pattern);
            const executionTime = Date.now() - startTime;

            if (executionTime > this.MAX_REGEX_EXECUTION_TIME_MS) {
              this.logger.warn(
                `Regex pattern took ${executionTime}ms to execute (over ${this.MAX_REGEX_EXECUTION_TIME_MS}ms limit)`,
              );
            }

            if (match) {
              const matches = match.map((m) => m?.trim()).filter(Boolean) as string[];
              if (matches.length > 0) {
                return {
                  blocked: true,
                  reason: `Violated anti-swear rule: ${rule.name}`,
                  matchedTerm: matches[0],
                  severity: 'medium',
                  actions: rule.actions,
                  ruleId: rule.id,
                  ruleName: rule.name,
                  matches,
                };
              }
            }
          }
          catch (error) {
            this.logger.error(`Error executing regex pattern for rule ${rule.name}:`, error);
          }
        }
      }

      return { blocked: false };
    }
    catch (error) {
      this.logger.error('Error checking anti-swear rules:', error);
      return { blocked: false };
    }
  }

  /**
   * Check hub-specific block words
   */
  private async checkBlockWords(content: string, hubId: string): Promise<ContentFilterResult> {
    try {
      const blockWords = await this.getBlockWordsForHub(hubId);
      if (blockWords.length === 0) {
        return { blocked: false };
      }

      const normalizedContent = content.toLowerCase();
      const wordsInMessage = new Set<string>(
        (normalizedContent.match(/\b\w+\b/g) || []).map((word) => word.toLowerCase()),
      );

      for (const blockWord of blockWords) {
        const words = blockWord.words.split(',').map((w) => w.trim().toLowerCase());

        for (const word of words) {
          if (wordsInMessage.has(word) || normalizedContent.includes(word)) {
            return {
              blocked: true,
              reason: `Blocked word: ${blockWord.name}`,
              matchedTerm: word,
              severity: 'medium',
              actions: blockWord.actions,
              ruleId: blockWord.id,
              ruleName: blockWord.name,
              matches: [word],
            };
          }
        }
      }

      return { blocked: false };
    }
    catch (error) {
      this.logger.error('Error checking block words:', error);
      return { blocked: false };
    }
  }

  /**
   * Check built-in content filters (spam, harmful, etc.)
   */
  private async checkBuiltInFilters(
    content: string,
    message: Message<true>,
  ): Promise<ContentFilterResult> {
    const normalizedContent = content.toLowerCase();

    // Check for Discord invite links
    const invitePattern = /(discord\.gg|discord\.com\/invite|discordapp\.com\/invite)\/[a-zA-Z0-9]+/gi;
    if (invitePattern.test(normalizedContent)) {
      return {
        blocked: true,
        reason: 'Discord invite links are not allowed',
        severity: 'medium',
        matchedTerm: normalizedContent.match(invitePattern)?.[0],
      };
    }

    // Check for spam patterns
    const spamPatterns = [
      /(.)\1{10,}/g, // Repeated characters
      /[A-Z]{15,}/g, // Excessive caps
    ];

    for (const pattern of spamPatterns) {
      const match = normalizedContent.match(pattern);
      if (match) {
        return {
          blocked: true,
          reason: 'Message appears to be spam',
          severity: 'medium',
          matchedTerm: match[0],
        };
      }
    }

    // Check attachments
    if (message.attachments.size > 0) {
      const attachmentResult = await this.checkAttachments(message);
      if (attachmentResult.blocked) {
        return attachmentResult;
      }
    }

    return { blocked: false };
  }

  /**
   * Check attachments for inappropriate content
   */
  private async checkAttachments(message: Message<true>): Promise<ContentFilterResult> {
    // Basic attachment validation
    for (const attachment of message.attachments.values()) {
      // Check file size (prevent massive files)
      if (attachment.size > 50 * 1024 * 1024) { // 50MB limit
        return {
          blocked: true,
          reason: 'File too large',
          severity: 'medium',
        };
      }

      // Check for suspicious file types
      const suspiciousExtensions = ['.exe', '.bat', '.scr', '.com', '.pif'];
      if (suspiciousExtensions.some((ext) => attachment.name?.toLowerCase().endsWith(ext))) {
        return {
          blocked: true,
          reason: 'Suspicious file type',
          severity: 'high',
        };
      }
    }

    return { blocked: false };
  }

  /**
   * Get severity level for a filter category
   */
  /**
   * Get user-friendly message for blocked content
   */
  getUserFriendlyMessage(result: ContentFilterResult): string {
    if (!result.blocked) {
      return '';
    }

    // If it's a hub-specific rule, provide specific feedback
    if (result.ruleName) {
      return `⚠️ Your message violates the "${result.ruleName}" rule and has been blocked.`;
    }

    switch (result.severity) {
      case 'high':
        return '🚨 Your message contains harmful content that violates our community guidelines.';
      case 'medium':
        return '⚠️ Your message was blocked due to inappropriate content.';
      case 'low':
        return '💬 Please rephrase your message to comply with our guidelines.';
      default:
        return '❌ Your message was blocked by our content filter.';
    }
  }

  /**
   * Get anti-swear rules for a hub with caching
   */
  private async getAntiSwearRulesForHub(hubId: string): Promise<CompiledAntiSwearRule[]> {
    const cacheKey = `antiswear_${hubId}`;
    const cached = this.ruleCache.get(cacheKey);

    if (cached && Date.now() - cached.timestamp < this.CACHE_TTL_MS) {
      return cached.rules;
    }

    const dbRules = await this.prisma.antiSwearRule.findMany({
      where: { hubId },
      include: { patterns: true },
    });

    const compiledRules: CompiledAntiSwearRule[] = dbRules.map((rule) => {
      const exactMatches: string[] = [];
      const patternStrings: string[] = [];
      const regexPatterns: RegExp[] = [];

      for (const pattern of rule.patterns) {
        const originalPattern = pattern.pattern.trim();
        if (!originalPattern) continue;

        if (!originalPattern.includes('*')) {
          // Exact match
          exactMatches.push(originalPattern.toLowerCase());
        }
        else {
          // Pattern with wildcards
          try {
            const escapedPattern = originalPattern.replace(/[-/\\^$+?.()|[\]{}]/g, '\\$&');
            const regexStr = escapedPattern.replace(/\*/g, '.*');
            const finalPattern = `(?<![\\w])${regexStr}(?![\\w])`;
            regexPatterns.push(new RegExp(finalPattern, 'i'));
            patternStrings.push(finalPattern);
          }
          catch (error) {
            this.logger.error(`Failed to compile pattern: ${originalPattern}`, error);
          }
        }
      }

      return {
        id: rule.id,
        hubId: rule.hubId,
        name: rule.name,
        actions: rule.actions,
        exactMatches,
        patternStrings,
        regexPatterns,
      };
    });

    // Cache the compiled rules
    this.ruleCache.set(cacheKey, {
      rules: compiledRules,
      blockWords: [],
      timestamp: Date.now(),
    });

    return compiledRules;
  }

  /**
   * Get block words for a hub
   */
  private async getBlockWordsForHub(hubId: string): Promise<BlockWord[]> {
    return await this.prisma.blockWord.findMany({
      where: { hubId },
    });
  }

  /**
   * Clean up expired cache entries
   */
  private cleanupCache(): void {
    const now = Date.now();
    let expiredCount = 0;

    for (const [key, value] of this.ruleCache.entries()) {
      if (now - value.timestamp > this.CACHE_TTL_MS) {
        this.ruleCache.delete(key);
        expiredCount++;
      }
    }

    if (expiredCount > 0) {
      this.logger.debug(`Cleaned up ${expiredCount} expired cache entries`);
    }
  }

  /**
   * Invalidate cache for a specific hub
   */
  async invalidateHubCache(hubId: string): Promise<void> {
    this.ruleCache.delete(`antiswear_${hubId}`);
    this.logger.debug(`Invalidated cache for hub ${hubId}`);
  }
}
