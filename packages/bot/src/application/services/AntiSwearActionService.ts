/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { injectable, inject } from 'inversify';
import type { Message } from 'discord.js';
import { Logger } from '../../shared/utils/Logger.js';
import { TYPES } from '../../shared/types/TYPES.js';
import { BlacklistService } from './BlacklistService.js';
import type { BlockWordAction } from '../../../../../build/generated/prisma/client/index.js';
import { SentryService } from '../../infrastructure/observability/SentryService.js';

/**
 * Result of executing an anti-swear action
 */
export interface ActionResult {
  success: boolean;
  shouldBlock: boolean;
  message?: string;
  error?: string;
}

/**
 * Service for executing anti-swear rule actions
 */
@injectable()
export class AntiSwearActionService {
  private readonly logger = Logger;

  constructor(
    @inject(TYPES.BlacklistService) private readonly blacklistService: BlacklistService,
    @inject(TYPES.SentryService) private readonly sentry: SentryService,
  ) {}

  /**
   * Execute all actions for a violated anti-swear rule
   */
  async executeActions(
    actions: BlockWordAction[],
    message: Message<true>,
    ruleId: string,
    hubId: string,
    ruleName: string,
    matches: string[],
  ): Promise<ActionResult[]> {
    // Set context for better error tracking
    this.sentry.setContext('anti-swear-action', {
      ruleId,
      hubId,
      ruleName,
      actionsCount: actions.length,
      userId: message.author.id,
      guildId: message.guild?.id,
    });

    const results: ActionResult[] = [];

    for (const action of actions) {
      const result = await this.sentry.startSpan(
        `anti-swear-action-${action}`,
        'anti-swear.action',
        async () => {
          try {
            return await this.executeAction(action, message, ruleId, hubId, ruleName, matches);
          }
          catch (error) {
            this.logger.error(`Failed to execute action ${action}:`, error);
            // Error is automatically captured by SentryService
            return {
              success: false,
              shouldBlock: false,
              error: error instanceof Error ? error.message : 'Unknown error',
            };
          }
        },
      );
      results.push(result);
    }

    return results;
  }

  /**
   * Execute a specific action
   */
  private async executeAction(
    action: BlockWordAction,
    message: Message<true>,
    ruleId: string,
    hubId: string,
    ruleName: string,
    matches: string[],
  ): Promise<ActionResult> {
    switch (action) {
      case 'BLOCK_MESSAGE':
        return this.handleBlockMessage(ruleName);

      case 'SEND_ALERT':
        return await this.handleSendAlert(message, ruleId, hubId, ruleName, matches);

      case 'BLACKLIST':
        return await this.handleBlacklist(message, hubId, ruleName);

      default:
        this.logger.warn(`Unknown action type: ${action}`);
        return {
          success: false,
          shouldBlock: false,
          error: `Unknown action type: ${action}`,
        };
    }
  }

  /**
   * Handle BLOCK_MESSAGE action
   */
  private handleBlockMessage(ruleName: string): ActionResult {
    return {
      success: true,
      shouldBlock: true,
      message: `Message blocked due to violating rule: ${ruleName}`,
    };
  }

  /**
   * Handle SEND_ALERT action
   * TODO: Implement hub moderation alert system
   */
  private async handleSendAlert(
    message: Message<true>,
    ruleId: string,
    hubId: string,
    ruleName: string,
    matches: string[],
  ): Promise<ActionResult> {
    try {
      // Log the alert for now - this would be replaced with actual alert system
      this.logger.info('Anti-swear alert generated', {
        ruleId,
        hubId,
        ruleName,
        userId: message.author.id,
        channelId: message.channel.id,
        guildId: message.guild.id,
        matches,
        messageContent: message.content.substring(0, 500), // Truncated for logging
      });

      // TODO: Implement actual hub moderation alert system
      // This would involve:
      // 1. Finding hub moderators
      // 2. Sending alerts to designated channels
      // 3. Creating moderation log entries

      return {
        success: true,
        shouldBlock: false,
        message: 'Alert sent to hub moderators',
      };
    }
    catch (error) {
      this.logger.error('Failed to send anti-swear alert:', error);
      return {
        success: false,
        shouldBlock: false,
        error: error instanceof Error ? error.message : 'Failed to send alert',
      };
    }
  }

  /**
   * Handle BLACKLIST action
   */
  private async handleBlacklist(
    message: Message<true>,
    hubId: string,
    ruleName: string,
  ): Promise<ActionResult> {
    try {
      const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes
      const reason = `Auto-blacklisted for using prohibited words (Rule: ${ruleName}).\n**Proof:** ${message.cleanContent}`;

      await this.blacklistService.addUserToBlacklist({
        userId: message.author.id,
        hubId,
        reason,
        expiresAt,
        moderatorId: message.client.user.id,
      });

      this.logger.info('User blacklisted for anti-swear violation', {
        userId: message.author.id,
        hubId,
        ruleName,
        reason,
        expiresAt,
      });

      return {
        success: true,
        shouldBlock: true,
        message: 'You have been temporarily blacklisted for using prohibited words.',
      };
    }
    catch (error) {
      this.logger.error('Failed to blacklist user for anti-swear violation:', error);
      return {
        success: false,
        shouldBlock: true,
        error: error instanceof Error ? error.message : 'Failed to blacklist user',
      };
    }
  }

  /**
   * Check if any of the actions should block the message
   */
  shouldBlockMessage(results: ActionResult[]): boolean {
    return results.some((result) => result.success && result.shouldBlock);
  }

  /**
   * Get user-friendly message from action results
   */
  getUserFriendlyMessage(results: ActionResult[]): string {
    const blockingResult = results.find((result) => result.success && result.shouldBlock);
    if (blockingResult && blockingResult.message) {
      return blockingResult.message;
    }

    const successfulResult = results.find((result) => result.success);
    if (successfulResult && successfulResult.message) {
      return successfulResult.message;
    }

    return 'Your message was blocked due to violating community guidelines.';
  }
}
