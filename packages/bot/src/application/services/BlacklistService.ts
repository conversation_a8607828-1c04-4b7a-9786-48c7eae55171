/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { injectable, inject } from 'inversify';
import { TYPES } from '../../shared/types/TYPES.js';
import { Logger } from '../../shared/utils/Logger.js';
import type { PrismaClient } from '../../../../../build/generated/prisma/client/index.js';

/**
 * Blacklist check result
 */
export interface BlacklistCheckResult {
  isBlacklisted: boolean;
  reason?: string;
  expiresAt?: Date;
  canAppeal?: boolean;
  appealCooldownEnds?: Date;
  severity?: 'warning' | 'temporary' | 'permanent';
}

/**
 * Appeal submission data
 */
export interface AppealData {
  userId: string;
  hubId: string;
  reason: string;
  appealText: string;
}

@injectable()
export class BlacklistService {
  constructor(@inject(TYPES.PrismaClient) private readonly prisma: PrismaClient) {}

  /**
   * Check if a user is blacklisted from a hub
   */
  async checkUserBlacklist(userId: string, hubId: string): Promise<BlacklistCheckResult> {
    try {
      const infraction = await this.prisma.infraction.findFirst({
        where: {
          userId,
          hubId,
          status: 'ACTIVE',
          type: 'BLACKLIST',
        },
        include: {
          appeals: true,
        },
      });

      if (!infraction) {
        return { isBlacklisted: false };
      }

      // Check if blacklist has expired
      if (infraction.expiresAt && infraction.expiresAt <= new Date()) {
        await this.expireBlacklist(infraction.id);
        return { isBlacklisted: false };
      }

      const severity = this.getSeverity(infraction.expiresAt);
      const canAppeal = await this.canUserAppeal(userId, hubId);
      const appealCooldownEnds = await this.getAppealCooldownEnd(userId, hubId) || undefined;

      return {
        isBlacklisted: true,
        reason: infraction.reason,
        expiresAt: infraction.expiresAt || undefined,
        canAppeal,
        appealCooldownEnds,
        severity,
      };
    }
    catch (error) {
      Logger.error('Error checking user blacklist:', error);
      return { isBlacklisted: false }; // Fail open for better UX
    }
  }

  /**
   * Check if a server is blacklisted from a hub
   */
  async checkServerBlacklist(serverId: string, hubId: string): Promise<BlacklistCheckResult> {
    try {
      const infraction = await this.prisma.infraction.findFirst({
        where: {
          serverId,
          hubId,
          status: 'ACTIVE',
          type: 'BLACKLIST',
        },
      });

      if (!infraction) {
        return { isBlacklisted: false };
      }

      // Check if blacklist has expired
      if (infraction.expiresAt && infraction.expiresAt <= new Date()) {
        await this.expireBlacklist(infraction.id);
        return { isBlacklisted: false };
      }

      return {
        isBlacklisted: true,
        reason: infraction.reason,
        expiresAt: infraction.expiresAt || undefined,
        severity: this.getSeverity(infraction.expiresAt),
        canAppeal: false, // Servers typically can't appeal through automated system
      };
    }
    catch (error) {
      Logger.error('Error checking server blacklist:', error);
      return { isBlacklisted: false };
    }
  }

  /**
   * Add a user to blacklist
   */
  async addUserToBlacklist(data: {
    userId: string;
    hubId: string;
    reason: string;
    expiresAt?: Date;
    moderatorId: string;
  }): Promise<{ success: boolean; message: string }> {
    try {
      // Check if user is already blacklisted
      const existingBlacklist = await this.checkUserBlacklist(data.userId, data.hubId);
      if (existingBlacklist.isBlacklisted) {
        return {
          success: false,
          message: 'User is already blacklisted from this hub',
        };
      }

      // Create the blacklist infraction
      await this.prisma.infraction.create({
        data: {
          userId: data.userId,
          hubId: data.hubId,
          moderatorId: data.moderatorId,
          type: 'BLACKLIST',
          reason: data.reason,
          expiresAt: data.expiresAt,
          status: 'ACTIVE',
        },
      });

      Logger.info('User blacklisted', {
        userId: data.userId,
        hubId: data.hubId,
        moderatorId: data.moderatorId,
        reason: data.reason,
        expiresAt: data.expiresAt,
      });

      return {
        success: true,
        message: data.expiresAt
          ? `User blacklisted temporarily until ${data.expiresAt.toLocaleDateString()}`
          : 'User blacklisted permanently',
      };
    }
    catch (error) {
      Logger.error('Error adding user to blacklist:', error);
      return {
        success: false,
        message: 'Failed to blacklist user due to internal error',
      };
    }
  }

  /**
   * Submit an appeal for a blacklist
   */
  async submitAppeal(appealData: AppealData): Promise<{ success: boolean; message: string }> {
    try {
      const { userId, hubId } = appealData;

      // Check if user can appeal
      const canAppeal = await this.canUserAppeal(userId, hubId);
      if (!canAppeal) {
        const cooldownEnd = await this.getAppealCooldownEnd(userId, hubId);
        const timeLeft = cooldownEnd
          ? Math.ceil((cooldownEnd.getTime() - Date.now()) / (1000 * 60 * 60))
          : 0;
        return {
          success: false,
          message: `You must wait ${timeLeft} more hours before submitting another appeal.`,
        };
      }

      // Find the active blacklist
      const infraction = await this.prisma.infraction.findFirst({
        where: {
          userId,
          hubId,
          status: 'ACTIVE',
          type: 'BLACKLIST',
        },
      });

      if (!infraction) {
        return {
          success: false,
          message: 'No active blacklist found.',
        };
      }

      // Create the appeal
      await this.prisma.appeal.create({
        data: {
          infractionId: infraction.id,
          userId: appealData.userId,
          reason: appealData.appealText,
          status: 'PENDING',
        },
      });

      return {
        success: true,
        message:
          '✅ Your appeal has been submitted successfully! Our moderators will review it within 24-48 hours.',
      };
    }
    catch (error) {
      Logger.error('Error submitting appeal:', error);
      return {
        success: false,
        message: 'Failed to submit appeal. Please try again later.',
      };
    }
  }

  /**
   * Check if a user can submit an appeal
   */
  private async canUserAppeal(userId: string, hubId: string): Promise<boolean> {
    try {
      const recentAppeal = await this.prisma.appeal.findFirst({
        where: {
          infraction: {
            userId,
            hubId,
          },
          createdAt: {
            gte: new Date(Date.now() - 72 * 60 * 60 * 1000), // 72 hours ago
          },
        },
      });

      return !recentAppeal;
    }
    catch (error) {
      Logger.error('Error checking appeal eligibility:', error);
      return false;
    }
  }

  /**
   * Get when the appeal cooldown ends
   */
  private async getAppealCooldownEnd(userId: string, hubId: string): Promise<Date | null> {
    try {
      const recentAppeal = await this.prisma.appeal.findFirst({
        where: {
          infraction: {
            userId,
            hubId,
          },
          createdAt: {
            gte: new Date(Date.now() - 72 * 60 * 60 * 1000), // 72 hours ago
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      if (!recentAppeal) {
        return null;
      }

      return new Date(recentAppeal.createdAt.getTime() + 72 * 60 * 60 * 1000);
    }
    catch (error) {
      Logger.error('Error getting appeal cooldown:', error);
      return null;
    }
  }

  /**
   * Expire a blacklist
   */
  private async expireBlacklist(infractionId: string): Promise<void> {
    try {
      await this.prisma.infraction.update({
        where: { id: infractionId },
        data: { status: 'REVOKED' },
      });
      Logger.info(`Blacklist ${infractionId} expired automatically`);
    }
    catch (error) {
      Logger.error('Error expiring blacklist:', error);
    }
  }

  /**
   * Get severity level based on expiration
   */
  private getSeverity(expiresAt: Date | null): 'warning' | 'temporary' | 'permanent' {
    if (!expiresAt) {
      return 'permanent';
    }

    const now = new Date();
    const timeLeft = expiresAt.getTime() - now.getTime();
    const hoursLeft = timeLeft / (1000 * 60 * 60);

    if (hoursLeft <= 24) {
      return 'warning';
    }

    return 'temporary';
  }

  /**
   * Get user-friendly message for blacklist status
   */
  getUserFriendlyMessage(result: BlacklistCheckResult): string {
    if (!result.isBlacklisted) {
      return '';
    }

    const baseMessage = `🚫 You are currently blacklisted from this hub.\n**Reason:** ${result.reason}`;

    if (result.expiresAt) {
      const timeLeft = Math.ceil((result.expiresAt.getTime() - Date.now()) / (1000 * 60 * 60));
      return `${baseMessage}\n⏰ **Expires in:** ${timeLeft} hours`;
    }

    if (result.canAppeal) {
      return `${baseMessage}\n📝 **Good news:** You can submit an appeal! Use the appeal command to request a review.`;
    }

    if (result.appealCooldownEnds) {
      const hoursLeft = Math.ceil(
        (result.appealCooldownEnds.getTime() - Date.now()) / (1000 * 60 * 60),
      );
      return `${baseMessage}\n⏳ **Appeal cooldown:** You can appeal again in ${hoursLeft} hours.`;
    }

    return `${baseMessage}\n📞 **Contact:** Reach out to hub moderators if you believe this is an error.`;
  }
}
