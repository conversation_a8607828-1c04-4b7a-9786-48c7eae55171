/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import {
  PermissionFlagsBits,
  type TextChannel,
  type Webhook,
} from 'discord.js';
import { injectable } from 'inversify';
import { Logger } from '../../shared/utils/Logger.js';

export interface WebhookCreationResult {
  success: boolean;
  webhookURL?: string;
  error?: string;
}

/**
 * Service for managing Discord webhooks for hub connections
 */
@injectable()
export class WebhookService {
  /**
   * Create or reuse an existing webhook for a channel
   */
  async getOrCreateWebhook(channel: TextChannel): Promise<WebhookCreationResult> {
    try {
      Logger.debug(`Getting/creating webhook for channel ${channel.id}`);

      // Check if we have permission to manage webhooks
      if (!channel.guild.members.me?.permissions.has(PermissionFlagsBits.ManageWebhooks)) {
        return {
          success: false,
          error: 'Bot lacks permission to manage webhooks in this server',
        };
      }

      // Try to find an existing webhook owned by the bot
      const existingWebhook = await this.findBotWebhook(channel);
      if (existingWebhook) {
        Logger.debug(`Reusing existing webhook: ${existingWebhook.id}`);
        return {
          success: true,
          webhookURL: existingWebhook.url,
        };
      }

      // Create a new webhook
      const newWebhook = await this.createNewWebhook(channel);
      if (!newWebhook) {
        return {
          success: false,
          error: 'Failed to create webhook',
        };
      }

      Logger.info(`Created new webhook for channel ${channel.id}: ${newWebhook.id}`);
      return {
        success: true,
        webhookURL: newWebhook.url,
      };
    }
    catch (error) {
      Logger.error('Error getting/creating webhook:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Find an existing webhook owned by the bot
   */
  private async findBotWebhook(channel: TextChannel): Promise<Webhook | null> {
    try {
      const webhooks = await channel.fetchWebhooks();

      // Look for a webhook owned by the bot
      const botWebhook = webhooks.find(
        (webhook) => webhook.owner?.id === channel.client.user?.id,
      );

      return botWebhook || null;
    }
    catch (error) {
      Logger.debug('Error fetching webhooks:', error);
      return null;
    }
  }

  /**
   * Create a new webhook for the channel
   */
  private async createNewWebhook(channel: TextChannel): Promise<Webhook | null> {
    try {
      const webhook = await channel.createWebhook({
        name: 'InterChat Hub',
        avatar: channel.client.user?.displayAvatarURL(),
        reason: 'InterChat hub connection setup',
      });

      return webhook;
    }
    catch (error) {
      Logger.error('Error creating webhook:', error);
      return null;
    }
  }

  /**
   * Validate webhook URL and check if it's accessible
   */
  async validateWebhookURL(webhookURL: string): Promise<boolean> {
    try {
      // Basic URL validation
      const url = new URL(webhookURL);
      if (!url.hostname.includes('discord')) {
        return false;
      }

      // Try to fetch webhook info (this doesn't require special permissions)
      const response = await fetch(webhookURL);
      return response.ok;
    }
    catch (error) {
      Logger.debug('Webhook validation failed:', error);
      return false;
    }
  }

  /**
   * Clean up unused webhooks (for maintenance)
   */
  async cleanupUnusedWebhooks(channel: TextChannel): Promise<number> {
    try {
      const webhooks = await channel.fetchWebhooks();
      const deletedCount = 0;

      for (const webhook of webhooks.values()) {
        // Only delete webhooks owned by the bot that are not in use
        if (webhook.owner?.id === channel.client.user?.id) {
          // TODO: Check if webhook is actually in use in the database
          // For now, we'll skip deletion to be safe
          Logger.warn(`Found bot webhook: ${webhook.id} (skipping deletion for safety)`);
        }
      }

      return deletedCount;
    }
    catch (error) {
      Logger.error('Error cleaning up webhooks:', error);
      return 0;
    }
  }
}
