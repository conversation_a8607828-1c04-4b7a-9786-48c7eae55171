/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { injectable } from 'inversify';
import type {
  MessageService as IMessageService,
  OriginalMessage,
  MessageConnection,
} from '../../domain/services/MessageService.js';

/**
 * Message Service Adapter
 *
 * This is a temporary adapter that implements the legacy MessageService interface.
 * Since the message system is complex and involves cross-server messaging,
 * this provides placeholder implementations until proper use cases are created.
 *
 * TODO: Implement proper message use cases:
 * - FindOriginalMessageUseCase
 * - GetMessageConnectionsUseCase
 * - EditMessageUseCase
 * - DeleteMessageUseCase
 * - ReportMessageUseCase
 */
@injectable()
export class MessageService implements IMessageService {
  async findOriginalMessage(_messageId: string): Promise<OriginalMessage | null> {
    // TODO: Implement with proper MessageRepository or use case
    // For now, return null to prevent command handlers from crashing
    return null;
  }

  async getMessageConnections(_messageId: string): Promise<MessageConnection[]> {
    // TODO: Implement with proper MessageRepository or use case
    // For now, return empty array to prevent command handlers from crashing
    return [];
  }
}
