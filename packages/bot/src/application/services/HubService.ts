/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { injectable, inject } from 'inversify';
import { TYPES } from '../../shared/types/TYPES.js';
import type {
  HubService as IHubService,
  Hub,
  HubCreationData,
} from '../../domain/services/HubService.js';
import type { GetHubUseCase } from '../use-cases/hub/GetHubUseCase.js';
import type { CreateHubUseCase } from '../use-cases/hub/CreateHubUseCase.js';

/**
 * Hub Service Adapter
 *
 * This adapter implements the legacy HubService interface by delegating
 * to proper use cases. This allows command handlers to work while following
 * the modern Clean Architecture pattern.
 *
 * TODO: Migrate command handlers to use use cases directly
 */
@injectable()
export class HubService implements IHubService {
  constructor(
    @inject(TYPES.GetHubUseCase) private readonly getHubUseCase: GetHubUseCase,
    @inject(TYPES.CreateHubUseCase) private readonly createHubUseCase: CreateHubUseCase,
  ) {}

  async getHub(hubId: string): Promise<Hub | null> {
    const result = await this.getHubUseCase.execute({ hubId });

    if (!result.success || !result.hub) {
      return null;
    }

    return {
      id: result.hub.id,
      name: result.hub.name,
      description: result.hub.description || undefined,
      ownerId: result.hub.ownerId,
      private: result.hub.isPrivate,
    };
  }

  async findHubByName(name: string): Promise<Hub | null> {
    try {
      const result = await this.getHubUseCase.execute({ hubName: name });

      if (!result.success || !result.hub) {
        return null;
      }

      return {
        id: result.hub.id,
        name: result.hub.name,
        description: result.hub.description || undefined,
        ownerId: result.hub.ownerId,
        private: result.hub.isPrivate,
      };
    }
    catch {
      return null;
    }
  }

  async createHub(data: HubCreationData): Promise<Hub> {
    const result = await this.createHubUseCase.execute({
      name: data.name,
      description: data.description || '',
      ownerId: data.ownerId,
    });

    if (!result.success || !result.hub) {
      throw new Error(result.error || 'Failed to create hub');
    }

    return {
      id: result.hub.id,
      name: result.hub.name,
      description: result.hub.description || undefined,
      ownerId: result.hub.ownerId,
      private: result.hub.isPrivate,
    };
  }
}
