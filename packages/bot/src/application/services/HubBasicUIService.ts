/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { inject, injectable } from 'inversify';
import {
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
  ContainerBuilder,
  ModalBuilder,
  SectionBuilder,
  SeparatorSpacingSize,
  TextDisplayBuilder,
  TextInputBuilder,
  TextInputStyle,
} from 'discord.js';
import { stripIndents } from 'common-tags';
import { TYPES } from '../../shared/types/TYPES.js';
import { CustomID } from '../../shared/utils/CustomID.js';
import type { IHubRepository } from '../../domain/repositories/HubRepositories.js';

@injectable()
export class HubBasicUIService {
  constructor(
    @inject(TYPES.HubRepository)
    private readonly hubRepository: IHubRepository,
  ) {}

  async buildBasicSettingsContainer(hubId: string): Promise<ContainerBuilder> {
    const hub = await this.hubRepository.findById(hubId);
    if (!hub) {
      throw new Error(`Hub with ID ${hubId} not found`);
    }

    const container = new ContainerBuilder();

    container.addTextDisplayComponents(
      new TextDisplayBuilder().setContent('## ⚙️ Hub Settings Configuration'),
    );

    container.addSeparatorComponents((separator) =>
      separator.setSpacing(SeparatorSpacingSize.Large),
    );

    // Current hub information
    container.addTextDisplayComponents(
      new TextDisplayBuilder().setContent(
        stripIndents`
          ### 📊 Current Hub Information
          **Name:** ${hub.name}
          **Description:** ${hub.description}
          **Privacy:** ${hub.isPrivate ? '🔒 Private' : '🌐 Public'}
          **NSFW:** ${hub.isNsfw ? '🔞 Yes' : '✅ No'}
          **Status:** ${hub.isLocked ? '🔒 Locked' : '🟢 Active'}
          **Appeal Cooldown:** ${hub.appealCooldownHours || 24} hours
        `,
      ),
    );

    container.addSeparatorComponents((separator) =>
      separator.setSpacing(SeparatorSpacingSize.Large),
    );

    // Configuration sections
    const configSections = [
      {
        title: '📝 Description',
        description: 'Update the hub description',
        action: 'description',
        emoji: '✏️',
      },
      {
        title: '🔒 Privacy Settings',
        description: 'Toggle between private and public hub',
        action: 'privacy',
        emoji: hub.isPrivate ? '🌐' : '🔒',
      },
      {
        title: '🔞 NSFW Settings',
        description: 'Toggle NSFW content allowance',
        action: 'nsfw',
        emoji: hub.isNsfw ? '✅' : '🔞',
      },
      {
        title: '⏰ Appeal Cooldown',
        description: 'Set appeal cooldown duration',
        action: 'cooldown',
        emoji: '⏱️',
      },
      {
        title: '🔒 Hub Status',
        description: 'Lock or unlock the hub',
        action: 'lock',
        emoji: hub.isLocked ? '🔓' : '🔒',
      },
    ];

    for (const section of configSections) {
      const sectionBuilder = new SectionBuilder()
        .addTextDisplayComponents(
          new TextDisplayBuilder().setContent(
            `### ${section.title}\n${section.description}`,
          ),
        )
        .setButtonAccessory(
          new ButtonBuilder()
            .setCustomId(new CustomID('hub_basic:config', [hubId, section.action]).toString())
            .setLabel('Configure')
            .setStyle(ButtonStyle.Secondary)
            .setEmoji(section.emoji),
        );

      container.addSectionComponents(sectionBuilder);
    }

    container.addSeparatorComponents((separator) =>
      separator.setSpacing(SeparatorSpacingSize.Large),
    );

    container.addActionRowComponents((row) =>
      row.addComponents(
        new ButtonBuilder()
          .setCustomId(new CustomID('hubEdit:home', [hubId]).toString())
          .setLabel('Back to Hub')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('↩️'),
        new ButtonBuilder()
          .setCustomId(new CustomID('hub_basic:refresh', [hubId]).toString())
          .setLabel('Refresh')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('🔄'),
      ),
    );

    return container;
  }

  buildDescriptionModal(hubId: string, currentDescription: string): ModalBuilder {
    return new ModalBuilder()
      .setCustomId(new CustomID('hub_basic:description_modal', [hubId]).toString())
      .setTitle('Update Hub Description')
      .addComponents(
        new ActionRowBuilder<TextInputBuilder>().addComponents(
          new TextInputBuilder()
            .setCustomId('description')
            .setLabel('Hub Description')
            .setStyle(TextInputStyle.Paragraph)
            .setPlaceholder('Enter the new hub description...')
            .setMaxLength(1000)
            .setValue(currentDescription)
            .setRequired(true),
        ),
      );
  }

  buildAppealCooldownModal(hubId: string, currentCooldown: number): ModalBuilder {
    return new ModalBuilder()
      .setCustomId(new CustomID('hub_basic:cooldown_modal', [hubId]).toString())
      .setTitle('Set Appeal Cooldown')
      .addComponents(
        new ActionRowBuilder<TextInputBuilder>().addComponents(
          new TextInputBuilder()
            .setCustomId('cooldown')
            .setLabel('Appeal Cooldown (hours)')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Enter cooldown in hours (1-8766)')
            .setMaxLength(10)
            .setValue(currentCooldown.toString())
            .setRequired(true),
        ),
      );
  }
}
