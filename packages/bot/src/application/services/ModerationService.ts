/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { injectable } from 'inversify';
import type {
  ModerationService as IModerationService,
  ModerationResult,
  EditResult,
  Report,
  ReportData,
} from '../../domain/services/ModerationService.js';
import type { OriginalMessage } from '../../domain/services/MessageService.js';
import type { Hub } from '../../domain/services/HubService.js';

/**
 * Moderation Service Adapter
 *
 * This is a temporary adapter that implements the legacy ModerationService interface.
 * The moderation functionality involves complex cross-server operations and should
 * be refactored into proper use cases with proper domain logic.
 *
 * TODO: Implement proper use cases:
 * - DeleteMessageUseCase
 * - EditMessageUseCase
 * - ReportMessageUseCase
 * - CheckModerationPermissionsUseCase
 * - LogModerationActionUseCase
 */
@injectable()
export class ModerationService implements IModerationService {
  async canUserDeleteMessage(
    _userId: string,
    _message: OriginalMessage,
    _hub: Hub,
  ): Promise<boolean> {
    // TODO: Implement proper permission checking
    // For now, return false to prevent unauthorized deletions
    return false;
  }

  async isDeleteInProgress(_messageId: string): Promise<boolean> {
    // TODO: Implement deletion tracking
    // For now, return false to prevent command handlers from crashing
    return false;
  }

  async deleteMessageFromHub(
    _hubId: string,
    _messageId: string,
    _userId: string,
  ): Promise<ModerationResult> {
    // TODO: Implement actual message deletion across all connected servers
    // For now, return zero counts to prevent command handlers from crashing
    return {
      deletedCount: 0,
      totalCount: 0,
    };
  }

  async logMessageDeletion(
    _message: OriginalMessage,
    _hub: Hub,
    _userId: string,
    _username: string,
  ): Promise<void> {
    // TODO: Implement moderation logging
    // For now, do nothing to prevent command handlers from crashing
  }

  async editMessageInHub(
    _hubId: string,
    _messageId: string,
    _content: string,
    _userId: string,
  ): Promise<EditResult> {
    // TODO: Implement actual message editing across all connected servers
    // For now, return zero counts to prevent command handlers from crashing
    return {
      editedCount: 0,
      totalCount: 0,
    };
  }

  async logMessageEdit(
    _message: OriginalMessage,
    _hub: Hub,
    _userId: string,
    _username: string,
    _newContent: string,
  ): Promise<void> {
    // TODO: Implement moderation logging for edits
    // For now, do nothing to prevent command handlers from crashing
  }

  async getRecentReports(_userId: string, _messageId: string): Promise<Report[]> {
    // TODO: Implement report retrieval from database
    // For now, return empty array to prevent command handlers from crashing
    return [];
  }

  async createReport(_data: ReportData): Promise<Report> {
    // TODO: Implement actual report creation in database
    // For now, return a mock report to prevent command handlers from crashing
    return {
      id: 'mock-report-id',
      messageId: _data.messageId,
      hubId: _data.hubId,
      reporterId: _data.reporterId,
      reportedUserId: _data.reportedUserId,
      reason: _data.reason,
      details: _data.details,
      guildId: _data.guildId,
      channelId: _data.channelId,
    };
  }

  async notifyHubModerators(_hub: Hub, _report: Report): Promise<void> {
    // TODO: Implement moderator notification system
    // For now, do nothing to prevent command handlers from crashing
  }
}
