/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { injectable, inject } from 'inversify';
import { TYPES } from '../../shared/types/TYPES.js';
import type {
  UserService as IUserService,
  User,
  InboxMessage,
  DailyReward,
  Achievement,
  AchievementReward,
  ClaimableRewards,
} from '../../domain/services/UserService.js';
import type { GetUserUseCase } from '../use-cases/users/GetUserUseCase.js';

/**
 * User Service Adapter
 *
 * This is a temporary adapter that implements the legacy UserService interface
 * by delegating to proper use cases. This allows the system to work while
 * gradually migrating away from the UserService pattern to specific use cases.
 *
 * TODO: Replace with specific use cases:
 * - GetUserUseCase (already exists)
 * - GetInboxMessagesUseCase
 * - MarkInboxMessageAsReadUseCase
 * - ClearInboxMessagesUseCase
 * - ClaimDailyRewardUseCase
 * - etc.
 */
@injectable()
export class UserService implements IUserService {
  constructor(
    @inject(TYPES.GetUserUseCase) private readonly getUserUseCase: GetUserUseCase,
  ) {}

  async getUser(userId: string): Promise<User | null> {
    try {
      const result = await this.getUserUseCase.execute({ userId });
      if (!result) return null;

      return {
        id: result.userId,
        image: result.image || undefined,
      };
    }
    catch {
      return null;
    }
  }

  // Inbox-related methods - these need proper use cases
  async getInboxMessages(_userId: string, _unreadOnly: boolean): Promise<InboxMessage[]> {
    // TODO: Implement GetInboxMessagesUseCase
    return [];
  }

  async getInboxMessage(_userId: string, _messageId: string): Promise<InboxMessage | null> {
    // TODO: Implement GetInboxMessageUseCase
    return null;
  }

  async markInboxMessageAsRead(_userId: string, _messageId: string): Promise<void> {
    // TODO: Implement MarkInboxMessageAsReadUseCase
  }

  async clearReadInboxMessages(_userId: string): Promise<number> {
    // TODO: Implement ClearInboxMessagesUseCase
    return 0;
  }

  // Achievement/reward methods - these need proper use cases
  async canClaimDailyReward(_userId: string): Promise<boolean> {
    // TODO: Implement CanClaimDailyRewardUseCase
    return false;
  }

  async getNextDailyClaimTime(_userId: string): Promise<Date> {
    // TODO: Implement GetNextDailyClaimTimeUseCase
    return new Date(Date.now() + 24 * 60 * 60 * 1000);
  }

  async claimDailyReward(_userId: string): Promise<DailyReward> {
    // TODO: Implement ClaimDailyRewardUseCase
    return {
      amount: 0,
      currency: 'coins',
      streak: 0,
    };
  }

  async getUnclaimedAchievement(
    _userId: string,
    _achievementId: string,
  ): Promise<Achievement | null> {
    // TODO: Implement GetUnclaimedAchievementUseCase
    return null;
  }

  async claimAchievement(_userId: string, _achievementId: string): Promise<AchievementReward> {
    // TODO: Implement ClaimAchievementUseCase
    return {
      amount: 0,
      currency: 'coins',
    };
  }

  async getClaimableRewards(_userId: string): Promise<ClaimableRewards> {
    // TODO: Implement GetClaimableRewardsUseCase
    return {
      canClaimDaily: false,
      nextDailyClaimTime: new Date(Date.now() + 24 * 60 * 60 * 1000),
      unclaimedAchievements: [],
      isPremium: false,
    };
  }
}
