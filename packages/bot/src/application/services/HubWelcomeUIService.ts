/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { inject, injectable } from 'inversify';
import {
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
  ContainerBuilder,
  ModalBuilder,
  SeparatorSpacingSize,
  TextDisplayBuilder,
  TextInputBuilder,
  TextInputStyle,
} from 'discord.js';
import { stripIndents } from 'common-tags';
import { TYPES } from '../../shared/types/TYPES.js';
import { CustomID } from '../../shared/utils/CustomID.js';
import type { IHubRepository } from '../../domain/repositories/HubRepositories.js';

@injectable()
export class HubWelcomeUIService {
  constructor(
    @inject(TYPES.HubRepository)
    private readonly hubRepository: IHubRepository,
  ) {}

  async buildWelcomeContainer(hubId: string): Promise<ContainerBuilder> {
    const hub = await this.hubRepository.findById(hubId);
    if (!hub) {
      throw new Error(`Hub with ID ${hubId} not found`);
    }

    const container = new ContainerBuilder();

    container.addTextDisplayComponents(
      new TextDisplayBuilder().setContent('## 👋 Welcome Message Configuration'),
    );

    container.addSeparatorComponents((separator) =>
      separator.setSpacing(SeparatorSpacingSize.Large),
    );

    container.addTextDisplayComponents(
      new TextDisplayBuilder().setContent(
        stripIndents`
          ### 🔍 Current Status
          Configure a welcome message that will be shown to new servers joining your hub.

          **Status:** ${hub.welcomeMessage ? '✅ Configured' : '❌ Not configured'}
          ${hub.welcomeMessage ? `**Current Message:**\n\`\`\`\n${hub.welcomeMessage}\n\`\`\`` : ''}

          ### 💡 Tips
          - Welcome messages are shown when new servers join your hub
          - Keep it friendly and informative
          - You can use basic formatting like **bold** and *italic*
          - Maximum length is 1000 characters
          - This feature requires voting support (Ko-fi supporters)
        `,
      ),
    );

    container.addSeparatorComponents((separator) =>
      separator.setSpacing(SeparatorSpacingSize.Large),
    );

    container.addActionRowComponents((row) =>
      row.addComponents(
        new ButtonBuilder()
          .setCustomId(new CustomID('hub_welcome:set', [hubId]).toString())
          .setLabel(hub.welcomeMessage ? 'Edit Message' : 'Set Message')
          .setStyle(ButtonStyle.Primary)
          .setEmoji('✏️'),
        ...(hub.welcomeMessage
          ? [
            new ButtonBuilder()
              .setCustomId(new CustomID('hub_welcome:remove', [hubId]).toString())
              .setLabel('Remove Message')
              .setStyle(ButtonStyle.Danger)
              .setEmoji('🗑️'),
          ]
          : []),
        new ButtonBuilder()
          .setCustomId(new CustomID('hubEdit:home', [hubId]).toString())
          .setLabel('Back to Hub')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('↩️'),
      ),
    );

    return container;
  }

  buildWelcomeModal(hubId: string, currentMessage?: string): ModalBuilder {
    return new ModalBuilder()
      .setCustomId(new CustomID('hub_welcome:modal', [hubId]).toString())
      .setTitle('Set Welcome Message')
      .addComponents(
        new ActionRowBuilder<TextInputBuilder>().addComponents(
          new TextInputBuilder()
            .setCustomId('welcome_message')
            .setLabel('Welcome Message')
            .setStyle(TextInputStyle.Paragraph)
            .setPlaceholder('Enter the welcome message for new servers joining your hub...')
            .setMaxLength(1000)
            .setValue(currentMessage || '')
            .setRequired(false),
        ),
      );
  }
}
