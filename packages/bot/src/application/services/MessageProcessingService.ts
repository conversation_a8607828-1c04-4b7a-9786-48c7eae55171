/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { inject, injectable } from 'inversify';
import {
  WebhookClient,
  type Message,
  Collection,
  EmbedBuilder,
} from 'discord.js';
import type { IHubRepository } from '../../domain/repositories/HubRepositories.js';
import type {
  IConnectionRepository,
  HubConnectionData,
} from '../../domain/repositories/ConnectionRepository.js';
import type { IEventBus } from '../../infrastructure/events/EventBus.js';
import type { Hub } from '../../domain/entities/Hub.js';
import { TYPES } from '../../shared/types/TYPES.js';
import { Logger } from '../../shared/utils/Logger.js';
import {
  MessageProcessedEvent,
  MessageBroadcastedEvent,
  MessageFilteredEvent,
} from '../../domain/events/DomainEvents.js';
import { ContentFilterService } from './ContentFilterService.js';
import { AntiSwearActionService } from './AntiSwearActionService.js';

export interface MessageProcessingResult {
  success: boolean;
  hubId?: string;
  recipientCount?: number;
  error?: string;
}

export interface BroadcastResult {
  success: boolean;
  sentCount: number;
  broadcastedMessages: BroadcastedMessage[];
}

export interface BroadcastedMessage {
  channelId: string;
  messageId: string;
  webhookURL: string;
  error?: string;
}

export interface ReferredMessageData {
  messageId: string;
  content: string;
  authorName: string;
  authorAvatar: string;
  timestamp: Date;
}

// Centralized webhook client collection with rate limit management
const webhookCollection = new Collection<string, { lastUsed: Date; client: WebhookClient }>();

// Cleanup unused webhooks every 10 minutes
setInterval(() => {
  const tenMinutes = 10 * 60 * 1000;
  const now = Date.now();

  webhookCollection.forEach((data, url) => {
    if (now - data.lastUsed.getTime() > tenMinutes) {
      Logger.debug(`Cleaning up unused webhook: ${url.substring(0, 20)}...`);
      webhookCollection.delete(url);
    }
  });
}, 10 * 60 * 1000);
/*
 * Handles message processing for hub channels
 */
@injectable()
export class MessageProcessingService {
  constructor(
    @inject(TYPES.HubRepository) private hubRepository: IHubRepository,
    @inject(TYPES.ConnectionRepository) private connectionRepository: IConnectionRepository,
    @inject(TYPES.EventBus) private eventBus: IEventBus,
    @inject(TYPES.ClusterId) private clusterId: string,
    @inject(TYPES.ContentFilterService) private contentFilterService: ContentFilterService,
    @inject(TYPES.AntiSwearActionService) private antiSwearActionService: AntiSwearActionService,
  ) {}

  /**
   * Process a message sent in a hub channel
   */
  async processHubMessage(message: Message<true>): Promise<MessageProcessingResult> {
    try {
      Logger.debug(`Processing hub message in channel ${message.channelId}`);

      // 1. Get hub connection data
      const connectionData = await this.getHubConnection(message.channelId);
      if (!connectionData) {
        return { success: false, error: 'No hub connection found' };
      }

      // 2. Get hub and all its connections
      const hub = await this.hubRepository.findById(connectionData.hubId);
      if (!hub) {
        return { success: false, error: 'Hub not found' };
      }

      const allConnections = await this.getHubConnections(connectionData.hubId, message.channelId);

      // 3. Validate message (content filtering, rules, etc.)
      const validationResult = await this.validateMessage(message, hub, connectionData);
      if (!validationResult.isValid) {
        await this.eventBus.publish(
          new MessageFilteredEvent(
            message.id,
            message.author.id,
            connectionData.hubId,
            validationResult.reason || 'Content filtered',
            this.clusterId,
          ),
        );

        return {
          success: false,
          error: validationResult.reason || 'Message filtered',
        };
      }

      // 4. Broadcast message to all connections
      await this.broadcastMessage(message, hub, allConnections, connectionData);

      // 5. Publish success events
      await this.eventBus.publish(
        new MessageProcessedEvent(
          message.id,
          message.author.id,
          connectionData.hubId,
          message.content,
          this.clusterId,
        ),
      );

      await this.eventBus.publish(
        new MessageBroadcastedEvent(
          message.id,
          connectionData.hubId,
          allConnections.length,
          this.clusterId,
        ),
      );

      Logger.debug(`Successfully processed message to ${allConnections.length} connections`);

      return {
        success: true,
        hubId: connectionData.hubId,
        recipientCount: allConnections.length,
      };
    }
    catch (error) {
      Logger.error('Error processing hub message:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get hub connection data for a channel
   */
  private async getHubConnection(channelId: string): Promise<HubConnectionData | null> {
    try {
      Logger.debug(`Getting hub connection for channel ${channelId}`);
      return await this.connectionRepository.findByChannelId(channelId);
    }
    catch (error) {
      Logger.error('Error getting hub connection:', error);
      return null;
    }
  }

  /**
   * Get all connections for a hub except the sender
   */
  private async getHubConnections(
    hubId: string,
    excludeChannelId: string,
  ): Promise<HubConnectionData[]> {
    try {
      Logger.debug(`Getting connections for hub ${hubId}`);
      return await this.connectionRepository.findActiveByHubId(hubId, excludeChannelId);
    }
    catch (error) {
      Logger.error('Error getting hub connections:', error);
      return [];
    }
  }

  /**
   * Validate message content and user permissions
   */
  private async validateMessage(
    message: Message<true>,
    hub: Hub,
    _connectionData: HubConnectionData,
  ): Promise<{ isValid: boolean; reason?: string }> {
    try {
      Logger.debug(`Validating message in hub ${hub.id}`);

      // 1. Use the modern content filter service (comprehensive filtering)
      const filterResult = await this.contentFilterService.checkMessage(message, hub.id);

      if (filterResult.blocked) {
        // Handle anti-swear actions if they exist
        if (
          filterResult.actions &&
          filterResult.ruleId &&
          filterResult.ruleName &&
          filterResult.matches
        ) {
          const actionResults = await this.antiSwearActionService.executeActions(
            filterResult.actions,
            message,
            filterResult.ruleId,
            hub.id,
            filterResult.ruleName,
            filterResult.matches,
          );

          // Check if any action resulted in blocking the message
          if (this.antiSwearActionService.shouldBlockMessage(actionResults)) {
            const userFriendlyMsg =
              this.antiSwearActionService.getUserFriendlyMessage(actionResults);
            return {
              isValid: false,
              reason: userFriendlyMsg,
            };
          }
        }
        else {
          // Standard content filter block (non-anti-swear)
          const userFriendlyMsg = this.contentFilterService.getUserFriendlyMessage(filterResult);
          return {
            isValid: false,
            reason: userFriendlyMsg || `Prohibited content: ${filterResult.reason}`,
          };
        }
      }

      // 2. Check for empty messages
      if (!message.content && message.attachments.size === 0 && message.stickers.size === 0) {
        return {
          isValid: false,
          reason: '📝 Please include some content in your message.',
        };
      }

      // 3. Message length validation
      if (message.content.length > 2000) {
        return {
          isValid: false,
          reason: '📏 Your message is too long. Please keep it under 2000 characters.',
        };
      }

      // 4. Additional safety checks
      if (await this.performAdditionalSafetyChecks(message, hub)) {
        return {
          isValid: false,
          reason: '🛡️ Your message was blocked for safety reasons.',
        };
      }

      return { isValid: true };
    }
    catch (error) {
      Logger.error('Error validating message:', error);
      return { isValid: false, reason: 'Validation error - please try again' };
    }
  }

  /**
   * Perform additional safety checks
   */
  private async performAdditionalSafetyChecks(message: Message<true>, _hub: Hub): Promise<boolean> {
    const content = message.content.toLowerCase();

    // Enhanced spam detection
    if (this.isAdvancedSpam(content)) {
      return true;
    }

    // Enhanced harmful content detection
    if (this.containsHarmfulContent(content)) {
      return true;
    }

    // Suspicious pattern detection
    if (this.containsSuspiciousPatterns(content)) {
      return true;
    }

    return false;
  }

  /**
   * Advanced spam detection
   */
  private isAdvancedSpam(content: string): boolean {
    // Excessive repeated characters
    if (/(.)\1{8,}/g.test(content)) {
      return true;
    }

    // Excessive caps (more than 70% caps in messages over 20 chars)
    const capsCount = (content.match(/[A-Z]/g) || []).length;
    if (content.length > 20 && capsCount / content.length > 0.7) {
      return true;
    }

    // Common spam patterns
    const spamPatterns = [
      /check.*dm/i,
      /free.*nitro/i,
      /click.*here/i,
      /won.*prize/i,
      /urgent.*action/i,
    ];

    return spamPatterns.some((pattern) => pattern.test(content));
  }

  /**
   * Enhanced harmful content detection
   */
  private containsHarmfulContent(content: string): boolean {
    const harmfulPatterns = [
      /k(ys|ill\s+your?self)/i,
      /suicide/i,
      /self\s*harm/i,
      /die.*please/i,
      /hurt.*yourself/i,
    ];

    return harmfulPatterns.some((pattern) => pattern.test(content));
  }

  /**
   * Suspicious pattern detection
   */
  private containsSuspiciousPatterns(content: string): boolean {
    // Potential doxxing attempts
    const doxxingPatterns = [
      /\b\d{3}-\d{3}-\d{4}\b/, // Phone numbers
      /\b\d{1,5}\s+\w+\s+(street|st|avenue|ave|road|rd|drive|dr)/i, // Addresses
      /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/, // Emails
    ];

    return doxxingPatterns.some((pattern) => pattern.test(content));
  }

  /**
   * Broadcast message to all hub connections
   */
  private async broadcastMessage(
    message: Message<true>,
    _hub: Hub,
    connections: HubConnectionData[],
    _senderConnection: HubConnectionData,
  ): Promise<BroadcastResult> {
    try {
      Logger.debug(`Broadcasting message to ${connections.length} connections`);

      if (connections.length === 0) {
        return { success: true, sentCount: 0, broadcastedMessages: [] };
      }

      // Resolve attachments and referenced message data
      const attachmentURL = await this.resolveAttachmentURL(message);
      const referredData = await this.getReferredMessageData(message);

      // Format the message for broadcasting
      const username = message.member?.displayName ?? message.author.username;
      const avatarURL = message.author.displayAvatarURL();
      const serverName = message.guild.name;

      const broadcastedMessages: BroadcastedMessage[] = [];
      let sentCount = 0;

      // Send to all connections in parallel
      const broadcastPromises = connections.map(async (connection) => {
        try {
          const result = await this.sendToConnection(
            message,
            username,
            avatarURL,
            serverName,
            connection,
            attachmentURL,
            referredData,
          );

          broadcastedMessages.push(result);
          if (!result.error) sentCount++;
        }
        catch (error) {
          Logger.error(`Failed to send to connection ${connection.channelId}:`, error);
          broadcastedMessages.push({
            channelId: connection.channelId,
            messageId: '',
            webhookURL: connection.webhookURL || '',
            error: error instanceof Error ? error.message : 'Unknown error',
          });
        }
      });

      await Promise.all(broadcastPromises);

      Logger.debug(`Successfully sent message to ${sentCount}/${connections.length} connections`);
      return { success: sentCount > 0, sentCount, broadcastedMessages };
    }
    catch (error) {
      Logger.error('Error broadcasting message:', error);
      return { success: false, sentCount: 0, broadcastedMessages: [] };
    }
  }

  /**
   * Send message to a specific connection via webhook
   */
  private async sendToConnection(
    message: Message<true>,
    username: string,
    avatarURL: string,
    serverName: string,
    connection: HubConnectionData,
    attachmentURL: string | null,
    referredData: ReferredMessageData | null,
  ): Promise<BroadcastedMessage> {
    try {
      if (!connection.webhookURL) {
        Logger.warn(`No webhook URL for connection ${connection.channelId}`);
        return {
          channelId: connection.channelId,
          messageId: '',
          webhookURL: '',
          error: 'No webhook URL',
        };
      }

      // Get the centralized webhook client
      const webhookClient = this.getWebhookClient(connection.webhookURL);

      // Format message content
      const content = this.formatMessageContent(message, serverName);

      // Prepare base webhook payload
      const webhookPayload = {
        content,
        username: `${username} • ${serverName}`,
        avatarURL,
        allowedMentions: { parse: [] as never[] }, // Disable all mentions for safety
      };

      // Prepare embeds array
      const embeds: EmbedBuilder[] = [];

      // Add attachment if present
      if (attachmentURL) {
        // For attachments, we include them as embeds or in content
        if (this.isImageOrVideo(attachmentURL)) {
          const attachmentEmbed = new EmbedBuilder()
            .setImage(attachmentURL)
            .setColor('#5865F2');
          embeds.push(attachmentEmbed);
        }
        else {
          // For non-image attachments, add as link in content
          webhookPayload.content += `\n🔗 [**Attachment**](${attachmentURL})`;
        }
      }

      // Add reply embed if this is a reply
      if (referredData) {
        const replyEmbed = this.createReplyEmbed(referredData);
        embeds.unshift(replyEmbed); // Put reply embed first
      }

      // Send via webhook
      const finalPayload = embeds.length > 0
        ? { ...webhookPayload, embeds }
        : webhookPayload;

      const sentMessage = await webhookClient.send(finalPayload);

      return {
        channelId: connection.channelId,
        messageId: typeof sentMessage === 'string' ? sentMessage : sentMessage.id,
        webhookURL: connection.webhookURL,
      };
    }
    catch (error) {
      Logger.error(`Error sending to connection ${connection.channelId}:`, error);
      return {
        channelId: connection.channelId,
        messageId: '',
        webhookURL: connection.webhookURL || '',
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Check if URL is an image or video
   */
  private isImageOrVideo(url: string): boolean {
    const imageVideoExtensions = /\.(jpg|jpeg|png|gif|webp|mp4|mov|webm)$/i;
    return imageVideoExtensions.test(url) || url.includes('tenor.com') || url.includes('giphy.com');
  }

  /**
   * Get or create a webhook client from the collection
   */
  private getWebhookClient(webhookURL: string): WebhookClient {
    const existing = webhookCollection.get(webhookURL);

    if (existing) {
      existing.lastUsed = new Date();
      return existing.client;
    }

    Logger.debug(`Creating new webhook client: ${webhookURL.substring(0, 20)}...`);
    const client = new WebhookClient({ url: webhookURL });
    webhookCollection.set(webhookURL, { lastUsed: new Date(), client });

    return client;
  }

  /**
   * Resolve attachment URL from message (attachments, GIFs, stickers, videos)
   */
  private async resolveAttachmentURL(message: Message<true>): Promise<string | null> {
    // Direct file attachments (images, videos, files)
    if (message.attachments.size > 0) {
      return message.attachments.first()!.url;
    }

    // Stickers
    if (message.stickers.size > 0) {
      return message.stickers.first()!.url;
    }

    // Extract URLs from message content (GIFs, images, videos)
    const urlRegex = /(https?:\/\/[^\s]+\.(gif|jpg|jpeg|png|webp|mp4|mov|webm))/gi;
    const match = message.content.match(urlRegex);
    if (match) {
      return match[0];
    }

    // Check for Tenor/Giphy GIFs in content
    const tenorRegex = /(https?:\/\/tenor\.com\/view\/[^\s]+)/gi;
    const giphyRegex = /(https?:\/\/giphy\.com\/gifs\/[^\s]+)/gi;

    const tenorMatch = message.content.match(tenorRegex);
    if (tenorMatch) return tenorMatch[0];

    const giphyMatch = message.content.match(giphyRegex);
    if (giphyMatch) return giphyMatch[0];

    return null;
  }

  /**
   * Fetch and format referenced message data for reply embeds
   */
  private async getReferredMessageData(
    message: Message<true>,
  ): Promise<ReferredMessageData | null> {
    if (!message.reference) return null;

    try {
      const referredMessage = await message.fetchReference();
      return {
        messageId: referredMessage.id,
        content: this.truncateContent(referredMessage.content || '*[No content]*', 100),
        authorName: referredMessage.author.username,
        authorAvatar: referredMessage.author.displayAvatarURL(),
        timestamp: referredMessage.createdAt,
      };
    }
    catch (error) {
      Logger.debug('Could not fetch referenced message:', error);
      return null;
    }
  }

  /**
   * Create reply embed for referenced messages
   */
  private createReplyEmbed(referredData: ReferredMessageData): EmbedBuilder {
    return new EmbedBuilder()
      .setAuthor({
        name: `Replying to ${referredData.authorName}`,
        iconURL: referredData.authorAvatar,
      })
      .setDescription(referredData.content)
      .setColor('#5865F2')
      .setTimestamp(referredData.timestamp);
  }

  /**
   * Truncate content to specified length
   */
  private truncateContent(content: string, maxLength: number): string {
    if (content.length <= maxLength) return content;
    return `${content.substring(0, maxLength - 3)}...`;
  }

  /**
   * Format message content for broadcasting
   */
  private formatMessageContent(message: Message<true>, _serverName: string): string {
    let content = message.content;

    // Add attachment info if present
    if (message.attachments.size > 0) {
      const attachment = message.attachments.first()!;
      content += `\n📎 **${attachment.name}** (${attachment.size} bytes)`;
    }

    // Add sticker info if present
    if (message.stickers.size > 0) {
      const sticker = message.stickers.first()!;
      content += `\n🎭 **${sticker.name}**`;
    }

    // Limit content length (Discord webhook limit is 2000 characters)
    if (content.length > 1900) {
      content = `${content.substring(0, 1900)}...`;
    }

    return content || '*[Empty message]*';
  }
}
