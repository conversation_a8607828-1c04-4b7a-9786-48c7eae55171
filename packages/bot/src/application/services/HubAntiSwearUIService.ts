/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { inject, injectable } from 'inversify';
import {
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
  ContainerBuilder,
  ModalBuilder,
  SectionBuilder,
  SeparatorSpacingSize,
  StringSelectMenuBuilder,
  StringSelectMenuOptionBuilder,
  TextDisplayBuilder,
  TextInputBuilder,
  TextInputStyle,
} from 'discord.js';
import { stripIndents } from 'common-tags';
import { TYPES } from '../../shared/types/TYPES.js';
import { CustomID } from '../../shared/utils/CustomID.js';
import type { IHubRepository } from '../../domain/repositories/HubRepositories.js';

const MAX_RULES = 5;

// Mock types for anti-swear functionality - these should be replaced with actual types
interface AntiSwearRule {
  id: string;
  name: string;
  patterns: Array<{ pattern: string; isRegex: boolean }>;
  actions: BlockWordAction[];
}

enum BlockWordAction {
  DELETE_MESSAGE = 'DELETE_MESSAGE',
  TIMEOUT_USER = 'TIMEOUT_USER',
  WARN_USER = 'WARN_USER',
  BAN_USER = 'BAN_USER',
}

const ACTION_LABELS = {
  [BlockWordAction.DELETE_MESSAGE]: 'Delete Message',
  [BlockWordAction.TIMEOUT_USER]: 'Timeout User',
  [BlockWordAction.WARN_USER]: 'Warn User',
  [BlockWordAction.BAN_USER]: 'Ban User',
};

@injectable()
export class HubAntiSwearUIService {
  constructor(
    @inject(TYPES.HubRepository)
    private readonly hubRepository: IHubRepository,
  ) {}

  async buildAntiSwearContainer(hubId: string): Promise<ContainerBuilder> {
    const hub = await this.hubRepository.findById(hubId);
    if (!hub) {
      throw new Error(`Hub with ID ${hubId} not found`);
    }

    // TODO: Replace with actual anti-swear rules fetching
    const rules: AntiSwearRule[] = []; // await hub.fetchAntiSwearRules();

    const container = new ContainerBuilder();

    container.addTextDisplayComponents(
      new TextDisplayBuilder().setContent('## 🛡️ Anti-Swear Configuration'),
    );

    container.addSeparatorComponents((separator) =>
      separator.setSpacing(SeparatorSpacingSize.Large),
    );

    container.addTextDisplayComponents(
      new TextDisplayBuilder().setContent(
        stripIndents`
          ### 🔍 Current Status
          Configure anti-swear rules to automatically moderate inappropriate content in your hub.

          **Total Rules:** ${rules.length}/${MAX_RULES}
          **Status:** ${rules.length > 0 ? '✅ Active' : '❌ Inactive'}

          ### 💡 How it works
          - Rules can contain multiple patterns (words or phrases)
          - Each rule can have multiple actions when triggered
          - Patterns support both exact matches and regex
          - Actions include: Delete, Timeout, Warn, or Ban
        `,
      ),
    );

    if (rules.length === 0) {
      container.addTextDisplayComponents(
        new TextDisplayBuilder().setContent('### 📝 No rules configured yet'),
      );
    }
    else {
      container.addSeparatorComponents((separator) =>
        separator.setSpacing(SeparatorSpacingSize.Large),
      );

      container.addTextDisplayComponents(
        new TextDisplayBuilder().setContent('### 📋 Current Rules'),
      );

      // Add rule selection
      container.addActionRowComponents((row) =>
        row.addComponents(
          new StringSelectMenuBuilder()
            .setCustomId(new CustomID('hub_antiswear:select', [hubId]).toString())
            .setPlaceholder('Select a rule to view or edit')
            .setOptions(
              rules.map((rule) =>
                new StringSelectMenuOptionBuilder()
                  .setLabel(rule.name)
                  .setValue(rule.id)
                  .setDescription(
                    `${rule.patterns.length} patterns, ${rule.actions.length} actions`,
                  ),
              ),
            ),
        ),
      );

      // Show rules as sections
      for (const rule of rules) {
        const patterns = rule.patterns.map((p) => p.pattern).join(', ');
        const actions = rule.actions.map((a) => ACTION_LABELS[a]).join(', ');

        const section = new SectionBuilder()
          .addTextDisplayComponents(
            new TextDisplayBuilder().setContent(
              `### ${rule.name}\n**Patterns:** ${patterns}\n**Actions:** ${actions}`,
            ),
          )
          .setButtonAccessory(
            new ButtonBuilder()
              .setCustomId(new CustomID('hub_antiswear:edit', [hubId, rule.id]).toString())
              .setLabel('Edit')
              .setStyle(ButtonStyle.Secondary)
              .setEmoji('✏️'),
          );

        container.addSectionComponents(section);
      }
    }

    container.addSeparatorComponents((separator) =>
      separator.setSpacing(SeparatorSpacingSize.Large),
    );

    container.addActionRowComponents((row) =>
      row.addComponents(
        new ButtonBuilder()
          .setCustomId(new CustomID('hub_antiswear:add', [hubId]).toString())
          .setLabel('Add Rule')
          .setStyle(ButtonStyle.Success)
          .setEmoji('➕')
          .setDisabled(rules.length >= MAX_RULES),
        new ButtonBuilder()
          .setCustomId(new CustomID('hubEdit:home', [hubId]).toString())
          .setLabel('Back to Hub')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('↩️'),
        new ButtonBuilder()
          .setCustomId(new CustomID('hub_antiswear:refresh', [hubId]).toString())
          .setLabel('Refresh')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('🔄'),
      ),
    );

    return container;
  }

  async buildRuleDetailContainer(hubId: string, rule: AntiSwearRule): Promise<ContainerBuilder> {
    const container = new ContainerBuilder();

    container.addTextDisplayComponents(
      new TextDisplayBuilder().setContent(`## 🛡️ Anti-Swear Rule: ${rule.name}`),
    );

    container.addSeparatorComponents((separator) =>
      separator.setSpacing(SeparatorSpacingSize.Large),
    );

    const patterns = rule.patterns.map((p) => p.pattern).join(', ');
    const actions =
      rule.actions.length > 0
        ? rule.actions.map((a) => ACTION_LABELS[a]).join(', ')
        : 'No actions configured';

    container.addTextDisplayComponents(
      new TextDisplayBuilder().setContent(
        stripIndents`
          ### 📝 Rule Details
          **Name:** ${rule.name}
          **Patterns:** ${patterns || 'No patterns configured'}
          **Actions:** ${actions}

          ### ⚙️ Configuration
          Click the buttons below to modify this rule.
        `,
      ),
    );

    container.addSeparatorComponents((separator) =>
      separator.setSpacing(SeparatorSpacingSize.Large),
    );

    // Actions select menu
    container.addActionRowComponents((row) =>
      row.addComponents(
        new StringSelectMenuBuilder()
          .setCustomId(new CustomID('hub_antiswear:actions', [hubId, rule.id]).toString())
          .setPlaceholder('Select actions for this rule')
          .setMinValues(0)
          .setMaxValues(Object.keys(BlockWordAction).length)
          .setOptions(
            Object.entries(ACTION_LABELS).map(([value, label]) =>
              new StringSelectMenuOptionBuilder()
                .setLabel(label)
                .setValue(value)
                .setDefault(rule.actions.includes(value as BlockWordAction)),
            ),
          ),
      ),
    );

    container.addActionRowComponents((row) =>
      row.addComponents(
        new ButtonBuilder()
          .setCustomId(new CustomID('hub_antiswear:edit_patterns', [hubId, rule.id]).toString())
          .setLabel('Edit Patterns')
          .setStyle(ButtonStyle.Primary)
          .setEmoji('📝'),
        new ButtonBuilder()
          .setCustomId(new CustomID('hub_antiswear:delete', [hubId, rule.id]).toString())
          .setLabel('Delete Rule')
          .setStyle(ButtonStyle.Danger)
          .setEmoji('🗑️'),
        new ButtonBuilder()
          .setCustomId(new CustomID('hub_antiswear:back', [hubId]).toString())
          .setLabel('Back to Rules')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('⬅️'),
      ),
    );

    return container;
  }

  buildAddRuleModal(hubId: string): ModalBuilder {
    return new ModalBuilder()
      .setCustomId(new CustomID('hub_antiswear:add_modal', [hubId]).toString())
      .setTitle('Add Anti-Swear Rule')
      .addComponents(
        new ActionRowBuilder<TextInputBuilder>().addComponents(
          new TextInputBuilder()
            .setCustomId('rule_name')
            .setLabel('Rule Name')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Enter a name for this rule...')
            .setMaxLength(100)
            .setRequired(true),
        ),
        new ActionRowBuilder<TextInputBuilder>().addComponents(
          new TextInputBuilder()
            .setCustomId('patterns')
            .setLabel('Patterns (one per line)')
            .setStyle(TextInputStyle.Paragraph)
            .setPlaceholder('Enter words or phrases to detect...')
            .setMaxLength(1000)
            .setRequired(true),
        ),
      );
  }

  buildEditRuleModal(hubId: string, rule: AntiSwearRule): ModalBuilder {
    const patterns = rule.patterns.map((p) => p.pattern).join('\n');

    return new ModalBuilder()
      .setCustomId(new CustomID('hub_antiswear:edit_modal', [hubId, rule.id]).toString())
      .setTitle(`Edit Rule: ${rule.name}`)
      .addComponents(
        new ActionRowBuilder<TextInputBuilder>().addComponents(
          new TextInputBuilder()
            .setCustomId('rule_name')
            .setLabel('Rule Name')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Enter a name for this rule...')
            .setMaxLength(100)
            .setValue(rule.name)
            .setRequired(true),
        ),
        new ActionRowBuilder<TextInputBuilder>().addComponents(
          new TextInputBuilder()
            .setCustomId('patterns')
            .setLabel('Patterns (one per line)')
            .setStyle(TextInputStyle.Paragraph)
            .setPlaceholder('Enter words or phrases to detect...')
            .setMaxLength(1000)
            .setValue(patterns)
            .setRequired(true),
        ),
      );
  }
}
