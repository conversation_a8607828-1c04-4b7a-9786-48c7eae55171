/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { inject, injectable } from 'inversify';
import type {
  HubLogConfig,
  HubLogConfigUpdateData,
  LogConfigType,
} from '../../domain/entities/HubLogConfig.js';
import type { HubLogConfigRepository } from '../../domain/repositories/HubLogConfigRepository.js';
import { TYPES } from '../../shared/types/TYPES.js';
import { Logger } from '../../shared/utils/Logger.js';

@injectable()
export class HubLogConfigService {
  constructor(
    @inject(TYPES.HubLogConfigRepository)
    private readonly hubLogConfigRepository: HubLogConfigRepository,
  ) {}

  /**
   * Get hub log configuration by hub ID
   */
  async getHubLogConfig(hubId: string): Promise<HubLogConfig | null> {
    try {
      return await this.hubLogConfigRepository.getByHubId(hubId);
    }
    catch (error) {
      Logger.error('Failed to get hub log config', { hubId, error });
      throw error;
    }
  }

  /**
   * Set a log channel for a specific log type
   */
  async setLogChannel(
    hubId: string,
    logType: LogConfigType,
    channelId: string,
  ): Promise<HubLogConfig> {
    try {
      const updateData: HubLogConfigUpdateData = {
        [`${logType}ChannelId`]: channelId,
      };

      return await this.hubLogConfigRepository.upsert(hubId, updateData);
    }
    catch (error) {
      Logger.error('Failed to set log channel', { hubId, logType, channelId, error });
      throw error;
    }
  }

  /**
   * Set a role ID for a specific log type
   */
  async setLogRole(hubId: string, logType: LogConfigType, roleId: string): Promise<HubLogConfig> {
    try {
      const updateData: HubLogConfigUpdateData = {
        [`${logType}RoleId`]: roleId,
      };

      return await this.hubLogConfigRepository.upsert(hubId, updateData);
    }
    catch (error) {
      Logger.error('Failed to set log role', { hubId, logType, roleId, error });
      throw error;
    }
  }

  /**
   * Reset a log type (remove channel and role)
   */
  async resetLogType(hubId: string, logType: LogConfigType): Promise<HubLogConfig> {
    try {
      const updateData: HubLogConfigUpdateData = {
        [`${logType}ChannelId`]: null,
        [`${logType}RoleId`]: null,
      };

      return await this.hubLogConfigRepository.upsert(hubId, updateData);
    }
    catch (error) {
      Logger.error('Failed to reset log type', { hubId, logType, error });
      throw error;
    }
  }

  /**
   * Remove a role for a specific log type
   */
  async removeLogRole(hubId: string, logType: LogConfigType): Promise<HubLogConfig> {
    try {
      const updateData: HubLogConfigUpdateData = {
        [`${logType}RoleId`]: null,
      };

      return await this.hubLogConfigRepository.upsert(hubId, updateData);
    }
    catch (error) {
      Logger.error('Failed to remove log role', { hubId, logType, error });
      throw error;
    }
  }

  /**
   * Update multiple log configurations at once
   */
  async updateLogConfig(hubId: string, data: HubLogConfigUpdateData): Promise<HubLogConfig> {
    try {
      return await this.hubLogConfigRepository.upsert(hubId, data);
    }
    catch (error) {
      Logger.error('Failed to update log config', { hubId, data, error });
      throw error;
    }
  }

  /**
   * Delete all log configurations for a hub
   */
  async deleteHubLogConfig(hubId: string): Promise<void> {
    try {
      await this.hubLogConfigRepository.delete(hubId);
    }
    catch (error) {
      Logger.error('Failed to delete hub log config', { hubId, error });
      throw error;
    }
  }
}
