/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { ClusterManager, HeartbeatManager, ReClusterManager } from 'discord-hybrid-sharding';
import { join } from 'node:path';
import 'dotenv/config';
import { Logger } from './shared/utils/Logger.js';

/**
 * InterChat Main Process
 */

const shardsPerClusters = 10;

// Use the new client path in packages/bot
// TODO: have a seperate tsconfig in packages/bot that compiles to packages/build/bot
const clientPath = join(process.cwd(), 'build/bot/src/client.js');

const clusterManager = new ClusterManager(clientPath, {
  token: process.env.DISCORD_TOKEN,
  totalShards: 'auto',
  totalClusters: 'auto',
  shardsPerClusters,
});

// Add cluster management extensions
clusterManager.extend(
  new HeartbeatManager({
    interval: 10 * 1000,
    maxMissedHeartbeats: 2,
  }),
);
clusterManager.extend(new ReClusterManager());

// Cluster event handlers
clusterManager.on('clusterReady', (cluster) => {
  Logger.info(
    `✅ Cluster ${cluster.id} ready with shards ${cluster.shardList[0]}...${cluster.shardList.at(-1)}`,
  );

  // Start scheduled tasks when the last cluster is ready
  if (cluster.id === clusterManager.totalClusters - 1) {
    Logger.info('🚀 All clusters ready, starting scheduled tasks...');
    // TODO: Start scheduled tasks using new architecture
  }

  // Handle recluster requests
  cluster.on('message', async (message) => {
    if (message === 'recluster') {
      Logger.info('🔄 Recluster requested, starting recluster...');

      const recluster = await clusterManager.recluster?.start({
        restartMode: 'rolling',
        totalShards: 'auto',
        shardsPerClusters,
      });

      if (recluster?.success) {
        Logger.info('✅ Recluster completed successfully');
      }
      else {
        Logger.error('❌ Failed to recluster!');
      }
    }
  });
});

clusterManager.on('clusterCreate', (cluster) => {
  Logger.debug(`Creating cluster ${cluster.id}...`);
});

clusterManager.on('debug', (message) => {
  Logger.debug(`ClusterManager Debug: ${message}`);
});

// Global error handling
process.on('unhandledRejection', (reason, promise) => {
  Logger.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
});

process.on('uncaughtException', (error) => {
  Logger.error('❌ Uncaught Exception:', error);
  process.exit(1);
});

// Start the cluster manager
Logger.info('🚀 Starting Cluster Manager...');
Logger.info(`📊 Shards per cluster: ${shardsPerClusters}`);
Logger.info(`🎯 Client path: ${clientPath}`);

clusterManager.spawn({ timeout: -1 });

export { clusterManager };
