/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { injectable, inject } from 'inversify';
import { PrismaClient } from '../../../../../../build/generated/prisma/client/index.js';
import type { IConnectionRepository, HubConnectionData } from '../../../domain/repositories/ConnectionRepository.js';
import { TYPES } from '../../../shared/types/TYPES.js';
import { Logger } from '../../../shared/utils/Logger.js';

@injectable()
export class ConnectionRepository implements IConnectionRepository {
  constructor(
    @inject(TYPES.PrismaClient) private readonly prisma: PrismaClient,
  ) {}

  /**
   * Find a connection by channel ID
   */
  async findByChannelId(channelId: string): Promise<HubConnectionData | null> {
    try {
      const connection = await this.prisma.connection.findFirst({
        where: {
          channelId,
          connected: true,
        },
        select: {
          id: true,
          channelId: true,
          connected: true,
          compact: true,
          webhookURL: true,
          parentId: true,
          hubId: true,
          embedColor: true,
          serverId: true,
          lastActive: true,
        },
      });

      return connection;
    }
    catch (error) {
      Logger.error(`Error finding connection by channel ID ${channelId}:`, error);
      return null;
    }
  }

  /**
   * Find all active connections for a hub, excluding a specific channel
   */
  async findActiveByHubId(hubId: string, excludeChannelId?: string): Promise<HubConnectionData[]> {
    try {
      const where: { hubId: string; connected: boolean; channelId?: { not: string } } = {
        hubId,
        connected: true,
      };

      if (excludeChannelId) {
        where.channelId = { not: excludeChannelId };
      }

      const connections = await this.prisma.connection.findMany({
        where,
        select: {
          id: true,
          channelId: true,
          connected: true,
          compact: true,
          webhookURL: true,
          parentId: true,
          hubId: true,
          embedColor: true,
          serverId: true,
          lastActive: true,
        },
      });

      return connections;
    }
    catch (error) {
      Logger.error(`Error finding active connections for hub ${hubId}:`, error);
      return [];
    }
  }

  /**
   * Find all connections for a hub
   */
  async findByHubId(hubId: string): Promise<HubConnectionData[]> {
    try {
      const connections = await this.prisma.connection.findMany({
        where: { hubId },
        select: {
          id: true,
          channelId: true,
          connected: true,
          compact: true,
          webhookURL: true,
          parentId: true,
          hubId: true,
          embedColor: true,
          serverId: true,
          lastActive: true,
        },
      });

      return connections;
    }
    catch (error) {
      Logger.error(`Error finding connections for hub ${hubId}:`, error);
      return [];
    }
  }

  /**
   * Update the last active timestamp for a connection
   */
  async updateLastActive(connectionId: string): Promise<void> {
    try {
      await this.prisma.connection.update({
        where: { id: connectionId },
        data: { lastActive: new Date() },
      });
    }
    catch (error) {
      Logger.error(`Error updating last active for connection ${connectionId}:`, error);
    }
  }
}
