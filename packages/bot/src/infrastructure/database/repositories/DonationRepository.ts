import { injectable, inject } from 'inversify';
import {
  PrismaClient,
  type Donation as DbDonation,
} from '../../../../../../build/generated/prisma/client/index.js';
import type { IDonationRepository } from '../../../domain/repositories/DonationRepositories.js';
import { Donation, DonationSource, DonationStatus } from '../../../domain/entities/Donation.js';
import { Money } from '../../../domain/value-objects/DonationValueObjects.js';
import { TYPES } from '../../../shared/types/TYPES.js';

@injectable()
export class DonationRepository implements IDonationRepository {
  constructor(@inject(TYPES.PrismaClient) private readonly prisma: PrismaClient) {}

  async save(donation: Donation): Promise<void> {
    const data = this.mapDomainToDatabase(donation);

    await this.prisma.donation.upsert({
      where: { id: donation.id },
      create: data,
      update: data,
    });
  }

  async findById(id: string): Promise<Donation | null> {
    const donation = await this.prisma.donation.findUnique({
      where: { id },
      include: {
        donationTier: true,
        discordUser: true,
      },
    });

    if (!donation) {
      return null;
    }

    return this.mapDatabaseToDomain(donation);
  }

  async findBySourceTransactionId(
    sourceTransactionId: string,
    source: DonationSource,
  ): Promise<Donation | null> {
    if (source !== DonationSource.KOFI) {
      return null;
    }

    const donation = await this.prisma.donation.findUnique({
      where: { kofiTransactionId: sourceTransactionId },
      include: {
        donationTier: true,
        discordUser: true,
      },
    });

    if (!donation) {
      return null;
    }

    return this.mapDatabaseToDomain(donation);
  }

  async findByUserId(userId: string): Promise<Donation[]> {
    const donations = await this.prisma.donation.findMany({
      where: { discordUserId: userId },
      include: {
        donationTier: true,
        discordUser: true,
      },
      orderBy: { createdAt: 'desc' },
    });

    return donations.map((donation) => this.mapDatabaseToDomain(donation));
  }

  async findByDateRange(startDate: Date, endDate: Date): Promise<Donation[]> {
    const donations = await this.prisma.donation.findMany({
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      },
      include: {
        donationTier: true,
        discordUser: true,
      },
      orderBy: { createdAt: 'desc' },
    });

    return donations.map((donation) => this.mapDatabaseToDomain(donation));
  }

  async getTotalDonatedByUser(userId: string): Promise<Money> {
    const result = await this.prisma.donation.aggregate({
      where: {
        discordUserId: userId,
        processed: true,
      },
      _sum: { amount: true },
    });

    const totalAmount = result._sum.amount || 0;
    return Money.create(totalAmount, 'USD');
  }

  async findActiveDonations(): Promise<Donation[]> {
    const donations = await this.prisma.donation.findMany({
      where: { processed: true },
      include: {
        donationTier: true,
        discordUser: true,
      },
      orderBy: { createdAt: 'desc' },
    });

    return donations.map((donation) => this.mapDatabaseToDomain(donation));
  }

  async findBySource(source: DonationSource): Promise<Donation[]> {
    if (source !== DonationSource.KOFI) {
      return [];
    }

    const donations = await this.prisma.donation.findMany({
      include: {
        donationTier: true,
        discordUser: true,
      },
      orderBy: { createdAt: 'desc' },
    });

    return donations.map((donation) => this.mapDatabaseToDomain(donation));
  }

  private mapDomainToDatabase(donation: Donation) {
    return {
      id: donation.id,
      kofiTransactionId: donation.sourceTransactionId,
      messageId: donation.sourceTransactionId,
      amount: donation.amount.amount,
      currency: donation.amount.currency,
      fromName: donation.donorName || 'Anonymous',
      message: donation.message,
      email: donation.donorEmail,
      isPublic: true,
      kofiTimestamp: donation.donatedAt,
      kofiUrl: null,
      discordUserId: donation.userId,
      processed: donation.status === DonationStatus.COMPLETED,
      donationTierId: null,
      createdAt: donation.donatedAt,
      updatedAt: new Date(),
    };
  }

  private mapDatabaseToDomain(dbDonation: DbDonation): Donation {
    const amount = Money.create(dbDonation.amount, dbDonation.currency);

    const status = dbDonation.processed ? DonationStatus.COMPLETED : DonationStatus.PENDING;

    return Donation.fromPersistence(
      dbDonation.id,
      dbDonation.discordUserId || 'unknown',
      amount,
      DonationSource.KOFI,
      dbDonation.kofiTransactionId,
      dbDonation.kofiTimestamp || dbDonation.createdAt,
      status,
      dbDonation.message ?? undefined,
      dbDonation.fromName,
      dbDonation.email ?? undefined,
      {},
    );
  }
}
