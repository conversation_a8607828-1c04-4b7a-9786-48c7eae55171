import { injectable, inject } from 'inversify';
import {
  PrismaClient,
  User as DbUser,
  DonationTierDefinition,
} from '../../../../../../build/generated/prisma/client/index.js';
import type { IUserPremiumRepository } from '../../../domain/repositories/DonationRepositories.js';
import { UserPremiumStatus } from '../../../domain/entities/Donation.js';
import { TYPES } from '../../../shared/types/TYPES.js';
import { DonationTier, Money } from '../../../domain/value-objects/DonationValueObjects.js';

@injectable()
export class UserPremiumRepository implements IUserPremiumRepository {
  constructor(@inject(TYPES.PrismaClient) private readonly db: PrismaClient) {}

  async save(status: UserPremiumStatus): Promise<void> {
    const data = this.mapDomainToDatabase(status);

    await this.db.user.upsert({
      where: { id: status.userId },
      create: {
        id: status.userId,
        ...data,
      },
      update: data,
    });
  }

  async findByUserId(userId: string): Promise<UserPremiumStatus | null> {
    const user = await this.db.user.findUnique({
      where: { id: userId },
      include: {
        donationTier: true,
      },
    });

    if (!user) {
      return null;
    }

    return this.mapDatabaseToDomain(user);
  }

  async findAllActivePremium(): Promise<UserPremiumStatus[]> {
    const users = await this.db.user.findMany({
      where: {
        donationTierId: { not: null },
        OR: [
          { donationExpiresAt: null }, // Never expires
          { donationExpiresAt: { gt: new Date() } }, // Not yet expired
        ],
      },
      include: {
        donationTier: true,
      },
    });

    return users.map((user) => this.mapDatabaseToDomain(user));
  }

  async findExpiredPremium(): Promise<UserPremiumStatus[]> {
    const users = await this.db.user.findMany({
      where: {
        donationTierId: { not: null },
        donationExpiresAt: { lt: new Date() },
      },
      include: {
        donationTier: true,
      },
    });

    return users.map((user) => this.mapDatabaseToDomain(user));
  }

  async findByTier(tierName: string): Promise<UserPremiumStatus[]> {
    const users = await this.db.user.findMany({
      where: {
        donationTier: {
          name: tierName,
        },
      },
      include: {
        donationTier: true,
      },
    });

    return users.map((user) => this.mapDatabaseToDomain(user));
  }

  async getActivePremiumCount(): Promise<number> {
    return await this.db.user.count({
      where: {
        donationTierId: { not: null },
        OR: [{ donationExpiresAt: null }, { donationExpiresAt: { gt: new Date() } }],
      },
    });
  }

  private mapDomainToDatabase(status: UserPremiumStatus) {
    return {
      donationTierId: status.tier?.id || null,
      donationExpiresAt: status.expiresAt,
    };
  }

  private mapDatabaseToDomain(
    dbUser: DbUser & { donationTier: DonationTierDefinition | null },
  ): UserPremiumStatus {
    let tier: DonationTier | null = null;

    if (dbUser.donationTier) {
      tier = DonationTier.create(
        dbUser.donationTier.id,
        dbUser.donationTier?.name,
        Money.create(dbUser.donationTier.price, 'USD'),
        [], // Benefits would be stored separately
        true,
      );
    }

    // Calculate if user is active based on tier and expiry
    const isActive =
      tier !== null && (dbUser.donationExpiresAt === null || dbUser.donationExpiresAt > new Date());

    return UserPremiumStatus.fromPersistence(
      dbUser.id,
      tier,
      dbUser.donationExpiresAt,
      isActive,
      Money.zero('USD'), // Would need to calculate from donations
      null, // Would need to get from donations
    );
  }
}
