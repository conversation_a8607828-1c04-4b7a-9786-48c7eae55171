/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { Container, Newable } from 'inversify';
import { readdir, stat } from 'fs/promises';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';
import { TYPES } from '../../shared/types/TYPES.js';
import { Logger } from '../../shared/utils/Logger.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

/**
 * Discord.js Event Handler Interface
 *
 * All event handlers should implement this interface to be automatically loaded
 */
export interface IDiscordEventHandler {
  /** The Discord.js event name (e.g., 'messageCreate', 'interactionCreate') */
  readonly eventName: string;
  /** Whether this is a once event (default: false) */
  readonly once?: boolean;
  /** Execute the event handler */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  execute(...args: any[]): Promise<void> | void;
}

/**
 * Event Handler Registration Info
 */
export interface EventHandlerInfo {
  eventName: string;
  handler: IDiscordEventHandler;
  once: boolean;
}

/**
 * Dynamic Event Handler Loader
 *
 * Automatically discovers and loads Discord.js event handlers from the events directory.
 * Similar to DynamicCommandLoader and DynamicInteractionLoader for consistency.
 */
export class DynamicEventLoader {
  private static readonly EVENTS_DIR = join(__dirname, '../events');

  /**
   * Load all event handlers dynamically and register them in the container
   */
  static async loadEventHandlers(container: Container): Promise<EventHandlerInfo[]> {
    Logger.debug('Loading event handlers dynamically...');

    const eventHandlers: EventHandlerInfo[] = [];

    try {
      const files = await this.getEventHandlerFiles();
      Logger.debug(`Found ${files.length} potential event handler files`);

      for (const file of files) {
        try {
          const handlerInfo = await this.loadEventHandler(file, container);
          if (handlerInfo) {
            eventHandlers.push(handlerInfo);
          }
        }
        catch (error) {
          Logger.error(`❌ Failed to load event handler from ${file}:`, error);
        }
      }

      Logger.info(`✅ Loaded ${eventHandlers.length} event handlers`);
      return eventHandlers;
    }
    catch (error) {
      Logger.error('❌ Failed to load event handlers:', error);
      return [];
    }
  }

  /**
   * Get all TypeScript files in the events directory
   */
  private static async getEventHandlerFiles(): Promise<string[]> {
    const files: string[] = [];

    try {
      const entries = await readdir(this.EVENTS_DIR);

      for (const entry of entries) {
        const fullPath = join(this.EVENTS_DIR, entry);
        const stats = await stat(fullPath);

        if (
          (stats.isFile() && !entry.endsWith('.js')) ||
          !entry.endsWith('.ts') ||
          entry.endsWith('.d.ts')
        ) {
          // Skip certain files
          if (this.shouldSkipFile(entry)) {
            continue;
          }
          files.push(fullPath);
        }
      }
    }
    catch (error) {
      Logger.warn('⚠️ Could not read events directory:', error);
    }

    return files;
  }

  /**
   * Check if a file should be skipped
   */
  private static shouldSkipFile(filename: string): boolean {
    const skipFiles = ['EventBus.ts', 'TestInterfaces.ts', 'index.ts'];
    return skipFiles.includes(filename);
  }

  /**
   * Load a single event handler from file
   */
  private static async loadEventHandler(
    filePath: string,
    container: Container,
  ): Promise<EventHandlerInfo | null> {
    try {
      // Convert file path to module URL
      const moduleUrl = `file://${filePath}`;
      const module = await import(moduleUrl);

      // Find the event handler class
      const EventHandlerClass = this.findEventHandlerClass(module);
      if (!EventHandlerClass) {
        Logger.debug(`⏭️  Skipping ${filePath} - no event handler class found`);
        return null;
      }

      // Check if it implements the event handler interface
      if (!this.isEventHandler(EventHandlerClass)) {
        Logger.debug(`⏭️  Skipping ${filePath} - does not implement IDiscordEventHandler`);
        return null;
      }

      // Bind the class to container temporarily to create instance
      const tempSymbol = Symbol.for(`temp_${EventHandlerClass.name}`);
      container.bind(tempSymbol).to(EventHandlerClass);
      const instance = container.get(tempSymbol) as IDiscordEventHandler;

      if (!this.hasRequiredMethods(instance)) {
        Logger.warn(`⚠️ Event handler ${EventHandlerClass.name} missing required methods`);
        return null;
      }

      // Register in container for dependency injection
      const symbolName = this.createSymbolName(instance.eventName);
      const symbol = Symbol.for(symbolName);
      container.bind(symbol).toConstantValue(instance);
      container.bind(TYPES.DiscordEventHandler).toConstantValue(instance);

      Logger.debug(`  ✓ Loaded: ${instance.eventName} (${EventHandlerClass.name})`);

      return {
        eventName: instance.eventName,
        handler: instance,
        once: instance.once || false,
      };
    }
    catch (error) {
      Logger.error(`❌ Error loading event handler from ${filePath}:`, error);
      return null;
    }
  }

  /**
   * Find the event handler class in the module
   */
  private static findEventHandlerClass(module: { default: Newable<IDiscordEventHandler> }) {
    // Look for default export first
    if (module.default && typeof module.default === 'function') {
      return module.default;
    }

    // Look for named exports that look like event handlers
    for (const [key, value] of Object.entries(module)) {
      if (typeof value === 'function' && key.includes('EventHandler')) {
        return value;
      }
    }

    return null;
  }

  /**
   * Check if a class implements the event handler interface
   */
  private static isEventHandler(EventHandlerClass: Newable<IDiscordEventHandler>): boolean {
    try {
      // Create a temporary instance to check interface
      const tempInstance = new EventHandlerClass();
      return (
        typeof tempInstance.eventName === 'string' && typeof tempInstance.execute === 'function'
      );
    }
    catch {
      return false;
    }
  }

  /**
   * Check if instance has required methods
   */
  private static hasRequiredMethods(instance: IDiscordEventHandler): boolean {
    return typeof instance.eventName === 'string' && typeof instance.execute === 'function';
  }

  /**
   * Create a symbol name for the event handler
   */
  private static createSymbolName(eventName: string): string {
    return `DiscordEventHandler_${eventName}`;
  }
}
