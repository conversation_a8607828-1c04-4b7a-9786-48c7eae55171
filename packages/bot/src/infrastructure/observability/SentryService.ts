/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import * as Sentry from '@sentry/node';
import { nodeProfilingIntegration } from '@sentry/profiling-node';
import { inject, injectable } from 'inversify';
import { TYPES } from '../../shared/types/TYPES.js';
import { Logger } from '../../shared/utils/Logger.js';

/**
 * Sentry Integration Service
 *
 * Provides centralized error tracking, performance monitoring, and observability
 * Eliminates the need for manual try/catch blocks and logging
 */
@injectable()
export class SentryService {
  private isInitialized = false;

  constructor(@inject(TYPES.ClusterId) private readonly clusterId: string) {}

  /**
   * Initialize Sentry with proper configuration
   */
  initialize(): void {
    if (this.isInitialized) {
      Logger.debug('Sentry already initialized, skipping...');
      return;
    }

    const sentryDsn = process.env.SENTRY_DSN;
    if (!sentryDsn) {
      Logger.warn('⚠️ SENTRY_DSN not configured, skipping Sentry initialization');
      return;
    }

    try {
      Sentry.init({
        dsn: sentryDsn,
        environment: process.env.NODE_ENV || 'development',
        release: process.env.npm_package_version || 'unknown',

        // Enhanced performance monitoring
        tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
        profilesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,

        // Add cluster context
        initialScope: {
          tags: {
            cluster: this.clusterId,
            service: 'interchat-bot',
          },
          contexts: {
            cluster: {
              id: this.clusterId,
              type: 'bot-cluster',
            },
          },
        },

        // Integrations
        integrations: [
          nodeProfilingIntegration(),
        ],

        // Enhanced error filtering
        beforeSend(event, hint) {
          // Filter out common Discord.js network errors that aren't actionable
          if (hint?.originalException) {
            const error = hint.originalException;
            if (error instanceof Error) {
              // Filter Discord API rate limits (handled automatically)
              if (error.message.includes('Rate limited') ||
                  error.message.includes('429')) {
                return null;
              }

              // Filter temporary network issues
              if (error.message.includes('ECONNRESET') ||
                  error.message.includes('ETIMEDOUT') ||
                  error.message.includes('Network request failed')) {
                return null;
              }

              // Filter Discord gateway reconnections (normal behavior)
              if (error.message.includes('WebSocket was closed') ||
                  error.message.includes('Connection terminated')) {
                return null;
              }
            }
          }

          return event;
        },

        // Enhanced breadcrumbs
        beforeBreadcrumb(breadcrumb) {
          // Filter out noisy HTTP requests
          if (breadcrumb.category === 'http' && breadcrumb.data?.url) {
            const url = breadcrumb.data.url as string;
            if (url.includes('discord.com/api')) {
              // Only keep failed Discord API requests
              if (breadcrumb.data.status_code && breadcrumb.data.status_code >= 400) {
                return breadcrumb;
              }
              return null;
            }
          }

          return breadcrumb;
        },
      });

      this.isInitialized = true;
      Logger.info(`✅ Sentry initialized for cluster ${this.clusterId}`);
    }
    catch (error) {
      Logger.error('Failed to initialize Sentry:', error);
    }
  }

  /**
   * Capture exception with additional context
   */
  captureException(error: unknown, context?: Record<string, unknown>): string {
    return Sentry.captureException(error, {
      tags: {
        cluster: this.clusterId,
        ...context?.tags as Record<string, string>,
      },
      extra: context,
    });
  }

  /**
   * Capture message with level
   */
  captureMessage(message: string, level: Sentry.SeverityLevel = 'info', context?: Record<string, unknown>): string {
    return Sentry.captureMessage(message, {
      level,
      tags: {
        cluster: this.clusterId,
        ...context?.tags as Record<string, string>,
      },
      extra: context,
    });
  }

  /**
   * Execute operation with isolated Sentry scope
   */
  withScope<T>(callback: (scope: Sentry.Scope) => T): T {
    return Sentry.withScope(callback);
  }

  /**
   * Execute async operation with isolated Sentry scope
   */
  async withScopeAsync<T>(callback: (scope: Sentry.Scope) => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      Sentry.withScope(async (scope) => {
        try {
          const result = await callback(scope);
          resolve(result);
        }
        catch (error) {
          reject(error);
        }
      });
    });
  }

  /**
   * Add user context for errors
   */
  setUserContext(userId: string, username?: string): void {
    Sentry.setUser({
      id: userId,
      username,
    });
  }

  /**
   * Add extra context
   */
  setContext(key: string, context: Record<string, unknown>): void {
    Sentry.setContext(key, context);
  }

  /**
   * Start a new span for performance monitoring
   */
  startSpan<T>(name: string, op: string, callback: (span: Sentry.Span) => T): T {
    return Sentry.startSpan({
      name,
      op,
      attributes: {
        cluster: this.clusterId,
      },
    }, callback);
  }

  /**
   * Wrap async functions with automatic error capture and performance monitoring
   */
  wrapAsync<T extends (...args: unknown[]) => Promise<unknown>>(
    fn: T,
    options?: {
      name?: string;
      op?: string;
      context?: Record<string, unknown>;
    },
  ): T {
    return (async (...args: Parameters<T>) =>
      await this.startSpan(
        options?.name || fn.name || 'async-operation',
        options?.op || 'function',
        async (span) => {
          try {
            if (options?.context) {
              this.setContext('operation', options.context);
            }

            const result = await fn(...args);
            span.setStatus({ code: 1 }); // ok
            return result;
          }
          catch (error) {
            span.setStatus({ code: 2 }); // error
            this.captureException(error, {
              function: fn.name,
              args: JSON.stringify(args).substring(0, 1000), // Truncate large args
              ...options?.context,
            });
            throw error;
          }
        },
      )
    ) as T;
  }

  /**
   * Get the currently active span
   */
  getActiveSpan(): Sentry.Span | undefined {
    return Sentry.getActiveSpan();
  }

  /**
   * Execute callback with a specific span as active
   */
  withActiveSpan<T>(span: Sentry.Span | null, callback: () => T): T {
    return Sentry.withActiveSpan(span, callback);
  }

  /**
   * Start an inactive span that must be manually ended
   */
  startInactiveSpan(name: string, op: string): Sentry.Span {
    return Sentry.startInactiveSpan({
      name,
      op,
      attributes: {
        cluster: this.clusterId,
      },
    });
  }

  /**
   * Add breadcrumb for debugging
   */
  addBreadcrumb(message: string, category?: string, level?: Sentry.SeverityLevel): void {
    Sentry.addBreadcrumb({
      message,
      category: category || 'custom',
      level: level || 'info',
      timestamp: Date.now() / 1000,
    });
  }

  /**
   * Set tag for all future events
   */
  setTag(key: string, value: string): void {
    Sentry.setTag(key, value);
  }

  /**
   * Set multiple tags for all future events
   */
  setTags(tags: Record<string, string>): void {
    Sentry.setTags(tags);
  }

  /**
   * Flush pending events (useful for graceful shutdown)
   */
  async flush(timeout = 5000): Promise<boolean> {
    if (!this.isInitialized) return true;

    try {
      return await Sentry.flush(timeout);
    }
    catch (error) {
      Logger.error('Error flushing Sentry events:', error);
      return false;
    }
  }

  /**
   * Close Sentry client
   */
  async close(timeout = 5000): Promise<boolean> {
    if (!this.isInitialized) return true;

    try {
      return await Sentry.close(timeout);
    }
    catch (error) {
      Logger.error('Error closing Sentry client:', error);
      return false;
    }
  }
}
