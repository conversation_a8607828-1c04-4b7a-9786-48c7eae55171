/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import type { CacheType, Interaction } from 'discord.js';
import { inject, injectable } from 'inversify';
import type { InteractionRegistry } from '../../presentation/interactions/InteractionRegistry.js';
import { ComponentContext } from '../../shared/context/index.js';
import { TYPES } from '../../shared/types/TYPES.js';
import { Logger } from '../../shared/utils/Logger.js';
import { CommandHandler } from '../handlers/CommandHandler.js';
import type { IDiscordEventHandler } from '../loaders/DynamicEventLoader.js';

/**
 * Modern InteractionCreate Event Handler
 *
 * This event handler routes all Discord interactions through our modern system:
 * - Commands (slash/context) → HybridCommandHandler
 * - Buttons/Modals/Selects → InteractionRegistry
 */
@injectable()
export class InteractionCreateEventHandler implements IDiscordEventHandler {
  readonly eventName = 'interactionCreate';
  readonly once = false;

  constructor(
    @inject(TYPES.HybridCommandHandler) private readonly handler: CommandHandler,
    @inject(TYPES.InteractionRegistry) private readonly interactionRegistry: InteractionRegistry,
  ) {}

  /**
   * Handle Discord interaction events
   */
  async execute(interaction: Interaction<CacheType>): Promise<void> {
    try {
      // Handle command interactions (slash commands, context menus, autocomplete)
      if (
        interaction.isChatInputCommand() ||
        interaction.isContextMenuCommand() ||
        interaction.isAutocomplete()
      ) {
        await this.handler.handleCommand(interaction);
        return;
      }

      // Handle component interactions (buttons, modals, select menus)
      if (interaction.isMessageComponent() || interaction.isModalSubmit()) {
        const context = new ComponentContext(interaction);
        await this.interactionRegistry.routeInteraction(context, interaction);
        return;
      }

      // Log unhandled interaction types for debugging
      Logger.info(`⚠️ Unhandled interaction type: ${interaction.type}`);
    }
    catch (error) {
      Logger.error('Error in InteractionCreateEventHandler:', error);
    }
  }

  /**
   * Get diagnostic information
   */
  getDiagnostics() {
    return {
      handler: 'HybridCommandHandler',
      ...this.handler.getDiagnostics(),
    };
  }
}
