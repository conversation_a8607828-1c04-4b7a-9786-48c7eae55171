/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the    return {
      eventId: payload.eventId,
      type: payload.type,
      aggregateId: payload.aggregateId,
      version: payload.version,
      clusterId: payload.clusterId,
      occurredAt: new Date(payload.occurredAt),
      shouldBroadcast: payload.shouldBroadcast,
      ...(typeof payload.data === 'object' && payload.data !== null ? payload.data : {}),
    } as DomainEvent;or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { inject, injectable } from 'inversify';
import { Redis } from 'ioredis';
import { type IEventBus } from '../../domain/events/IEventBus.js';
import { TYPES } from '../../shared/types/TYPES.js';
import { Logger } from '../../shared/utils/Logger.js';
import { type DomainEvent } from './EventBus.js';

/**
 * Redis-backed Event Bus for Cross-Cluster Communication
 *
 * This implementation handles broadcasting events across multiple cluster processes.
 * Each cluster can publish events that other clusters will receive and process.
 *
 * Features:
 * - Cross-cluster event broadcasting via Redis pub/sub
 * - Event deduplication to prevent loops
 * - Automatic retry on Redis failures
 * - Cluster-aware event routing
 */
@injectable()
export class RedisEventBus implements IEventBus {
  private readonly CHANNEL_PREFIX = 'interchat:events:';
  private readonly publisher: Redis;
  private readonly subscriber: Redis;
  private readonly eventHandlers = new Map<string, Set<(event: DomainEvent) => Promise<void>>>();
  private readonly processedEvents = new Set<string>(); // For deduplication
  private readonly maxProcessedEvents = 10000; // Prevent memory leak

  constructor(
    @inject(TYPES.RedisClient) redisClient: Redis,
  ) {
    // Create separate Redis connections for pub/sub
    this.publisher = redisClient.duplicate();
    this.subscriber = redisClient.duplicate();

    this.setupSubscriber();
  }

  /**
   * Publish event to other clusters
   */
  async publish<T extends DomainEvent>(event: T): Promise<void> {
    try {
      const channel = this.getChannelName(event.type);
      const payload = {
        eventId: event.eventId,
        type: event.type,
        aggregateId: event.aggregateId,
        version: event.version,
        clusterId: event.clusterId,
        occurredAt: event.occurredAt.toISOString(),
        shouldBroadcast: event.shouldBroadcast,
        data: this.serializeEvent(event),
      };

      await this.publisher.publish(channel, JSON.stringify(payload));

      Logger.debug(`Published event to cluster channel: ${event.type}`, {
        eventId: event.eventId,
        channel,
        clusterId: event.clusterId,
      });
    }
    catch (error) {
      Logger.error('Failed to publish event to Redis', {
        eventType: event.type,
        eventId: event.eventId,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  /**
   * Subscribe to event type
   */
  async subscribe<T extends DomainEvent>(
    eventType: string,
    handler: (event: T) => Promise<void>,
  ): Promise<void> {
    if (!this.eventHandlers.has(eventType)) {
      this.eventHandlers.set(eventType, new Set());

      // Subscribe to Redis channel for this event type
      const channel = this.getChannelName(eventType);
      await this.subscriber.subscribe(channel);

      Logger.info(`Subscribed to cross-cluster event: ${eventType}`, { channel });
    }

    this.eventHandlers.get(eventType)!.add(handler as (event: DomainEvent) => Promise<void>);
  }

  /**
   * Unsubscribe from event type
   */
  async unsubscribe(
    eventType: string,
    handler: (event: DomainEvent) => Promise<void>,
  ): Promise<void> {
    const handlers = this.eventHandlers.get(eventType);
    if (handlers) {
      handlers.delete(handler);

      if (handlers.size === 0) {
        this.eventHandlers.delete(eventType);
        const channel = this.getChannelName(eventType);
        await this.subscriber.unsubscribe(channel);

        Logger.info(`Unsubscribed from cross-cluster event: ${eventType}`, { channel });
      }
    }
  }

  /**
   * Setup Redis subscriber to handle incoming events from other clusters
   */
  private setupSubscriber(): void {
    this.subscriber.on('message', async (channel: string, message: string) => {
      try {
        const payload = JSON.parse(message);
        const eventType = this.getEventTypeFromChannel(channel);

        // Prevent processing our own events (deduplication)
        const currentClusterId = process.env.CLUSTER_ID || '0';
        if (payload.clusterId === currentClusterId || this.processedEvents.has(payload.eventId)) {
          return;
        }

        // Track processed events (with cleanup to prevent memory leaks)
        this.processedEvents.add(payload.eventId);
        if (this.processedEvents.size > this.maxProcessedEvents) {
          const toDelete = Array.from(this.processedEvents).slice(0, 1000);
          toDelete.forEach((id) => this.processedEvents.delete(id));
        }

        // Reconstruct the domain event
        const event = this.deserializeEvent(payload);

        // Call all handlers for this event type
        const handlers = this.eventHandlers.get(eventType);
        if (handlers) {
          await Promise.all(
            Array.from(handlers).map(async (handler) => {
              try {
                await handler(event);
              }
              catch (error) {
                Logger.error(`Event handler failed for ${eventType}`, {
                  eventId: event.eventId,
                  handler: handler.name,
                  error: error instanceof Error ? error.message : String(error),
                });
              }
            }),
          );
        }

        Logger.debug(`Processed cross-cluster event: ${eventType}`, {
          eventId: payload.eventId,
          sourceCluster: payload.clusterId,
          currentCluster: process.env.CLUSTER_ID || '0',
        });
      }
      catch (error) {
        Logger.error('Failed to process Redis event message', {
          channel,
          error: error instanceof Error ? error.message : String(error),
        });
      }
    });

    this.subscriber.on('error', (error) => {
      Logger.error('Redis subscriber error', {
        error: error instanceof Error ? error.message : String(error),
      });
    });

    this.subscriber.on('connect', () => {
      Logger.info('Redis subscriber connected for cross-cluster events');
    });

    this.subscriber.on('disconnect', () => {
      Logger.warn('Redis subscriber disconnected');
    });
  }

  /**
   * Generate Redis channel name for event type
   */
  private getChannelName(eventType: string): string {
    return `${this.CHANNEL_PREFIX}${eventType}`;
  }

  /**
   * Extract event type from Redis channel name
   */
  private getEventTypeFromChannel(channel: string): string {
    return channel.replace(this.CHANNEL_PREFIX, '');
  }

  /**
   * Serialize domain event for Redis transmission
   */
  private serializeEvent(event: DomainEvent): Record<string, unknown> {
    // Create a plain object with all event properties
    const serialized: Record<string, unknown> = {};

    // Copy all enumerable properties
    for (const [key, value] of Object.entries(event)) {
      if (key !== 'occurredAt' && key !== 'eventId' && key !== 'clusterId') {
        serialized[key] = value;
      }
    }

    return serialized;
  }

  /**
   * Deserialize event data from Redis
   */
  private deserializeEvent(payload: { [key: string]: unknown }): DomainEvent {
    // This is a simplified reconstruction - in a real implementation,
    // you'd want a proper event factory that knows how to reconstruct
    // specific event types with their proper prototypes
    return {
      eventId: payload.eventId,
      type: payload.type,
      aggregateId: payload.aggregateId,
      version: payload.version,
      clusterId: payload.clusterId,
      occurredAt: new Date(payload.occurredAt as string),
      shouldBroadcast: payload.shouldBroadcast,
      ...(typeof payload.data === 'object' && payload.data !== null ? payload.data : {}),
    } as DomainEvent;
  }

  /**
   * Cleanup resources
   */
  async dispose(): Promise<void> {
    await this.subscriber.disconnect();
    await this.publisher.disconnect();
    this.eventHandlers.clear();
    this.processedEvents.clear();

    Logger.info('Redis event bus disposed');
  }
}
