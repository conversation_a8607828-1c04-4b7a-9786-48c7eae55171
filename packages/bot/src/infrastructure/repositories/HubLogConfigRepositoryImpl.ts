/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { PrismaClient } from '../../../../../build/generated/prisma/client/index.js';
import { injectable } from 'inversify';
import { HubLogConfig, type HubLogConfigCreationData, type HubLogConfigUpdateData } from '../../domain/entities/HubLogConfig.js';
import type { HubLogConfigRepository } from '../../domain/repositories/HubLogConfigRepository.js';
import { Logger } from '../../shared/utils/Logger.js';

@injectable()
export class HubLogConfigRepositoryImpl implements HubLogConfigRepository {
  private readonly prisma = new PrismaClient();

  async getByHubId(hubId: string): Promise<HubLogConfig | null> {
    const hubLogConfig = await this.prisma.hubLogConfig.findUnique({
      where: { hubId },
    });

    if (!hubLogConfig) {
      return null;
    }

    return HubLogConfig.fromPrisma(hubLogConfig);
  }

  async create(data: HubLogConfigCreationData): Promise<HubLogConfig> {
    try {
      const hubLogConfig = await this.prisma.hubLogConfig.create({
        data,
      });

      return HubLogConfig.fromPrisma(hubLogConfig);
    }
    catch (error) {
      Logger.error('Failed to create hub log config', { hubId: data.hubId, error });
      throw error;
    }
  }

  async update(hubId: string, data: HubLogConfigUpdateData): Promise<HubLogConfig> {
    const hubLogConfig = await this.prisma.hubLogConfig.update({
      where: { hubId },
      data,
    });

    return HubLogConfig.fromPrisma(hubLogConfig);
  }

  async delete(hubId: string): Promise<void> {
    try {
      await this.prisma.hubLogConfig.delete({
        where: { hubId },
      });
    }
    catch (error) {
      Logger.error('Failed to delete hub log config', { hubId, error });
      throw error;
    }
  }

  async upsert(hubId: string, data: HubLogConfigUpdateData): Promise<HubLogConfig> {
    const hubLogConfig = await this.prisma.hubLogConfig.upsert({
      where: { hubId },
      create: { hubId, ...data },
      update: data,
    });

    return HubLogConfig.fromPrisma(hubLogConfig);
  }
}
