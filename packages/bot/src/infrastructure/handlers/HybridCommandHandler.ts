/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { injectable } from 'inversify';
import type {
  ChatInputCommandInteraction,
  ContextMenuCommandInteraction,
  AutocompleteInteraction,
} from 'discord.js';
import { CommandBridge } from '../bridge/CommandBridge.js';
import { Logger } from '../../shared/utils/Logger.js';

/**
 * Modern Command Handler
 *
 * Handles command execution using the new CommandRegistry system.
 * This is part of the modern architecture and does not depend on legacy code.
 *
 * NOTE: The "Hybrid" name is kept for compatibility during migration, but this
 * handler only uses the new command system.
 */
@injectable()
export class HybridCommandHandler {
  private commandBridge = new CommandBridge();

  /**
   * Handle command interactions using the modern command system only
   */
  async handleCommand(
    interaction:
      | ChatInputCommandInteraction
      | ContextMenuCommandInteraction
      | AutocompleteInteraction,
  ): Promise<void> {
    try {
      // Handle autocomplete separately
      if (interaction.isAutocomplete()) {
        await this.handleAutocomplete(interaction);
        return;
      }

      // Handle slash commands and context menu commands
      if (interaction.isChatInputCommand() || interaction.isContextMenuCommand()) {
        const handled = await this.executeModernCommand(interaction);
        if (handled) {
          Logger.debug(`Command '${interaction.commandName}' executed successfully`);
        }
        else {
          Logger.warn(`Command '${interaction.commandName}' not found in modern system`);
        }
      }
    }
    catch (error) {
      Logger.error(`Error handling command '${interaction.commandName}':`, error);
    }
  }

  /**
   * Execute command using the modern CommandRegistry system
   */
  private async executeModernCommand(
    interaction: ChatInputCommandInteraction | ContextMenuCommandInteraction,
  ): Promise<boolean> {
    try {
      if (!this.commandBridge.isAvailable()) {
        Logger.debug('🔧 Command bridge not available');
        return false;
      }

      if (!this.commandBridge.hasCommand(interaction.commandName)) {
        Logger.debug(`🔍 Command '${interaction.commandName}' not found in modern registry`);
        return false;
      }

      // Currently, CommandBridge only supports ChatInputCommandInteraction
      // Context menu commands will need separate handling when implemented
      if (interaction.isChatInputCommand()) {
        await this.commandBridge.executeCommand(interaction);
        return true;
      }
      else {
        Logger.debug(
          `⚠️ Context menu command '${interaction.commandName}' not yet supported in modern system`,
        );
        return false;
      }
    }
    catch (error) {
      Logger.error(`💥 Error in command system for '${interaction.commandName}':`, error);
      return false;
    }
  }

  /**
   * Handle autocomplete interactions using the modern system
   */
  private async handleAutocomplete(interaction: AutocompleteInteraction): Promise<void> {
    try {
      const handled = await this.commandBridge.handleAutocomplete(interaction);
      if (handled) {
        Logger.debug(`✅ Autocomplete for '${interaction.commandName}' handled successfully`);
      }
      else {
        Logger.debug(`⚠️ Autocomplete for '${interaction.commandName}' not available`);
        // Send empty response to prevent Discord timeout
        await interaction.respond([]);
      }
    }
    catch (error) {
      Logger.error(`❌ Error handling autocomplete for '${interaction.commandName}':`, error);
      // Send empty response to prevent Discord timeout
      await interaction.respond([]);
    }
  }

  /**
   * Get diagnostic information about the modern command system
   */
  getDiagnostics(): {
    systemAvailable: boolean;
    availableCommands: string[];
    bridgeStatus: string;
    commandCount: number;
  } {
    const commands = this.commandBridge.getNewCommands();
    return {
      systemAvailable: this.commandBridge.isAvailable(),
      availableCommands: commands,
      bridgeStatus: this.commandBridge.isAvailable() ? 'Ready' : 'Not Available',
      commandCount: commands.length,
    };
  }

  /**
   * Check if a specific command is available in the modern system
   */
  hasCommand(commandName: string): boolean {
    return this.commandBridge.hasCommand(commandName);
  }

  /**
   * Get the command bridge instance (for testing/debugging)
   */
  getCommandBridge(): CommandBridge {
    return this.commandBridge;
  }
}
