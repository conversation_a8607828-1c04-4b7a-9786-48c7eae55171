/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { ModernInterChatClient } from './core/ModernClient.js';
import 'dotenv/config';
import { Logger } from './shared/utils/Logger.js';

/**
 * Modern InterChat Client Entry Point
 *
 * This replaces the legacy src/client.ts with a clean implementation
 * that uses the new DI container and modern architecture.
 */

// Create the modern client instance
const client = new ModernInterChatClient();

// Debug logging for development
if (process.env.NODE_ENV === 'development') {
  client.on('debug', (debug: string) => Logger.debug(`${debug}`));
  client.rest.on('restDebug', (debug: string) => Logger.debug(`🌐 REST: ${debug}`));
}

// Rate limit logging
client.rest.on('rateLimited', (data) => {
  Logger.warn('⏰ Rate limited:', data);
});

// // Global error handling for this cluster process
// process.on('unhandledRejection', (reason, promise) => {
//   Logger.error(
//     `❌ Cluster ${(client as any).modernCluster?.id || 'unknown'} - Unhandled Rejection:`,
//     reason,
//   );
// });

// process.on('uncaughtException', (error) => {
//   Logger.error(
//     `❌ Cluster ${(client as any).modernCluster?.id || 'unknown'} - Uncaught Exception:`,
//     error,
//   );
//   process.exit(1);
// });

// Start the client
client.start().catch((error) => {
  Logger.error('❌ Failed to start client:', error);
  process.exit(1);
});

export { client };
