/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { captureException } from '@sentry/node';
import 'dotenv/config';
import { InterChatClient } from './core/InterChatClient.js';
import { Logger } from './shared/utils/Logger.js';

/**
 * InterChat Client Entry Point
 */

const client = new InterChatClient();

// Debug logging for development
if (process.env.NODE_ENV === 'development') {
  client.on('debug', (debug: string) => Logger.debug(`${debug}`));
  client.rest.on('restDebug', (debug: string) => Logger.debug(`🌐 REST: ${debug}`));
}

// Rate limit logging
client.rest.on('rateLimited', (data) => {
  Logger.warn('⏰ Rate limited:', data);
});

process.on('unhandledRejection', (reason) => {
  const clusterId = client.cluster?.id || 'unknown';
  const errorMessage = `Cluster ${clusterId} - Unhandled Rejection`;

  Logger.error(errorMessage, reason);
  captureException(reason);
});

process.on('uncaughtException', (error) => {
  const clusterId = client.cluster?.id || 'unknown';
  const errorMessage = `Cluster ${clusterId} - Uncaught Exception`;

  Logger.error(errorMessage, error);
  captureException(error);
});

client.start();
