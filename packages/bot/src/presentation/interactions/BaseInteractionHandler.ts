/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * its of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { injectable } from 'inversify';
import {
  ButtonInteraction,
  ModalSubmitInteraction,
  StringSelectMenuInteraction,
  UserSelectMenuInteraction,
  RoleSelectMenuInteraction,
  ChannelSelectMenuInteraction,
  MentionableSelectMenuInteraction,
} from 'discord.js';
import type { Context } from '../../shared/context/Context.js';
import { CustomID } from '../../shared/utils/CustomID.js';

/**
 * Base class for all interaction handlers
 */
@injectable()
export abstract class BaseInteractionHandler {
  /**
   * The unique identifier for this interaction handler
   * Used for routing interactions to the correct handler
   */
  abstract readonly handlerId: string;

  /**
   * Handle a button interaction
   */
  async handleButton?(_context: Context, _interaction: ButtonInteraction): Promise<void> {
    throw new Error(`Button interaction not supported by ${this.constructor.name}`);
  }

  /**
   * Handle a modal submit interaction
   */
  async handleModal?(_context: Context, _interaction: ModalSubmitInteraction): Promise<void> {
    throw new Error(`Modal interaction not supported by ${this.constructor.name}`);
  }

  /**
   * Handle a string select menu interaction
   */
  async handleStringSelect?(
    _context: Context,
    _interaction: StringSelectMenuInteraction,
  ): Promise<void> {
    throw new Error(`String select interaction not supported by ${this.constructor.name}`);
  }

  /**
   * Handle a user select menu interaction
   */
  async handleUserSelect?(
    _context: Context,
    _interaction: UserSelectMenuInteraction,
  ): Promise<void> {
    throw new Error(`User select interaction not supported by ${this.constructor.name}`);
  }

  /**
   * Handle a role select menu interaction
   */
  async handleRoleSelect?(
    _context: Context,
    _interaction: RoleSelectMenuInteraction,
  ): Promise<void> {
    throw new Error(`Role select interaction not supported by ${this.constructor.name}`);
  }

  /**
   * Handle a channel select menu interaction
   */
  async handleChannelSelect?(
    _context: Context,
    _interaction: ChannelSelectMenuInteraction,
  ): Promise<void> {
    throw new Error(`Channel select interaction not supported by ${this.constructor.name}`);
  }

  /**
   * Handle a mentionable select menu interaction
   */
  async handleMentionableSelect?(
    _context: Context,
    _interaction: MentionableSelectMenuInteraction,
  ): Promise<void> {
    throw new Error(`Mentionable select interaction not supported by ${this.constructor.name}`);
  }

  /**
   * Check if this handler can handle the given custom ID
   * Default implementation checks if customId starts with handlerId
   */
  canHandle(customId: string): boolean {
    return customId.startsWith(this.handlerId);
  }

  /**
   * Extract parameters from a custom ID
   * Format: handlerId:param1:param2:param3
   */
  public extractParams(customId: string): string[] {
    const parts = customId.split(':');
    return parts.slice(1); // Remove the handler ID part
  }

  /**
   * Build a custom ID with parameters using the modern CustomID utility
   * This ensures compression when needed to stay under Discord's limits
   */
  public buildCustomId(...params: string[]): string {
    return CustomID.fromHandler(this.handlerId, ...params).toString();
  }

  /**
   * Build a simple custom ID without compression (for backwards compatibility)
   */
  public buildSimpleCustomId(...params: string[]): string {
    return [this.handlerId, ...params].join(':');
  }
}
