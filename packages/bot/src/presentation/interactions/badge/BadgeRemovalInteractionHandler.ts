/**
 * Copyright (c) 2024 InterChat
 *
 * This file is part of InterChat, licensed under the AGPL-3.0 license.
 * See the LICENSE file in the root directory for license information.
 */

/**
 * Badge Removal Interaction Handler
 *
 * Handles button interactions for badge removal confirmation.
 * Demonstrates Discord components integration with the new architecture.
 */

import { EmbedBuilder, MessageFlags } from 'discord.js';
import { inject, injectable } from 'inversify';
import type { IUserRepository } from '../../../domain/repositories/UserRepositories.js';
import { ComponentContext } from '../../../shared/context/ComponentContext.js';
import { TYPES } from '../../../shared/types/TYPES.js';
import { Logger } from '../../../shared/utils/Logger.js';
import { BaseInteractionHandler } from '../BaseInteractionHandler.js';

@injectable()
export class BadgeRemovalInteractionHandler extends BaseInteractionHandler {
  readonly handlerId = 'badge_remove';

  constructor(
    @inject(TYPES.UserRepository)
    private readonly userRepository: IUserRepository,
  ) {
    super();
  }

  async handleButton(ctx: ComponentContext): Promise<void> {
    // Parse custom ID to get action and parameters
    const { action, targetUserId, badgeType } = ctx.customId.params;

    if (action === 'confirm') {
      await this.handleConfirmRemoval(ctx, targetUserId, badgeType);
    }
    else if (action === 'cancel') {
      await this.handleCancelRemoval(ctx);
    }
    else {
      await ctx.reply({
        content: '❌ Unknown action.',
        flags: [MessageFlags.Ephemeral],
      });
    }
  }

  /**
   * Handle badge removal confirmation
   */
  private async handleConfirmRemoval(
    interaction: ComponentContext,
    targetUserId: string,
    badgeType: string,
  ): Promise<void> {
    try {
      // Get target user info
      const targetUser = await interaction.client.users.fetch(targetUserId);

      if (!targetUser) {
        await interaction.reply({
          content: '❌ Could not find the target user.',
          flags: [MessageFlags.Ephemeral],
        });
        return;
      }

      // Check if user exists in our system
      const user = await this.userRepository.findById(targetUserId);

      if (!user) {
        await interaction.reply({
          content: '❌ User not found in the InterChat system.',
          flags: [MessageFlags.Ephemeral],
        });
        return;
      }

      // In a real implementation, you would:
      // 1. Check if user has this badge
      // 2. Remove the badge from the user's badge collection
      // 3. Log the action for audit purposes

      // Create success embed
      const embed = new EmbedBuilder()
        .setColor('#00FF00')
        .setTitle('Badge Removed Successfully')
        .setDescription(
          `✅ Successfully removed **${this.getBadgeName(badgeType)}** badge from ${targetUser.username}\n\n` +
            `**Moderator:** ${interaction.user.username}\n` +
            `**Target:** ${targetUser.username} (${targetUserId})\n` +
            '**Action:** Badge removal confirmed',
        )
        .setTimestamp();

      // Update the original message to show success
      await interaction.editReply({
        embeds: [embed],
        components: [], // Remove buttons after action
      });
    }
    catch (error) {
      Logger.error('Error removing badge:', error);

      await interaction.reply({
        content: '❌ An error occurred while removing the badge.',
        flags: [MessageFlags.Ephemeral],
      });
    }
  }

  /**
   * Handle badge removal cancellation
   */
  private async handleCancelRemoval(ctx: ComponentContext): Promise<void> {
    const embed = new EmbedBuilder()
      .setColor('#FF0000')
      .setTitle('Badge Removal Cancelled')
      .setDescription('❌ Badge removal has been cancelled.')
      .setTimestamp();

    // Update the original message to show cancellation
    await ctx.editReply({
      embeds: [embed],
      components: [], // Remove buttons after action
    });
  }

  /**
   * Get display name for badge type
   */
  private getBadgeName(badgeType: string): string {
    const badgeNames: Record<string, string> = {
      CHAMPION: '🏆 Champion',
      ARTIST: '🎨 Artist',
      DEVELOPER: '🛠️ Developer',
      SUPPORTER: '🎭 Supporter',
      VIP: '⭐ VIP',
      BETA_TESTER: '🔧 Beta Tester',
    };

    return badgeNames[badgeType] || badgeType;
  }
}
