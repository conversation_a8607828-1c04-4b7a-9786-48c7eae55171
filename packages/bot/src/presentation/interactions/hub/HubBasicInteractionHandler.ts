/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { inject, injectable } from 'inversify';
import { MessageFlags } from 'discord.js';
import { BaseInteractionHandler } from '../BaseInteractionHandler.js';
import { ComponentContext } from '../../../shared/context/index.js';
import { TYPES } from '../../../shared/types/TYPES.js';
import { HubBasicUIService } from '../../../application/services/HubBasicUIService.js';
import type { IHubRepository } from '../../../domain/repositories/HubRepositories.js';

@injectable()
export default class HubBasicInteractionHandler extends BaseInteractionHandler {
  public readonly handlerId = 'hub_basic';

  constructor(
    @inject(TYPES.HubRepository)
    private readonly hubRepository: IHubRepository,
    @inject(TYPES.HubBasicUIService)
    private readonly hubBasicUIService: HubBasicUIService,
  ) {
    super();
  }

  async handleButton(ctx: ComponentContext): Promise<void> {
    const [hubId, action] = ctx.customId.args;

    if (!hubId || !action) {
      await ctx.reply({
        content: '❌ Invalid basic configuration request.',
        flags: [MessageFlags.Ephemeral],
      });
      return;
    }

    // Verify user owns the hub
    const hub = await this.hubRepository.findById(hubId);
    if (!hub || hub.ownerId !== ctx.user.id) {
      await ctx.reply({
        content: '❌ You can only edit hubs you own.',
        flags: [MessageFlags.Ephemeral],
      });
      return;
    }

    switch (action) {
      case 'config':
        await this.handleConfigAction(ctx, hubId);
        break;
      case 'refresh':
        await this.handleRefresh(ctx, hubId);
        break;
      default:
        await ctx.reply({
          content: '❌ Unknown basic configuration action.',
          flags: [MessageFlags.Ephemeral],
        });
        break;
    }
  }

  async handleModal(ctx: ComponentContext): Promise<void> {
    const [hubId, action] = ctx.customId.args;

    if (!hubId || !action) {
      await ctx.reply({
        content: '❌ Invalid basic configuration modal request.',
        flags: [MessageFlags.Ephemeral],
      });
      return;
    }

    // Verify user owns the hub
    const hub = await this.hubRepository.findById(hubId);
    if (!hub || hub.ownerId !== ctx.user.id) {
      await ctx.reply({
        content: '❌ You can only edit hubs you own.',
        flags: [MessageFlags.Ephemeral],
      });
      return;
    }

    switch (action) {
      case 'description_modal':
        await this.handleDescriptionModal(ctx, hubId);
        break;
      case 'cooldown_modal':
        await this.handleCooldownModal(ctx, hubId);
        break;
      default:
        await ctx.reply({
          content: '❌ Unknown basic configuration modal action.',
          flags: [MessageFlags.Ephemeral],
        });
        break;
    }
  }

  private async handleConfigAction(ctx: ComponentContext, hubId: string): Promise<void> {
    const configType = ctx.customId.args[2];

    if (!configType) {
      await ctx.reply({
        content: '❌ Configuration type is required.',
        flags: [MessageFlags.Ephemeral],
      });
      return;
    }

    const hub = await this.hubRepository.findById(hubId);
    if (!hub) {
      await ctx.reply({
        content: '❌ Hub not found.',
        flags: [MessageFlags.Ephemeral],
      });
      return;
    }

    switch (configType) {
      case 'description':
        const descriptionModal = this.hubBasicUIService.buildDescriptionModal(
          hubId,
          hub.description,
        );
        await ctx.showModal(descriptionModal);
        break;
      case 'privacy':
        await hub.update({ private: !hub.isPrivate });
        await this.refreshAndNotify(ctx, hubId, `Privacy set to ${hub.isPrivate ? 'Public' : 'Private'}`);
        break;
      case 'nsfw':
        await hub.update({ nsfw: !hub.isNsfw });
        await this.refreshAndNotify(ctx, hubId, `NSFW content ${hub.isNsfw ? 'disabled' : 'enabled'}`);
        break;
      case 'cooldown':
        const cooldownModal = this.hubBasicUIService.buildAppealCooldownModal(
          hubId,
          hub.appealCooldownHours,
        );
        await ctx.showModal(cooldownModal);
        break;
      case 'lock':
        await hub.update({ locked: !hub.isLocked });
        await this.refreshAndNotify(ctx, hubId, `Hub ${hub.isLocked ? 'unlocked' : 'locked'}`);
        break;
      default:
        await ctx.reply({
          content: '❌ Unknown configuration type.',
          flags: [MessageFlags.Ephemeral],
        });
        break;
    }
  }

  private async handleRefresh(ctx: ComponentContext, hubId: string): Promise<void> {
    const container = await this.hubBasicUIService.buildBasicSettingsContainer(hubId);
    await ctx.editReply({
      components: [container],
      flags: ['IsComponentsV2'],
    });
  }

  private async handleDescriptionModal(ctx: ComponentContext, hubId: string): Promise<void> {
    const description = ctx.getModalFieldValue('description');

    if (!description) {
      await ctx.reply({
        content: '❌ Description is required.',
        flags: [MessageFlags.Ephemeral],
      });
      return;
    }

    const hub = await this.hubRepository.findById(hubId);
    if (!hub) {
      await ctx.reply({
        content: '❌ Hub not found.',
        flags: [MessageFlags.Ephemeral],
      });
      return;
    }

    await hub.update({ description });
    await this.refreshAndNotify(ctx, hubId, 'Description updated successfully');
  }

  private async handleCooldownModal(ctx: ComponentContext, hubId: string): Promise<void> {
    const cooldownStr = ctx.getModalFieldValue('cooldown');

    if (!cooldownStr) {
      await ctx.reply({
        content: '❌ Cooldown is required.',
        flags: [MessageFlags.Ephemeral],
      });
      return;
    }

    const cooldown = parseInt(cooldownStr, 10);
    if (isNaN(cooldown) || cooldown < 1 || cooldown > 8766) {
      await ctx.reply({
        content: '❌ Cooldown must be between 1 and 8766 hours.',
        flags: [MessageFlags.Ephemeral],
      });
      return;
    }

    const hub = await this.hubRepository.findById(hubId);
    if (!hub) {
      await ctx.reply({
        content: '❌ Hub not found.',
        flags: [MessageFlags.Ephemeral],
      });
      return;
    }

    await hub.update({ appealCooldownHours: cooldown });
    await this.refreshAndNotify(ctx, hubId, `Appeal cooldown set to ${cooldown} hours`);
  }

  private async refreshAndNotify(
    ctx: ComponentContext,
    hubId: string,
    message: string,
  ): Promise<void> {
    const container = await this.hubBasicUIService.buildBasicSettingsContainer(hubId);
    await ctx.editReply({
      components: [container],
      flags: ['IsComponentsV2'],
    });

    await ctx.reply({
      content: `✅ ${message}`,
      flags: [MessageFlags.Ephemeral],
    });
  }
}
