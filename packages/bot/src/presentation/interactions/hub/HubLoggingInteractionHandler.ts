/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { inject, injectable, type Container } from 'inversify';
import type { HubLogConfigService } from '../../../application/services/HubLogConfigService.js';
import type { LogConfigType } from '../../../domain/entities/HubLogConfig.js';
import { ComponentContext } from '../../../shared/context/ComponentContext.js';
import { TYPES } from '../../../shared/types/TYPES.js';
import { Logger } from '../../../shared/utils/Logger.js';
import HubLoggingCommandHandler from '../../commands/hub/HubLoggingCommandHandler.js';
import { BaseInteractionHandler } from '../BaseInteractionHandler.js';

@injectable()
export default class HubLoggingInteractionHandler extends BaseInteractionHandler {
  readonly handlerId = 'hubLogging';

  constructor(
    @inject(TYPES.HubLogConfigService)
    private readonly hubLogConfigService: HubLogConfigService,
    @inject(TYPES.Container)
    private readonly container: Container,
  ) {
    super();
  }

  private getHubLoggingCommandHandler(): HubLoggingCommandHandler {
    // Get the command handler dynamically from container
    return this.container.get<HubLoggingCommandHandler>(HubLoggingCommandHandler);
  }

  async handleButton(ctx: ComponentContext): Promise<void> {
    // Check if this is a hub logging related button
    if (ctx.customId.prefix === 'hubLogging') {
      const [userId, hubId, ...rest] = ctx.customId.args;

      // Verify user permission
      if (ctx.user.id !== userId) {
        await ctx.reply({
          content: '❌ You do not have permission to use this button.',
          flags: ['Ephemeral'],
        });
        return;
      }

      // Handle different suffixes
      switch (ctx.customId.suffix) {
        case 'refresh':
          await this.handleRefresh(ctx, hubId, userId);
          return;

        case 'configure':
          if (rest.length > 0) {
            const logType = rest[0] as LogConfigType;
            await this.handleConfigure(ctx, hubId, userId, logType);
          }
          return;

        case 'back':
          await this.handleBack(ctx, hubId, userId);
          return;

        default:
          throw new Error(`Unknown hub logging button suffix: ${ctx.customId.suffix}`);
      }
    }

    throw new Error(`No handler for button: ${ctx.customId}`);
  }

  async handleChannelSelect(ctx: ComponentContext): Promise<void> {
    // Parse the CustomID using the new utility - all CustomIDs are compressed
    const parsed = ctx.customId;

    // Check if this is a hub logging related channel select
    if (parsed.prefix === 'hubLogging' && parsed.suffix === 'channel') {
      const [userId, hubId, logType] = parsed.args;

      // Verify user permission
      if (ctx.user.id !== userId) {
        await ctx.reply({
          content: '❌ You do not have permission to use this menu.',
          flags: ['Ephemeral'],
        });
        return;
      }

      await this.handleChannelSelection(ctx, hubId, userId, logType as LogConfigType, ctx.values);
      return;
    }

    throw new Error(`No handler for channel select menu: ${ctx.customId}`);
  }

  async handleRoleSelect(ctx: ComponentContext): Promise<void> {
    // Check if this is a hub logging related role select
    if (ctx.customId.prefix === 'hubLogging' && ctx.customId.suffix === 'role') {
      const [userId, hubId, logType] = ctx.customId.args;

      // Verify user permission
      if (ctx.user.id !== userId) {
        await ctx.reply({
          content: '❌ You do not have permission to use this menu.',
          flags: ['Ephemeral'],
        });
        return;
      }

      await this.handleRoleSelection(ctx, hubId, userId, logType as LogConfigType, ctx.values);
      return;
    }

    throw new Error(`No handler for role select menu: ${ctx.customId}`);
  }

  private async handleRefresh(ctx: ComponentContext, hubId: string, userId: string): Promise<void> {
    try {
      await ctx.deferUpdate();

      // Get the hub log configuration
      const logConfig = await this.hubLogConfigService.getHubLogConfig(hubId);

      // Build the updated container
      const container = await this.getHubLoggingCommandHandler().buildLoggingContainer(
        hubId,
        userId,
        logConfig,
      );

      await ctx.editReply({
        components: [container],
        flags: ['IsComponentsV2'],
      });
    }
    catch (error) {
      Logger.error('Failed to refresh hub logging', { hubId, userId, error });
      await ctx.reply({
        content: '❌ Failed to refresh logging configuration.',
        flags: ['Ephemeral'],
      });
    }
  }

  private async handleConfigure(
    ctx: ComponentContext,
    hubId: string,
    userId: string,
    logType: LogConfigType,
  ): Promise<void> {
    try {
      await ctx.deferUpdate();

      // Get the hub log configuration
      const logConfig = await this.hubLogConfigService.getHubLogConfig(hubId);

      // Build the log type configuration container
      const container = await this.getHubLoggingCommandHandler().buildLogTypeContainer(
        hubId,
        userId,
        logType,
        logConfig,
      );

      await ctx.editReply({
        components: [container],
        flags: ['IsComponentsV2'],
      });
    }
    catch (error) {
      Logger.error('Failed to configure hub logging', { hubId, userId, logType, error });
      await ctx.reply({
        content: '❌ Failed to configure logging.',
        flags: ['Ephemeral'],
      });
    }
  }

  private async handleBack(ctx: ComponentContext, hubId: string, userId: string): Promise<void> {
    try {
      await ctx.deferUpdate();

      // Get the hub log configuration
      const logConfig = await this.hubLogConfigService.getHubLogConfig(hubId);

      // Build the main container
      const container = await this.getHubLoggingCommandHandler().buildLoggingContainer(
        hubId,
        userId,
        logConfig,
      );

      await ctx.editReply({
        components: [container],
        flags: ['IsComponentsV2'],
      });
    }
    catch (error) {
      Logger.error('Failed to go back to hub logging overview', { hubId, userId, error });
      await ctx.reply({
        content: '❌ Failed to go back to overview.',
        flags: ['Ephemeral'],
      });
    }
  }

  private async handleChannelSelection(
    ctx: ComponentContext,
    hubId: string,
    userId: string,
    logType: LogConfigType,
    values: string[],
  ): Promise<void> {
    try {
      await ctx.deferUpdate();

      const [channelId] = values;

      if (!channelId) {
        // Reset the log type
        await this.hubLogConfigService.resetLogType(hubId, logType);
        await ctx.reply({
          content: `✅ ${logType} logging has been reset.`,
          flags: ['Ephemeral'],
        });
      }
      else {
        // Set the log channel
        await this.hubLogConfigService.setLogChannel(hubId, logType, channelId);
        await ctx.reply({
          content: `✅ ${logType} logs will now be sent to <#${channelId}>.`,
          flags: ['Ephemeral'],
        });
      }

      // Update the UI
      const logConfig = await this.hubLogConfigService.getHubLogConfig(hubId);
      const container = await this.getHubLoggingCommandHandler().buildLogTypeContainer(
        hubId,
        userId,
        logType,
        logConfig,
      );

      await ctx.editReply({
        components: [container],
        flags: ['IsComponentsV2'],
      });
    }
    catch (error) {
      Logger.error('Failed to handle channel select', { hubId, userId, logType, values, error });
      await ctx.reply({
        content: '❌ Failed to update channel configuration.',
        flags: ['Ephemeral'],
      });
    }
  }

  private async handleRoleSelection(
    ctx: ComponentContext,
    hubId: string,
    userId: string,
    logType: LogConfigType,
    values: string[],
  ): Promise<void> {
    try {
      await ctx.deferUpdate();

      const [roleId] = values;

      if (!roleId) {
        // Remove the role
        await this.hubLogConfigService.removeLogRole(hubId, logType);
        await ctx.reply({
          content: `✅ Role mention has been removed from ${logType} logs.`,
          flags: ['Ephemeral'],
        });
      }
      else {
        // Set the log role
        await this.hubLogConfigService.setLogRole(hubId, logType, roleId);
        await ctx.reply({
          content: `✅ <@&${roleId}> will now be pinged for ${logType} logs.`,
          flags: ['Ephemeral'],
        });
      }

      // Update the UI
      const logConfig = await this.hubLogConfigService.getHubLogConfig(hubId);
      const container = await this.getHubLoggingCommandHandler().buildLogTypeContainer(
        hubId,
        userId,
        logType,
        logConfig,
      );

      await ctx.editReply({
        components: [container],
        flags: ['IsComponentsV2'],
      });
    }
    catch (error) {
      Logger.error('Failed to handle role select', { hubId, userId, logType, values, error });
      await ctx.reply({
        content: '❌ Failed to update role configuration.',
        flags: ['Ephemeral'],
      });
    }
  }
}
