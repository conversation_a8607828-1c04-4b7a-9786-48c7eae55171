/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { inject, injectable } from 'inversify';
import { MessageFlags } from 'discord.js';
import { BaseInteractionHandler } from '../BaseInteractionHandler.js';
import { ComponentContext } from '../../../shared/context/index.js';
import { TYPES } from '../../../shared/types/TYPES.js';
import { HubWelcomeUIService } from '../../../application/services/HubWelcomeUIService.js';
import type { IHubRepository } from '../../../domain/repositories/HubRepositories.js';

@injectable()
export default class HubWelcomeInteractionHandler extends BaseInteractionHandler {
  public readonly handlerId = 'hub_welcome';

  constructor(
    @inject(TYPES.HubRepository)
    private readonly hubRepository: IHubRepository,
    @inject(TYPES.HubWelcomeUIService)
    private readonly hubWelcomeUIService: HubWelcomeUIService,
  ) {
    super();
  }

  async handleButton(ctx: ComponentContext): Promise<void> {
    const [hubId, action] = ctx.customId.args;

    if (!hubId || !action) {
      await ctx.reply({
        content: '❌ Invalid welcome configuration request.',
        flags: [MessageFlags.Ephemeral],
      });
      return;
    }

    // Verify user owns the hub
    const hub = await this.hubRepository.findById(hubId);
    if (!hub || hub.ownerId !== ctx.user.id) {
      await ctx.reply({
        content: '❌ You can only edit hubs you own.',
        flags: [MessageFlags.Ephemeral],
      });
      return;
    }

    switch (action) {
      case 'set':
        await this.handleSetMessage(ctx, hubId);
        break;
      case 'remove':
        await this.handleRemoveMessage(ctx, hubId);
        break;
      default:
        await ctx.reply({
          content: '❌ Unknown welcome action.',
          flags: [MessageFlags.Ephemeral],
        });
        break;
    }
  }

  async handleModal(ctx: ComponentContext): Promise<void> {
    const [hubId, action] = ctx.customId.args;

    if (!hubId || !action) {
      await ctx.reply({
        content: '❌ Invalid welcome modal request.',
        flags: [MessageFlags.Ephemeral],
      });
      return;
    }

    // Verify user owns the hub
    const hub = await this.hubRepository.findById(hubId);
    if (!hub || hub.ownerId !== ctx.user.id) {
      await ctx.reply({
        content: '❌ You can only edit hubs you own.',
        flags: [MessageFlags.Ephemeral],
      });
      return;
    }

    if (action === 'modal') {
      await this.handleWelcomeModal(ctx, hubId);
    }
    else {
      await ctx.reply({
        content: '❌ Unknown welcome modal action.',
        flags: [MessageFlags.Ephemeral],
      });
    }
  }

  private async handleSetMessage(ctx: ComponentContext, hubId: string): Promise<void> {
    const hub = await this.hubRepository.findById(hubId);
    if (!hub) {
      await ctx.reply({
        content: '❌ Hub not found.',
        flags: [MessageFlags.Ephemeral],
      });
      return;
    }

    // TODO: Check if user has voted (premium feature)
    // For now, we'll assume they have access

    const modal = this.hubWelcomeUIService.buildWelcomeModal(hubId, hub.welcomeMessage || '');
    await ctx.showModal(modal);
  }

  private async handleRemoveMessage(ctx: ComponentContext, hubId: string): Promise<void> {
    const hub = await this.hubRepository.findById(hubId);
    if (!hub) {
      await ctx.reply({
        content: '❌ Hub not found.',
        flags: [MessageFlags.Ephemeral],
      });
      return;
    }

    await hub.update({ welcomeMessage: undefined });

    // Refresh the container
    const container = await this.hubWelcomeUIService.buildWelcomeContainer(hubId);
    await ctx.editReply({
      components: [container],
      flags: ['IsComponentsV2'],
    });

    await ctx.reply({
      content: '✅ Welcome message removed successfully.',
      flags: [MessageFlags.Ephemeral],
    });
  }

  private async handleWelcomeModal(ctx: ComponentContext, hubId: string): Promise<void> {
    const welcomeMessage = ctx.getModalFieldValue('welcome_message') || null;

    const hub = await this.hubRepository.findById(hubId);
    if (!hub) {
      await ctx.reply({
        content: '❌ Hub not found.',
        flags: [MessageFlags.Ephemeral],
      });
      return;
    }

    await hub.update({ welcomeMessage: welcomeMessage || undefined });

    // Refresh the container
    const container = await this.hubWelcomeUIService.buildWelcomeContainer(hubId);
    await ctx.editReply({
      components: [container],
      flags: ['IsComponentsV2'],
    });

    const message = welcomeMessage
      ? '✅ Welcome message updated successfully.'
      : '✅ Welcome message removed successfully.';

    await ctx.reply({
      content: message,
      flags: [MessageFlags.Ephemeral],
    });
  }
}
