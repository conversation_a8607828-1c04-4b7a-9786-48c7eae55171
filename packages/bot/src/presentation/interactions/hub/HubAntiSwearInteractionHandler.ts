/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { inject, injectable } from 'inversify';
import { MessageFlags } from 'discord.js';
import { BaseInteractionHandler } from '../BaseInteractionHandler.js';
import { ComponentContext } from '../../../shared/context/index.js';
import { TYPES } from '../../../shared/types/TYPES.js';
import { HubAntiSwearUIService } from '../../../application/services/HubAntiSwearUIService.js';
import type { IHubRepository } from '../../../domain/repositories/HubRepositories.js';

@injectable()
export default class HubAntiSwearInteractionHandler extends BaseInteractionHandler {
  public readonly handlerId = 'hub_antiswear';

  constructor(
    @inject(TYPES.HubRepository)
    private readonly hubRepository: IHubRepository,
    @inject(TYPES.HubAntiSwearUIService)
    private readonly hubAntiSwearUIService: HubAntiSwearUIService,
  ) {
    super();
  }

  async handleButton(ctx: ComponentContext): Promise<void> {
    const [hubId] = ctx.customId.args;
    const { suffix: action } = ctx.customId;
    const ruleId = ctx.values[0];

    if (!hubId || !action) {
      await ctx.reply({
        content: '❌ Invalid anti-swear configuration request.',
        flags: [MessageFlags.Ephemeral],
      });
      return;
    }

    // Verify user owns the hub
    const hub = await this.hubRepository.findById(hubId);
    if (!hub || hub.ownerId !== ctx.user.id) {
      await ctx.reply({
        content: '❌ You can only edit hubs you own.',
        flags: [MessageFlags.Ephemeral],
      });
      return;
    }

    switch (action) {
      case 'add':
        await this.handleAddRule(ctx, hubId);
        break;
      case 'edit':
        await this.handleEditRule(ctx, hubId, ruleId);
        break;
      case 'edit_patterns':
        await this.handleEditPatterns(ctx, hubId, ruleId);
        break;
      case 'delete':
        await this.handleDeleteRule(ctx, hubId, ruleId);
        break;
      case 'back':
        await this.handleBack(ctx, hubId);
        break;
      case 'refresh':
        await this.handleRefresh(ctx, hubId);
        break;
      default:
        await ctx.reply({
          content: '❌ Unknown anti-swear action.',
          flags: [MessageFlags.Ephemeral],
        });
        break;
    }
  }

  async handleStringSelectMenu(ctx: ComponentContext): Promise<void> {
    const [hubId, ruleId] = ctx.customId.args;
    const { suffix: action } = ctx.customId;

    if (!hubId || !action) {
      await ctx.reply({
        content: '❌ Invalid anti-swear selection request.',
        flags: [MessageFlags.Ephemeral],
      });
      return;
    }

    // Verify user owns the hub
    const hub = await this.hubRepository.findById(hubId);
    if (!hub || hub.ownerId !== ctx.user.id) {
      await ctx.reply({
        content: '❌ You can only edit hubs you own.',
        flags: [MessageFlags.Ephemeral],
      });
      return;
    }

    switch (action) {
      case 'select':
        if (ctx.values && ctx.values.length > 0) {
          await this.handleSelectRule(ctx, hubId, ctx.values[0]);
        }
        break;
      case 'actions':
        if (ctx.values) {
          await this.handleActionSelection(ctx, hubId, ruleId, ctx.values);
        }
        break;
      default:
        await ctx.reply({
          content: '❌ Unknown anti-swear selection action.',
          flags: [MessageFlags.Ephemeral],
        });
        break;
    }
  }

  async handleModal(ctx: ComponentContext): Promise<void> {
    const [hubId, ruleId] = ctx.customId.args;
    const { suffix: action } = ctx.customId;

    if (!hubId || !action) {
      await ctx.reply({
        content: '❌ Invalid anti-swear modal request.',
        flags: [MessageFlags.Ephemeral],
      });
      return;
    }

    // Verify user owns the hub
    const hub = await this.hubRepository.findById(hubId);
    if (!hub || hub.ownerId !== ctx.user.id) {
      await ctx.reply({
        content: '❌ You can only edit hubs you own.',
        flags: [MessageFlags.Ephemeral],
      });
      return;
    }

    switch (action) {
      case 'add_modal':
        await this.handleAddRuleModal(ctx, hubId);
        break;
      case 'edit_modal':
        await this.handleEditRuleModal(ctx, hubId, ruleId);
        break;
      default:
        await ctx.reply({
          content: '❌ Unknown anti-swear modal action.',
          flags: [MessageFlags.Ephemeral],
        });
        break;
    }
  }

  private async handleAddRule(ctx: ComponentContext, hubId: string): Promise<void> {
    const modal = this.hubAntiSwearUIService.buildAddRuleModal(hubId);
    await ctx.showModal(modal);
  }

  private async handleEditRule(
    ctx: ComponentContext,
    _hubId: string,
    _ruleId: string,
  ): Promise<void> {
    // TODO: Implement anti-swear rule editing
    await ctx.reply({
      content: '⚠️ Anti-swear rule editing coming soon!',
      flags: [MessageFlags.Ephemeral],
    });
  }

  private async handleEditPatterns(
    ctx: ComponentContext,
    _hubId: string,
    _ruleId: string,
  ): Promise<void> {
    // TODO: Implement pattern editing
    await ctx.reply({
      content: '⚠️ Pattern editing coming soon!',
      flags: [MessageFlags.Ephemeral],
    });
  }

  private async handleDeleteRule(
    ctx: ComponentContext,
    _hubId: string,
    _ruleId: string,
  ): Promise<void> {
    // TODO: Implement anti-swear rule deletion
    await ctx.reply({
      content: '⚠️ Anti-swear rule deletion coming soon!',
      flags: [MessageFlags.Ephemeral],
    });
  }

  private async handleSelectRule(
    ctx: ComponentContext,
    _hubId: string,
    _ruleId: string,
  ): Promise<void> {
    // TODO: Implement rule selection and detail view
    await ctx.reply({
      content: '⚠️ Rule selection coming soon!',
      flags: [MessageFlags.Ephemeral],
    });
  }

  private async handleActionSelection(
    ctx: ComponentContext,
    _hubId: string,
    _ruleId: string,
    _actions: string[],
  ): Promise<void> {
    // TODO: Implement action selection
    await ctx.reply({
      content: '⚠️ Action selection coming soon!',
      flags: [MessageFlags.Ephemeral],
    });
  }

  private async handleBack(ctx: ComponentContext, hubId: string): Promise<void> {
    const container = await this.hubAntiSwearUIService.buildAntiSwearContainer(hubId);
    await ctx.editReply({
      components: [container],
      flags: ['IsComponentsV2'],
    });
  }

  private async handleRefresh(ctx: ComponentContext, hubId: string): Promise<void> {
    const container = await this.hubAntiSwearUIService.buildAntiSwearContainer(hubId);
    await ctx.editReply({
      components: [container],
      flags: ['IsComponentsV2'],
    });
  }

  private async handleAddRuleModal(ctx: ComponentContext, _hubId: string): Promise<void> {
    // TODO: Implement add rule modal handling
    await ctx.reply({
      content: '⚠️ Add rule modal handling coming soon!',
      flags: [MessageFlags.Ephemeral],
    });
  }

  private async handleEditRuleModal(
    ctx: ComponentContext,
    _hubId: string,
    _ruleId: string,
  ): Promise<void> {
    // TODO: Implement edit rule modal handling
    await ctx.reply({
      content: '⚠️ Edit rule modal handling coming soon!',
      flags: [MessageFlags.Ephemeral],
    });
  }
}
