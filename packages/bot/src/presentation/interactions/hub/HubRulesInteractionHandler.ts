/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { inject, injectable } from 'inversify';
import { MessageFlags } from 'discord.js';
import { BaseInteractionHandler } from '../BaseInteractionHandler.js';
import { ComponentContext } from '../../../shared/context/index.js';
import { TYPES } from '../../../shared/types/TYPES.js';
import { HubRulesUIService } from '../../../application/services/HubRulesUIService.js';
import type { IHubRepository } from '../../../domain/repositories/HubRepositories.js';

@injectable()
export default class HubRulesInteractionHandler extends BaseInteractionHandler {
  public readonly handlerId = 'hub_rules';

  constructor(
    @inject(TYPES.HubRepository)
    private readonly hubRepository: IHubRepository,
    @inject(TYPES.HubRulesUIService)
    private readonly hubRulesUIService: HubRulesUIService,
  ) {
    super();
  }

  async handleButton(ctx: ComponentContext): Promise<void> {
    const [hubId, ruleIndex] = ctx.customId.args;
    const { suffix: action } = ctx.customId;

    if (!hubId || !action) {
      await ctx.reply({
        content: '❌ Invalid rules configuration request.',
        flags: [MessageFlags.Ephemeral],
      });
      return;
    }

    // Verify user owns the hub
    const hub = await this.hubRepository.findById(hubId);
    if (!hub || hub.ownerId !== ctx.user.id) {
      await ctx.reply({
        content: '❌ You can only edit hubs you own.',
        flags: [MessageFlags.Ephemeral],
      });
      return;
    }

    switch (action) {
      case 'add':
        await this.handleAddRule(ctx, hubId);
        break;
      case 'edit':
        await this.handleEditRule(ctx, hubId, ruleIndex);
        break;
      case 'delete':
        await this.handleDeleteRule(ctx, hubId, ruleIndex);
        break;
      case 'back':
        await this.handleBack(ctx, hubId);
        break;
      case 'refresh':
        await this.handleRefresh(ctx, hubId);
        break;
      default:
        await ctx.reply({
          content: '❌ Unknown rules action.',
          flags: [MessageFlags.Ephemeral],
        });
        break;
    }
  }

  async handleStringSelectMenu(ctx: ComponentContext): Promise<void> {
    const [hubId, action] = ctx.customId.args;

    if (!hubId || !action) {
      await ctx.reply({
        content: '❌ Invalid rules selection request.',
        flags: [MessageFlags.Ephemeral],
      });
      return;
    }

    // Verify user owns the hub
    const hub = await this.hubRepository.findById(hubId);
    if (!hub || hub.ownerId !== ctx.user.id) {
      await ctx.reply({
        content: '❌ You can only edit hubs you own.',
        flags: [MessageFlags.Ephemeral],
      });
      return;
    }

    if (action === 'select' && ctx.values && ctx.values.length > 0) {
      const ruleIndex = parseInt(ctx.values[0], 10);
      await this.handleSelectRule(ctx, hubId, ruleIndex);
    }
  }

  async handleModal(ctx: ComponentContext): Promise<void> {
    const [hubId, ruleIndex] = ctx.customId.args;
    const { suffix: action } = ctx.customId;

    if (!hubId || !action) {
      await ctx.reply({
        content: '❌ Invalid rules modal request.',
        flags: [MessageFlags.Ephemeral],
      });
      return;
    }

    // Verify user owns the hub
    const hub = await this.hubRepository.findById(hubId);
    if (!hub || hub.ownerId !== ctx.user.id) {
      await ctx.reply({
        content: '❌ You can only edit hubs you own.',
        flags: [MessageFlags.Ephemeral],
      });
      return;
    }

    switch (action) {
      case 'add_modal':
        await this.handleAddRuleModal(ctx, hubId);
        break;
      case 'edit_modal':
        await this.handleEditRuleModal(ctx, hubId, ruleIndex);
        break;
      default:
        await ctx.reply({
          content: '❌ Unknown rules modal action.',
          flags: [MessageFlags.Ephemeral],
        });
        break;
    }
  }

  private async handleAddRule(ctx: ComponentContext, hubId: string): Promise<void> {
    const modal = this.hubRulesUIService.buildAddRuleModal(hubId);
    await ctx.showModal(modal);
  }

  private async handleEditRule(
    ctx: ComponentContext,
    hubId: string,
    ruleIndexStr: string,
  ): Promise<void> {
    const ruleIndex = parseInt(ruleIndexStr, 10);
    const hub = await this.hubRepository.findById(hubId);

    if (!hub) {
      await ctx.reply({
        content: '❌ Hub not found.',
        flags: [MessageFlags.Ephemeral],
      });
      return;
    }

    const rules = hub.rules || [];
    const rule = rules[ruleIndex];

    if (!rule) {
      await ctx.reply({
        content: '❌ Rule not found.',
        flags: [MessageFlags.Ephemeral],
      });
      return;
    }

    const modal = this.hubRulesUIService.buildEditRuleModal(hubId, ruleIndex, rule);
    await ctx.showModal(modal);
  }

  private async handleDeleteRule(
    ctx: ComponentContext,
    hubId: string,
    ruleIndexStr: string,
  ): Promise<void> {
    const ruleIndex = parseInt(ruleIndexStr, 10);
    const hub = await this.hubRepository.findById(hubId);

    if (!hub) {
      await ctx.reply({
        content: '❌ Hub not found.',
        flags: [MessageFlags.Ephemeral],
      });
      return;
    }

    const rules = hub.rules || [];
    if (ruleIndex < 0 || ruleIndex >= rules.length) {
      await ctx.reply({
        content: '❌ Rule not found.',
        flags: [MessageFlags.Ephemeral],
      });
      return;
    }

    // Remove the rule
    const newRules = rules.filter((_, index) => index !== ruleIndex);
    await hub.update({ rules: newRules });

    // Refresh the container
    const container = await this.hubRulesUIService.buildRulesContainer(hubId);
    await ctx.editReply({
      components: [container],
      flags: ['IsComponentsV2'],
    });

    await ctx.reply({
      content: `✅ Rule ${ruleIndex + 1} deleted successfully.`,
      flags: [MessageFlags.Ephemeral],
    });
  }

  private async handleSelectRule(
    ctx: ComponentContext,
    hubId: string,
    ruleIndex: number,
  ): Promise<void> {
    const container = await this.hubRulesUIService.buildRuleDetailContainer(hubId, ruleIndex);
    await ctx.editReply({
      components: [container],
      flags: ['IsComponentsV2'],
    });
  }

  private async handleBack(ctx: ComponentContext, hubId: string): Promise<void> {
    const container = await this.hubRulesUIService.buildRulesContainer(hubId);
    await ctx.editReply({
      components: [container],
      flags: ['IsComponentsV2'],
    });
  }

  private async handleRefresh(ctx: ComponentContext, hubId: string): Promise<void> {
    const container = await this.hubRulesUIService.buildRulesContainer(hubId);
    await ctx.editReply({
      components: [container],
      flags: ['IsComponentsV2'],
    });
  }

  private async handleAddRuleModal(ctx: ComponentContext, hubId: string): Promise<void> {
    const ruleText = ctx.getModalFieldValue('rule_text');
    if (!ruleText) {
      await ctx.reply({
        content: '❌ Rule text is required.',
        flags: [MessageFlags.Ephemeral],
      });
      return;
    }

    const hub = await this.hubRepository.findById(hubId);
    if (!hub) {
      await ctx.reply({
        content: '❌ Hub not found.',
        flags: [MessageFlags.Ephemeral],
      });
      return;
    }

    const rules = hub.rules || [];
    const newRules = [...rules, ruleText];
    await hub.update({ rules: newRules });

    // Refresh the container
    const container = await this.hubRulesUIService.buildRulesContainer(hubId);
    await ctx.editReply({
      components: [container],
      flags: ['IsComponentsV2'],
    });

    await ctx.reply({
      content: `✅ Rule ${newRules.length} added successfully.`,
      flags: [MessageFlags.Ephemeral],
    });
  }

  private async handleEditRuleModal(
    ctx: ComponentContext,
    hubId: string,
    ruleIndexStr: string,
  ): Promise<void> {
    const ruleIndex = parseInt(ruleIndexStr, 10);
    const ruleText = ctx.getModalFieldValue('rule_text');

    if (!ruleText) {
      await ctx.reply({
        content: '❌ Rule text is required.',
        flags: [MessageFlags.Ephemeral],
      });
      return;
    }

    const hub = await this.hubRepository.findById(hubId);
    if (!hub) {
      await ctx.reply({
        content: '❌ Hub not found.',
        flags: [MessageFlags.Ephemeral],
      });
      return;
    }

    const rules = hub.rules || [];
    if (ruleIndex < 0 || ruleIndex >= rules.length) {
      await ctx.reply({
        content: '❌ Rule not found.',
        flags: [MessageFlags.Ephemeral],
      });
      return;
    }

    // Update the rule
    const newRules = [...rules];
    newRules[ruleIndex] = ruleText;
    await hub.update({ rules: newRules });

    // Refresh the container
    const container = await this.hubRulesUIService.buildRulesContainer(hubId);
    await ctx.editReply({
      components: [container],
      flags: ['IsComponentsV2'],
    });

    await ctx.reply({
      content: `✅ Rule ${ruleIndex + 1} updated successfully.`,
      flags: [MessageFlags.Ephemeral],
    });
  }
}
