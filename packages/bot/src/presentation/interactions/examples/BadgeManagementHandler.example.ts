/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { ActionRowBuilder, ButtonBuilder, ButtonInteraction, ButtonStyle, MessageFlags } from 'discord.js';
import { injectable } from 'inversify';
import { BaseInteractionHandler } from '../BaseInteractionHandler.js';
import { Context } from '../../../shared/context/Context.js';
import { CustomID } from '../../../shared/utils/CustomID.js';

/**
 * Example Badge Management Interaction Handler
 *
 * This demonstrates how to use the modern CustomID system with the
 * BaseInteractionHandler for compressed custom IDs that stay under
 * Discord's 100-character limit.
 */
@injectable()
export default class BadgeManagementHandler extends BaseInteractionHandler {
  readonly handlerId = 'badge_mgmt';

  async handleButton(ctx: Context, interaction: ButtonInteraction): Promise<void> {
    // Parse the custom ID using the modern system
    const parsed = CustomID.parse(interaction.customId);
    const [action, targetUserId, badgeType] = parsed.args;

    switch (action) {
      case 'add':
        await this.handleAddBadge(ctx, targetUserId, badgeType);
        break;
      case 'remove':
        await this.handleRemoveBadge(ctx, targetUserId, badgeType);
        break;
      case 'list':
        await this.handleListBadges(ctx, targetUserId);
        break;
      default:
        await interaction.reply({
          content: '❌ Unknown badge action.',
          flags: [MessageFlags.Ephemeral],
        });
    }
  }

  private async handleAddBadge(
    ctx: Context,
    targetUserId: string,
    badgeType: string,
  ): Promise<void> {
    // Create confirmation buttons with compressed custom IDs
    const confirmButton = new ButtonBuilder()
      .setCustomId(this.buildCustomId('confirm_add', targetUserId, badgeType))
      .setLabel('✅ Confirm Add')
      .setStyle(ButtonStyle.Success);

    const cancelButton = new ButtonBuilder()
      .setCustomId(this.buildCustomId('cancel', targetUserId, badgeType))
      .setLabel('❌ Cancel')
      .setStyle(ButtonStyle.Secondary);

    const row = new ActionRowBuilder<ButtonBuilder>().addComponents(confirmButton, cancelButton);

    await ctx.reply({
      content: `Are you sure you want to add the **${badgeType}** badge to <@${targetUserId}>?`,
      components: [row],
      flags: [MessageFlags.Ephemeral],
    });
  }

  private async handleRemoveBadge(
    ctx: Context,
    targetUserId: string,
    badgeType: string,
  ): Promise<void> {
    // Create confirmation buttons with compressed custom IDs
    const confirmButton = new ButtonBuilder()
      .setCustomId(this.buildCustomId('confirm_remove', targetUserId, badgeType))
      .setLabel('✅ Confirm Remove')
      .setStyle(ButtonStyle.Danger);

    const cancelButton = new ButtonBuilder()
      .setCustomId(this.buildCustomId('cancel', targetUserId, badgeType))
      .setLabel('❌ Cancel')
      .setStyle(ButtonStyle.Secondary);

    const row = new ActionRowBuilder<ButtonBuilder>().addComponents(confirmButton, cancelButton);

    await ctx.reply({
      content: `Are you sure you want to remove the **${badgeType}** badge from <@${targetUserId}>?`,
      components: [row],
      flags: [MessageFlags.Ephemeral],
    });
  }

  private async handleListBadges(
    ctx: Context,
    targetUserId: string,
  ): Promise<void> {
    // Example of creating a custom ID with expiry (24 hours)
    const refreshButton = new ButtonBuilder()
      .setCustomId(
        CustomID.fromHandler(this.handlerId, 'list', targetUserId)
          .setExpiry(new Date(Date.now() + 24 * 60 * 60 * 1000))
          .toString(),
      )
      .setLabel('🔄 Refresh')
      .setStyle(ButtonStyle.Secondary);

    const row = new ActionRowBuilder<ButtonBuilder>().addComponents(refreshButton);

    await ctx.reply({
      content: `📋 Badge list for <@${targetUserId}>:\n\n• Developer Badge\n• Supporter Badge\n• Moderator Badge`,
      components: [row],
      flags: [MessageFlags.Ephemeral],
    });
  }

  /**
   * Create badge management buttons for use in other parts of the application
   */
  public static createBadgeButtons(targetUserId: string): ActionRowBuilder<ButtonBuilder>[] {
    const addButton = new ButtonBuilder()
      .setCustomId(CustomID.fromHandler('badge_mgmt', 'add', targetUserId, 'supporter').toString())
      .setLabel('➕ Add Badge')
      .setStyle(ButtonStyle.Primary);

    const removeButton = new ButtonBuilder()
      .setCustomId(CustomID.fromHandler('badge_mgmt', 'remove', targetUserId, 'supporter').toString())
      .setLabel('➖ Remove Badge')
      .setStyle(ButtonStyle.Danger);

    const listButton = new ButtonBuilder()
      .setCustomId(CustomID.fromHandler('badge_mgmt', 'list', targetUserId).toString())
      .setLabel('📋 List Badges')
      .setStyle(ButtonStyle.Secondary);

    return [
      new ActionRowBuilder<ButtonBuilder>().addComponents(addButton, removeButton),
      new ActionRowBuilder<ButtonBuilder>().addComponents(listButton),
    ];
  }
}

/**
 * Example usage in a command handler
 */
export class ExampleCommandHandler {
  static async createBadgeManagementEmbed(targetUserId: string) {
    const buttons = BadgeManagementHandler.createBadgeButtons(targetUserId);

    return {
      embeds: [
        {
          title: 'Badge Management',
          description: `Manage badges for <@${targetUserId}>`,
          color: 0x00FF00,
        },
      ],
      components: buttons,
    };
  }
}
