/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import {
  ButtonInteraction,
  ContainerBuilder,
  SeparatorSpacingSize,
  TextDisplayBuilder,
} from 'discord.js';
import { inject, injectable, type Container } from 'inversify';
import { ComponentContext } from '../../../shared/context/ComponentContext.js';
import { TYPES } from '../../../shared/types/TYPES.js';
import ConnectionsCommandHandler from '../../commands/utilities/ConnectionsCommandHandler.js';
import { BaseInteractionHandler } from '../BaseInteractionHandler.js';

@injectable()
export default class ConnectionsInteractionHandler extends BaseInteractionHandler {
  private async handleSetupHelp(context: ComponentContext): Promise<void> {
    const container = new ContainerBuilder();

    container.addTextDisplayComponents(
      new TextDisplayBuilder().setContent(
        '## 🚀 How to Connect to a Hub\nFollow these simple steps to connect your server to InterChat hubs:',
      ),
    );

    container.addSeparatorComponents((separator) =>
      separator.setSpacing(SeparatorSpacingSize.Large),
    );

    // Add steps as sections
    const steps = [
      '### 1️⃣ Choose a Channel\nPick a channel in your server where hub messages will appear',
      '### 2️⃣ Run the Setup Command\n```/setup channel channel:#your-channel hub:HubName```',
      '### 3️⃣ Start Chatting!\nMessages in that channel will now be shared with other connected servers',
      '### 💡 Need Help Finding Hubs?\nBrowse available hubs at [interchat.tech/hubs](https://interchat.tech/hubs)',
    ];

    for (const step of steps) {
      container.addTextDisplayComponents(new TextDisplayBuilder().setContent(step));
    }

    container.addTextDisplayComponents(
      new TextDisplayBuilder().setContent(
        '**Note:** You need "Manage Server" permission to connect channels',
      ),
    );

    await context.editOrReply({ components: [container] }, ['IsComponentsV2', 'Ephemeral']);
  }

  private async handleManageHelp(context: ComponentContext): Promise<void> {
    const container = new ContainerBuilder();

    container.addTextDisplayComponents(
      new TextDisplayBuilder().setContent(
        "## ⚙️ Managing Connections\nHere are the commands you can use to manage your server's hub connections:",
      ),
    );

    container.addSeparatorComponents((separator) =>
      separator.setSpacing(SeparatorSpacingSize.Large),
    );

    // Add command info as sections
    const commands = [
      '### 🔌 Disconnect a Channel\n```/disconnect [channel:#channel-name]```\nRemoves a channel from its hub (permanent)',
      '### 🔗 Connect New Channel\n```/setup channel channel:#new-channel hub:HubName```\nConnect another channel to a hub',
      '### 📋 View Connections\n```/connections```\nView all active connections (this command)',
      '### 📊 Server Setup Info\n```/setup info```\nDetailed setup information for your server',
    ];

    for (const command of commands) {
      container.addTextDisplayComponents(new TextDisplayBuilder().setContent(command));
    }

    container.addTextDisplayComponents(
      new TextDisplayBuilder().setContent(
        '**Note:** Most management commands require "Manage Channels" permission',
      ),
    );

    await context.editOrReply({ components: [container] }, ['IsComponentsV2', 'Ephemeral']);
  }
  readonly handlerId = 'connections';

  constructor(
    @inject(TYPES.Container)
    private readonly container: Container,
  ) {
    super();
  }

  private getConnectionsCommandHandler(): ConnectionsCommandHandler {
    // Get the command handler dynamically from container
    return this.container.get<ConnectionsCommandHandler>(ConnectionsCommandHandler);
  }

  async handleButton(ctx: ComponentContext, interaction: ButtonInteraction): Promise<void> {
    // Check if this is a connections-related button
    if (ctx.customId.prefix === 'connections') {
      // Handle different suffixes
      switch (ctx.customId.suffix) {
        case 'setup_help':
          await this.handleSetupHelp(ctx);
          return;
        case 'manage_help':
          await this.handleManageHelp(ctx);
          return;
        case 'page':
          await this.handlePage(ctx);
          return;
        case 'configure':
          await this.handleConfigure(ctx);
          return;
        default:
          throw new Error(`Unknown connections suffix: ${ctx.customId.suffix}`);
      }
    }
    throw new Error(`No handler for button: ${interaction.customId}`);
  }

  /**
   * Handle pagination button (next/prev/first/last page)
   */
  private async handlePage(context: ComponentContext): Promise<void> {
    // CustomID: connections:page:[pageNumber]
    const page = parseInt(context.customId.args[0], 10);
    if (isNaN(page)) throw new Error('Invalid page number');
    const handler = this.getConnectionsCommandHandler();
    const guild = context.guild;
    if (!guild) throw new Error('Guild not found in context');
    const member = context.member;
    let hasManagePermission = false;
    if (
      member &&
      member.permissions &&
      typeof member.permissions !== 'string' &&
      typeof member.permissions.has === 'function'
    ) {
      hasManagePermission = member.permissions.has('ManageChannels');
    }
    const connections = await handler.serverRepository.getServerConnections(guild.id);
    const container = await handler.buildConnectionsContainer(
      connections,
      guild.name,
      hasManagePermission,
      page,
    );
    await context.editOrReply({ components: [container] }, ['IsComponentsV2']);
  }

  /**
   * Handle configure button for a connection
   */
  private async handleConfigure(context: ComponentContext): Promise<void> {
    // CustomID: connections:configure:[channelId]
    const channelId = context.customId.args[0];
    if (!channelId) throw new Error('No channelId provided');

    const container = new ContainerBuilder();
    container.addTextDisplayComponents(
      new TextDisplayBuilder().setContent(
        `## ⚙️ Configure Connection\nManaging connection for <#${channelId}>`,
      ),
    );

    container.addSeparatorComponents((separator) =>
      separator.setSpacing(SeparatorSpacingSize.Large),
    );

    // Configuration options
    const options = [
      '### 🔌 Disconnect Channel\nRemove this channel from the hub (permanent action)',
      '### 🔄 Reconnect Channel\nReconnect if there are connection issues',
      '### 📊 View Hub Info\nSee details about the connected hub',
      '### ⚙️ Channel Settings\nModify how messages appear in this channel',
    ];

    for (const option of options) {
      container.addTextDisplayComponents(new TextDisplayBuilder().setContent(option));
    }

    await context.editOrReply({ components: [container] }, ['IsComponentsV2', 'Ephemeral']);
  }

  // (removed duplicate handleSetupHelp and handleManageHelp methods)
}
