/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

/**
 * Presentation Layer DTOs for Discord Interactions
 *
 * These DTOs convert Discord interaction data to/from application layer DTOs.
 */

import { ChatInputCommandInteraction, User } from 'discord.js';

/**
 * Extract donation command data from Discord interaction
 */
export interface DonationCommandData {
  readonly amount: number;
  readonly currency: string;
  readonly tier: number;
  readonly donor: User;
  readonly kofiTransactionId?: string;
  readonly message?: string;
}

/**
 * Premium status display data
 */
export interface PremiumStatusDisplay {
  readonly user: User;
  readonly isPremium: boolean;
  readonly tier: number;
  readonly tierName: string;
  readonly expiresAt?: Date;
  readonly totalDonated: number;
  readonly donationCount: number;
}

/**
 * Command response data
 */
export interface CommandResponse {
  readonly title: string;
  readonly description: string;
  readonly color: number;
  readonly ephemeral: boolean;
  readonly fields?: Array<{
    name: string;
    value: string;
    inline?: boolean;
  }>;
}

/**
 * Utility functions for converting interaction data
 */
export class InteractionDataConverter {
  /**
   * Extract donation data from slash command interaction
   */
  static extractDonationData(interaction: ChatInputCommandInteraction): DonationCommandData {
    const amount = interaction.options.getNumber('amount', true);
    const currency = interaction.options.getString('currency') || 'USD';
    const tier = interaction.options.getInteger('tier') || 1;
    const targetUser = interaction.options.getUser('user') || interaction.user;
    const kofiId = interaction.options.getString('kofi_id') || undefined;
    const message = interaction.options.getString('message') || undefined;

    return {
      amount,
      currency,
      tier,
      donor: targetUser,
      kofiTransactionId: kofiId,
      message,
    };
  }

  /**
   * Convert premium status to display format
   */
  static formatPremiumStatus(
    user: User,
    premiumResult: {
      isPremium: boolean;
      tier: number;
      expiresAt?: Date;
      totalDonated: number;
      donationCount: number;
    },
  ): PremiumStatusDisplay {
    return {
      user,
      isPremium: premiumResult.isPremium,
      tier: premiumResult.tier,
      tierName: this.getTierName(premiumResult.tier),
      expiresAt: premiumResult.expiresAt,
      totalDonated: premiumResult.totalDonated,
      donationCount: premiumResult.donationCount,
    };
  }

  /**
   * Get tier name from tier number
   */
  static getTierName(tier: number): string {
    switch (tier) {
      case 1:
        return '🥉 Bronze Supporter';
      case 2:
        return '🥈 Silver Supporter';
      case 3:
        return '🥇 Gold Supporter';
      case 4:
        return '💎 Platinum Supporter';
      default:
        return '👤 Regular User';
    }
  }

  /**
   * Format currency display
   */
  static formatCurrency(amount: number, currency: string): string {
    const formatter = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
      minimumFractionDigits: 2,
    });

    try {
      return formatter.format(amount);
    }
    catch {
      return `${amount} ${currency}`;
    }
  }

  /**
   * Format timestamp for Discord
   */
  static formatDiscordTimestamp(date: Date, style: 'F' | 'R' | 'D' | 'T' = 'F'): string {
    const timestamp = Math.floor(date.getTime() / 1000);
    return `<t:${timestamp}:${style}>`;
  }

  /**
   * Create success response
   */
  static createSuccessResponse(
    title: string,
    description: string,
    ephemeral = false,
  ): CommandResponse {
    return {
      title: `✅ ${title}`,
      description,
      color: 0x00ff00,
      ephemeral,
    };
  }

  /**
   * Create error response
   */
  static createErrorResponse(
    title: string,
    description: string,
    ephemeral = true,
  ): CommandResponse {
    return {
      title: `❌ ${title}`,
      description,
      color: 0xff0000,
      ephemeral,
    };
  }

  /**
   * Create info response
   */
  static createInfoResponse(
    title: string,
    description: string,
    ephemeral = false,
  ): CommandResponse {
    return {
      title: `ℹ️ ${title}`,
      description,
      color: 0x0099ff,
      ephemeral,
    };
  }
}
