/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { injectable, inject } from 'inversify';
import type { IEventHandler } from '../../infrastructure/events/TestInterfaces.js';
import {
  ClientReadyEvent,
  ClientShutdownEvent,
  GuildJoinedEvent,
  GuildLeftEvent,
} from '../../domain/events/ClientEvents.js';
import { Logger } from '../../shared/utils/Logger.js';
import { TYPES } from '../../shared/types/TYPES.js';
import { PrismaClient } from '../../../../../build/generated/prisma/client/index.js';

/**
 * Client Ready Event Handler
 *
 * Handles actions when the Discord client becomes ready
 */
@injectable()
export class ClientReadyEventHandler implements IEventHandler<ClientReadyEvent> {
  readonly eventType = 'client.ready';
  readonly handlerId = 'ClientReadyEventHandler';

  async handle(event: ClientReadyEvent): Promise<void> {
    Logger.info(`🎉 Client ready event received for cluster ${event.clusterId}`);
    Logger.info(
      `📊 Cluster stats: ${event.guilds} guilds, ${event.users} users, shards: [${event.shards.join(', ')}]`,
    );

    // Here you could:
    // - Update metrics/monitoring
    // - Send notifications to other services
    // - Initialize periodic tasks
    // - Update database with cluster status
  }
}

/**
 * Client Shutdown Event Handler
 *
 * Handles cleanup when the Discord client shuts down
 */
@injectable()
export class ClientShutdownEventHandler implements IEventHandler<ClientShutdownEvent> {
  readonly eventType = 'client.shutdown';
  readonly handlerId = 'ClientShutdownEventHandler';

  async handle(event: ClientShutdownEvent): Promise<void> {
    Logger.info(`🛑 Client shutdown event received for cluster ${event.clusterId}`);
    Logger.info(`📋 Shutdown reason: ${event.reason || 'No reason provided'}`);

    // Here you could:
    // - Clean up resources
    // - Save state to database
    // - Notify monitoring systems
    // - Send alerts if unexpected shutdown
  }
}

/**
 * Guild Joined Event Handler
 *
 * Handles actions when the bot joins a new guild
 */
@injectable()
export class GuildJoinedEventHandler implements IEventHandler<GuildJoinedEvent> {
  readonly eventType = 'guild.joined';
  readonly handlerId = 'GuildJoinedEventHandler';

  constructor(@inject(TYPES.PrismaClient) private readonly prisma: PrismaClient) {}

  async handle(event: GuildJoinedEvent): Promise<void> {
    Logger.info(
      `🎊 Guild joined: ${event.guildName} (${event.guildId}) with ${event.memberCount} members`,
    );

    try {
      // Create or update server data in database
      await this.prisma.serverData.upsert({
        where: {
          id: event.guildId,
        },
        create: {
          id: event.guildId,
          name: event.guildName,
          iconUrl: null, // Will be updated later when we have access to guild details
          messageCount: 0,
          premiumStatus: false,
          lastMessageAt: new Date(),
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        update: {
          name: event.guildName,
          updatedAt: new Date(),
        },
      });

      Logger.info(
        `✅ Server data created/updated for guild: ${event.guildName} (${event.guildId})`,
      );
    }
    catch (error) {
      Logger.error(`❌ Failed to create/update server data for guild ${event.guildId}:`, error);
    }

    // Additional actions:
    // - Update guild count metrics
    // - Send welcome message to guild
    // - Log to analytics
    // - Check for premium features
    // - Initialize guild-specific settings
  }
}

/**
 * Guild Left Event Handler
 *
 * Handles cleanup when the bot leaves a guild
 */
@injectable()
export class GuildLeftEventHandler implements IEventHandler<GuildLeftEvent> {
  readonly eventType = 'guild.left';
  readonly handlerId = 'GuildLeftEventHandler';

  constructor(@inject(TYPES.PrismaClient) private readonly prisma: PrismaClient) {}

  async handle(event: GuildLeftEvent): Promise<void> {
    Logger.info(`👋 Guild left: ${event.guildName} (${event.guildId})`);

    try {
      // Delete related connections
      await this.prisma.connection.deleteMany({
        where: { serverId: event.guildId },
      });

      const deleted = await this.prisma.serverData.deleteMany({
        where: {
          id: event.guildId,
          connections: { none: {} }, // Ensure no connections exist
          infractions: { none: {} }, // Ensure no infractions exist
          serverBans: { none: {} }, // Ensure no server bans exist
        },
      });

      if (deleted.count === 0) {
        Logger.warn(
          `⚠️ Server data not-found or is being used as fkey to delete for guild: ${event.guildName} (${event.guildId})`,
        );

        Logger.info(
          `✅ Server data safely deleted for guild: ${event.guildName} (${event.guildId})`,
        );
      }
    }
    catch (error) {
      // If the server doesn't exist in DB, that's fine
      if (error instanceof Error && error.message.includes('Record to delete does not exist')) {
        Logger.debug(`Server data not found for guild ${event.guildId}, nothing to delete`);
      }
      else {
        Logger.error(
          `❌ Failed to process server data deletion for guild ${event.guildId}:`,
          error,
        );
      }
    }

    // Additional cleanup actions:
    // - Update metrics
    // - Remove guild-specific configurations
    // - Log for analytics
    // - Cancel premium subscriptions if applicable
  }
}
