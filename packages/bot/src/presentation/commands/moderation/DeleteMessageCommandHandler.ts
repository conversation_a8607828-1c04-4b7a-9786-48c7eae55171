/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { SlashCommandBuilder, ApplicationCommandType, ContextMenuCommandBuilder, MessageFlags } from 'discord.js';
import { injectable, inject } from 'inversify';
import { BaseCommandHandler, CommandCategory, type FlexibleCommandResponse } from '../BaseCommandHandler.js';
import { Context } from '../../../shared/context/Context.js';
import { TYPES } from '../../../shared/types/TYPES.js';
import type { MessageService } from '../../../domain/services/MessageService.js';
import type { HubService } from '../../../domain/services/HubService.js';
import type { ModerationService } from '../../../domain/services/ModerationService.js';

@injectable()
export class DeleteMessageCommandHandler extends BaseCommandHandler {
  readonly metadata = {
    name: 'deletemsg',
    description: 'Delete a message you sent using InterChat',
    category: CommandCategory.MODERATION,
    cooldown: 10, // 10 seconds
    guildOnly: true,
    permissions: [],
  };

  constructor(
    @inject(TYPES.MessageService) private readonly messageService: MessageService,
    @inject(TYPES.HubService) private readonly hubService: HubService,
    @inject(TYPES.ModerationService) private readonly moderationService: ModerationService,
  ) {
    super();
  }

  buildCommand() {
    return new SlashCommandBuilder()
      .setName(this.metadata.name)
      .setDescription(this.metadata.description)
      .addStringOption((option) =>
        option
          .setName('message')
          .setDescription('The message ID or message link of the message to delete')
          .setRequired(true),
      )
      .setContexts([0, 1]) // Guild and DM
      .setDefaultMemberPermissions(null);
  }

  /**
   * Build context menu command
   */
  buildContextMenuCommand(): ContextMenuCommandBuilder {
    return new ContextMenuCommandBuilder()
      .setName('Delete Message')
      .setType(ApplicationCommandType.Message);
  }

  async execute(ctx: Context): Promise<FlexibleCommandResponse> {
    // Ensure we're in a guild
    if (!ctx.inGuild()) {
      return {
        success: false,
        message: '❌ This command can only be used in servers.',
        flags: [MessageFlags.Ephemeral],
      };
    }

    await ctx.deferReply({ flags: ['Ephemeral'] });

    // Get target message ID
    const targetId = ctx.getTargetMessageId('message');
    if (!targetId) {
      return {
        success: false,
        message: '❌ Invalid message ID or link provided.',
        flags: [MessageFlags.Ephemeral],
      };
    }

    // Find the original message
    const originalMessage = await this.messageService.findOriginalMessage(targetId);
    if (!originalMessage) {
      return {
        success: false,
        message: '❌ Message not found or was not sent through InterChat.',
        flags: [MessageFlags.Ephemeral],
      };
    }

    // Get the hub
    const hub = await this.hubService.getHub(originalMessage.hubId);
    if (!hub) {
      return {
        success: false,
        message: '❌ Hub not found for this message.',
        flags: [MessageFlags.Ephemeral],
      };
    }

    // Check permissions
    const canDelete = await this.moderationService.canUserDeleteMessage(
      ctx.user.id,
      originalMessage,
      hub,
    );

    if (!canDelete) {
      return {
        success: false,
        message: '❌ You can only delete messages you sent, or you must be a hub moderator.',
        flags: [MessageFlags.Ephemeral],
      };
    }

    // Check if deletion is already in progress
    const isInProgress = await this.moderationService.isDeleteInProgress(originalMessage.id);
    if (isInProgress) {
      return {
        success: false,
        message: '⚠️ This message is already being deleted.',
        flags: [MessageFlags.Ephemeral],
      };
    }

    // Process the deletion
    const result = await this.moderationService.deleteMessageFromHub(
      hub.id,
      originalMessage.id,
      ctx.user.id,
    );

    // Log the deletion if user is a moderator
    await this.moderationService.logMessageDeletion(
      originalMessage,
      hub,
      ctx.user.id,
      ctx.user.username,
    );

    return {
      success: true,
      message: `✅ Message deleted successfully! Removed from ${result.deletedCount}/${result.totalCount} connected servers.`,
      flags: [MessageFlags.Ephemeral],
    };
  }
}
