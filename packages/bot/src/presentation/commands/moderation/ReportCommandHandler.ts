/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { SlashCommandBuilder, ApplicationCommandType, ContextMenuCommandBuilder, EmbedBuilder, MessageFlags } from 'discord.js';
import { injectable, inject } from 'inversify';
import { BaseCommandHandler, CommandCategory, type FlexibleCommandResponse } from '../BaseCommandHandler.js';
import { Context } from '../../../shared/context/Context.js';
import { TYPES } from '../../../shared/types/TYPES.js';
import type { MessageService } from '../../../domain/services/MessageService.js';
import type { HubService } from '../../../domain/services/HubService.js';
import type { ModerationService } from '../../../domain/services/ModerationService.js';

@injectable()
export class ReportCommandHandler extends BaseCommandHandler {
  readonly metadata = {
    name: 'report',
    description: 'Report a message or user to hub moderators',
    category: CommandCategory.MODERATION,
    cooldown: 60, // 1 minute to prevent spam
    guildOnly: true,
    permissions: [],
  };

  constructor(
    @inject(TYPES.MessageService) private readonly messageService: MessageService,
    @inject(TYPES.HubService) private readonly hubService: HubService,
    @inject(TYPES.ModerationService) private readonly moderationService: ModerationService,
  ) {
    super();
  }

  buildCommand() {
    return new SlashCommandBuilder()
      .setName(this.metadata.name)
      .setDescription(this.metadata.description)
      .addStringOption((option) =>
        option
          .setName('message')
          .setDescription('The message ID or message link to report')
          .setRequired(true),
      )
      .addStringOption((option) =>
        option
          .setName('reason')
          .setDescription('The reason for reporting this message')
          .setRequired(true)
          .addChoices(
            { name: 'Spam', value: 'spam' },
            { name: 'Harassment', value: 'harassment' },
            { name: 'Inappropriate Content', value: 'inappropriate' },
            { name: 'Hate Speech', value: 'hate_speech' },
            { name: 'NSFW Content', value: 'nsfw' },
            { name: 'Advertising', value: 'advertising' },
            { name: 'Other', value: 'other' },
          ),
      )
      .addStringOption((option) =>
        option
          .setName('details')
          .setDescription('Additional details about the report (optional)')
          .setRequired(false)
          .setMaxLength(500),
      )
      .setContexts([0, 1]) // Guild and DM
      .setDefaultMemberPermissions(null);
  }

  /**
   * Build context menu command
   */
  buildContextMenuCommand(): ContextMenuCommandBuilder {
    return new ContextMenuCommandBuilder()
      .setName('Report Message')
      .setType(ApplicationCommandType.Message);
  }

  async execute(ctx: Context): Promise<FlexibleCommandResponse> {
    // Ensure we're in a guild
    if (!ctx.inGuild()) {
      return {
        success: false,
        message: '❌ This command can only be used in servers.',
        flags: [MessageFlags.Ephemeral],
      };
    }

    await ctx.deferReply({ flags: ['Ephemeral'] });

    try {
      // Get target message ID
      const targetId = ctx.getTargetMessageId('message');
      if (!targetId) {
        return {
          success: false,
          message: '❌ Invalid message ID or link provided.',
          flags: [MessageFlags.Ephemeral],
        };
      }

      // Get report details
      const reason = ctx.options.getString('reason') ?? 'other';
      const details = ctx.options.getString('details');

      // Find the original message
      const originalMessage = await this.messageService.findOriginalMessage(targetId);
      if (!originalMessage) {
        return {
          success: false,
          message: '❌ Message not found or was not sent through InterChat.',
          flags: [MessageFlags.Ephemeral],
        };
      }

      // Get the hub
      const hub = await this.hubService.getHub(originalMessage.hubId);
      if (!hub) {
        return {
          success: false,
          message: '❌ Hub not found for this message.',
          flags: [MessageFlags.Ephemeral],
        };
      }

      // Check if user is trying to report their own message
      if (originalMessage.authorId === ctx.user.id) {
        return {
          success: false,
          message: '❌ You cannot report your own messages.',
          flags: [MessageFlags.Ephemeral],
        };
      }

      // Check for recent reports from this user
      const recentReports = await this.moderationService.getRecentReports(
        ctx.user.id,
        originalMessage.id,
      );

      if (recentReports.length > 0) {
        return {
          success: false,
          message: '❌ You have already reported this message recently.',
          flags: [MessageFlags.Ephemeral],
        };
      }

      // Create the report
      const report = await this.moderationService.createReport({
        messageId: originalMessage.id,
        hubId: hub.id,
        reporterId: ctx.user.id,
        reportedUserId: originalMessage.authorId,
        reason,
        details: details || undefined,
        guildId: ctx.guild.id,
        channelId: ctx.channel?.id || '',
      });

      // Notify hub moderators
      await this.moderationService.notifyHubModerators(hub, report);

      // Create confirmation embed
      const embed = new EmbedBuilder()
        .setTitle('✅ Report Submitted')
        .setColor(0x00ff00)
        .setDescription('Your report has been submitted to the hub moderators.')
        .addFields([
          {
            name: '🏠 Hub',
            value: hub.name,
            inline: true,
          },
          {
            name: '📝 Reason',
            value: reason ? this.formatReason(reason) : 'No reason provided',
            inline: true,
          },
          {
            name: '🆔 Report ID',
            value: `\`${report.id}\``,
            inline: true,
          },
        ])
        .setTimestamp()
        .setFooter({
          text: 'Thank you for helping keep the community safe',
        });

      if (details) {
        embed.addFields({
          name: '📋 Details',
          value: details,
        });
      }

      return {
        success: true,
        embed,
        flags: [MessageFlags.Ephemeral],
      };
    }
    catch (_error) {
      return {
        success: false,
        message: '❌ An error occurred while submitting your report.',
        flags: [MessageFlags.Ephemeral],
      };
    }
  }

  private formatReason(reason: string): string {
    const reasonMap: Record<string, string> = {
      spam: '🚫 Spam',
      harassment: '😡 Harassment',
      inappropriate: '⚠️ Inappropriate Content',
      hate_speech: '🔥 Hate Speech',
      nsfw: '🔞 NSFW Content',
      advertising: '📢 Advertising',
      other: '❓ Other',
    };

    return reasonMap[reason] || reason;
  }
}
