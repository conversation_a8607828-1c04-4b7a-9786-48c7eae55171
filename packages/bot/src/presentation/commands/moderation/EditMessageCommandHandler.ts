/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { SlashCommandBuilder, ApplicationCommandType, ContextMenuCommandBuilder, MessageFlags } from 'discord.js';
import { injectable, inject } from 'inversify';
import { BaseCommandHandler, CommandCategory, type FlexibleCommandResponse } from '../BaseCommandHandler.js';
import { Context } from '../../../shared/context/Context.js';
import { TYPES } from '../../../shared/types/TYPES.js';
import type { MessageService } from '../../../domain/services/MessageService.js';
import type { HubService } from '../../../domain/services/HubService.js';
import type { ModerationService } from '../../../domain/services/ModerationService.js';

@injectable()
export class EditMessageCommandHandler extends BaseCommandHandler {
  readonly metadata = {
    name: 'editmsg',
    description: 'Edit a message you sent using InterChat',
    category: CommandCategory.MODERATION,
    cooldown: 3, // 3 seconds
    guildOnly: true,
    permissions: [],
  };

  constructor(
    @inject(TYPES.MessageService) private readonly messageService: MessageService,
    @inject(TYPES.HubService) private readonly hubService: HubService,
    @inject(TYPES.ModerationService) private readonly moderationService: ModerationService,
  ) {
    super();
  }

  buildCommand() {
    return new SlashCommandBuilder()
      .setName(this.metadata.name)
      .setDescription(this.metadata.description)
      .addStringOption((option) =>
        option
          .setName('message')
          .setDescription('The message ID or message link of the message to edit')
          .setRequired(true),
      )
      .addStringOption((option) =>
        option
          .setName('content')
          .setDescription('The new content for the message')
          .setRequired(true),
      )
      .setContexts([0, 1]) // Guild and DM
      .setDefaultMemberPermissions(null);
  }

  /**
   * Build context menu command
   */
  buildContextMenuCommand(): ContextMenuCommandBuilder {
    return new ContextMenuCommandBuilder()
      .setName('Edit Message')
      .setType(ApplicationCommandType.Message);
  }

  async execute(ctx: Context): Promise<FlexibleCommandResponse> {
    // Ensure we're in a guild
    if (!ctx.inGuild()) {
      return {
        success: false,
        message: '❌ This command can only be used in servers.',
        flags: [MessageFlags.Ephemeral],
      };
    }

    await ctx.deferReply({ flags: ['Ephemeral'] });

    try {
      // Get target message ID
      const targetId = ctx.getTargetMessageId('message');
      if (!targetId) {
        return {
          success: false,
          message: '❌ Invalid message ID or link provided.',
          flags: [MessageFlags.Ephemeral],
        };
      }

      // Get new content
      const newContent = ctx.options.getString('content');
      if (!newContent || newContent.length > 2000) {
        return {
          success: false,
          message: '❌ New content must be between 1 and 2000 characters.',
          flags: [MessageFlags.Ephemeral],
        };
      }

      // Find the original message
      const originalMessage = await this.messageService.findOriginalMessage(targetId);
      if (!originalMessage) {
        return {
          success: false,
          message: '❌ Message not found or was not sent through InterChat.',
          flags: [MessageFlags.Ephemeral],
        };
      }

      // Get the hub
      const hub = await this.hubService.getHub(originalMessage.hubId);
      if (!hub) {
        return {
          success: false,
          message: '❌ Hub not found for this message.',
          flags: [MessageFlags.Ephemeral],
        };
      }

      // Check permissions (only author can edit)
      if (originalMessage.authorId !== ctx.user.id) {
        return {
          success: false,
          message: '❌ You can only edit messages you sent.',
          flags: [MessageFlags.Ephemeral],
        };
      }

      // Check if message is too old (15 minutes)
      const messageAge = Date.now() - originalMessage.createdAt.getTime();
      if (messageAge > 15 * 60 * 1000) {
        return {
          success: false,
          message: '❌ Messages older than 15 minutes cannot be edited.',
          flags: [MessageFlags.Ephemeral],
        };
      }

      // Process the edit
      const result = await this.moderationService.editMessageInHub(
        hub.id,
        originalMessage.id,
        newContent,
        ctx.user.id,
      );

      // Log the edit
      await this.moderationService.logMessageEdit(
        originalMessage,
        hub,
        ctx.user.id,
        ctx.user.username,
        newContent,
      );

      return {
        success: true,
        message: `✅ Message edited successfully! Updated in ${result.editedCount}/${result.totalCount} connected servers.`,
        flags: [MessageFlags.Ephemeral],
      };
    }
    catch (_error) {
      return {
        success: false,
        message: '❌ An error occurred while editing the message.',
        flags: [MessageFlags.Ephemeral],
      };
    }
  }
}
