/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { Slash<PERSON>ommandBuilder, EmbedBuilder, MessageFlags } from 'discord.js';
import { injectable, inject } from 'inversify';
import {
  BaseCommandHandler,
  CommandCategory,
  type FlexibleCommandResponse,
} from '../BaseCommandHandler.js';
import { Context } from '../../../shared/context/Context.js';
import { TYPES } from '../../../shared/types/TYPES.js';
import type { UserService } from '../../../domain/services/UserService.js';
import { createPaginatedEmbedList, PaginationManager } from '../../../shared/ui/PaginationManager.js';

interface InboxMessage {
  id: string;
  from: string;
  subject: string;
  content: string;
  createdAt: Date;
  read: boolean;
}

@injectable()
export class InboxCommandHandler extends BaseCommandHandler {
  readonly metadata = {
    name: 'inbox',
    description: 'View your InterChat inbox messages',
    category: CommandCategory.UTILITIES,
    cooldown: 5, // 5 seconds
    guildOnly: false,
    permissions: [],
  };

  constructor(@inject(TYPES.UserService) private readonly userService: UserService) {
    super();
  }

  buildCommand() {
    return new SlashCommandBuilder()
      .setName(this.metadata.name)
      .setDescription(this.metadata.description)
      .addSubcommand((subcommand) =>
        subcommand
          .setName('list')
          .setDescription('List all your inbox messages')
          .addBooleanOption((option) =>
            option.setName('unread').setDescription('Show only unread messages').setRequired(false),
          ),
      )
      .addSubcommand((subcommand) =>
        subcommand
          .setName('read')
          .setDescription('Read a specific message')
          .addStringOption((option) =>
            option.setName('id').setDescription('The ID of the message to read').setRequired(true),
          ),
      )
      .addSubcommand((subcommand) =>
        subcommand.setName('clear').setDescription('Clear all read messages from your inbox'),
      )
      .setContexts([0, 1]); // Guild and DM
  }

  async execute(ctx: Context): Promise<FlexibleCommandResponse> {
    const subcommand = ctx.options.getSubcommand(true);

    switch (subcommand) {
      case 'list':
        return await this.handleList(ctx);
      case 'read':
        return await this.handleRead(ctx);
      case 'clear':
        return await this.handleClear(ctx);
      default:
        return {
          success: false,
          message: '❌ Invalid subcommand.',
          flags: [MessageFlags.Ephemeral],
        };
    }
  }

  private async handleList(ctx: Context): Promise<FlexibleCommandResponse> {
    const unreadOnly = ctx.options.getBoolean('unread') || false;

    try {
      // Get user's inbox messages
      const messages = await this.userService.getInboxMessages(ctx.user.id, unreadOnly);

      if (messages.length === 0) {
        const message = unreadOnly
          ? '📭 You have no unread messages in your inbox.'
          : '📭 Your inbox is empty.';

        return {
          success: true,
          message,
          flags: [MessageFlags.Ephemeral],
        };
      }

      // Create pagination options
      const paginationOptions = await createPaginatedEmbedList(
        messages,
        (message: InboxMessage, index: number) => {
          const status = message.read ? '📖' : '📩';
          const date = `<t:${Math.floor(message.createdAt.getTime() / 1000)}:d>`;
          return (
            `${status} **${index + 1}.** ${message.subject}\n` +
            `From: ${message.from} • ${date}\n` +
            `ID: \`${message.id}\``
          );
        },
        {
          title: unreadOnly ? '📩 Unread Messages' : '📬 Inbox Messages',
          description: `${messages.length} message${messages.length === 1 ? '' : 's'} found`,
          itemsPerPage: 5,
          color: 0x3498db,
        },
      );

      // Create pagination manager
      const pagination = new PaginationManager(paginationOptions);
      await pagination.start(ctx);

      return; // Pagination manager handles the response
    }
    catch (_error) {
      return {
        success: false,
        message: '❌ An error occurred while fetching your inbox.',
        flags: [MessageFlags.Ephemeral],
      };
    }
  }

  private async handleRead(ctx: Context): Promise<FlexibleCommandResponse> {
    const messageId = ctx.options.getString('id');

    try {
      // Get the specific message
      const message = messageId
        ? await this.userService.getInboxMessage(ctx.user.id, messageId)
        : null;

      if (!message || !messageId) {
        return {
          success: false,
          message: '❌ Message not found in your inbox.',
          flags: [MessageFlags.Ephemeral],
        };
      }

      // Mark as read if not already
      if (!message.read) {
        await this.userService.markInboxMessageAsRead(ctx.user.id, messageId);
      }

      // Create detailed embed
      const embed = new EmbedBuilder()
        .setTitle('📖 Inbox Message')
        .setColor(0x3498db)
        .addFields([
          {
            name: '📄 Subject',
            value: message.subject,
            inline: false,
          },
          {
            name: '👤 From',
            value: message.from,
            inline: true,
          },
          {
            name: '📅 Date',
            value: `<t:${Math.floor(message.createdAt.getTime() / 1000)}:F>`,
            inline: true,
          },
          {
            name: '💬 Message',
            value:
              message.content.length > 1000
                ? `${message.content.substring(0, 1000)}...`
                : message.content,
            inline: false,
          },
        ])
        .setTimestamp()
        .setFooter({
          text: `Message ID: ${message.id}`,
        });

      return {
        success: true,
        embed,
        flags: [MessageFlags.Ephemeral],
      };
    }
    catch (_error) {
      return {
        success: false,
        message: '❌ An error occurred while reading the message.',
        flags: [MessageFlags.Ephemeral],
      };
    }
  }

  private async handleClear(ctx: Context): Promise<FlexibleCommandResponse> {
    try {
      // Clear read messages
      const clearedCount = await this.userService.clearReadInboxMessages(ctx.user.id);

      if (clearedCount === 0) {
        return {
          success: true,
          message: '📭 No read messages to clear.',
          flags: [MessageFlags.Ephemeral],
        };
      }

      return {
        success: true,
        message: `✅ Cleared ${clearedCount} read message${clearedCount === 1 ? '' : 's'} from your inbox.`,
        flags: [MessageFlags.Ephemeral],
      };
    }
    catch (_error) {
      return {
        success: false,
        message: '❌ An error occurred while clearing your inbox.',
        flags: [MessageFlags.Ephemeral],
      };
    }
  }
}
