/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import {
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
  ContainerBuilder,
  MessageFlags,
  SectionBuilder,
  SeparatorSpacingSize,
  SlashCommandBuilder,
  TextDisplayBuilder,
} from 'discord.js';
import { inject, injectable } from 'inversify';
import type {
  ServerConnection,
  ServerRepository,
} from '../../../domain/repositories/ServerRepository.js';
import { Context } from '../../../shared/context/Context.js';
import { TYPES } from '../../../shared/types/TYPES.js';
import { CustomID } from '../../../shared/utils/CustomID.js';
import {
  BaseCommandHandler,
  CommandCategory,
  type FlexibleCommandResponse,
} from '../BaseCommandHandler.js';

/**
 * Helper function to create compressed CustomIDs for interactions
 */
function createCompressedCustomId(prefix: string, suffix: string, params: string[] = []): string {
  const handlerId = `${prefix}:${suffix}`;
  return CustomID.withOptions(handlerId, params, { forceCompression: true }).toString();
}

@injectable()
export default class ConnectionsCommandHandler extends BaseCommandHandler {
  readonly metadata = {
    name: 'connections',
    description: 'View all hub connections in this server',
    category: CommandCategory.UTILITIES,
    cooldown: 5, // 5 seconds
    guildOnly: true,
    permissions: [],
  };

  constructor(@inject(TYPES.ServerRepository) public readonly serverRepository: ServerRepository) {
    super();
  }

  buildCommand(): SlashCommandBuilder {
    return new SlashCommandBuilder()
      .setName(this.metadata.name)
      .setDescription(this.metadata.description)
      .setContexts([0]) as SlashCommandBuilder; // Guild only
  }

  async execute(ctx: Context): Promise<FlexibleCommandResponse> {
    if (!ctx.inGuild()) {
      return {
        success: false,
        message: '❌ This command can only be used in servers.',
        flags: [MessageFlags.Ephemeral],
      };
    }

    try {
      const connections = await this.serverRepository.getServerConnections(ctx.guild.id);
      const container =
        connections.length === 0
          ? this.buildNoConnectionsContainer(ctx.guild.name)
          : this.buildConnectionsContainer(
            connections,
            ctx.guild.name,
            ctx.member.permissions.has('ManageChannels'),
          );

      await ctx.reply({
        components: [container],
        flags: ['IsComponentsV2'],
      });

      return { success: true };
    }
    catch (_error) {
      return {
        success: false,
        message: '❌ An error occurred while fetching connections.',
        flags: [MessageFlags.Ephemeral],
      };
    }
  }

  /**
   * Build container for when there are no connections
   */
  private buildNoConnectionsContainer(guildName: string): ContainerBuilder {
    const container = new ContainerBuilder();

    container.addTextDisplayComponents(
      new TextDisplayBuilder().setContent(
        '## 📋 Server Connections\n❌ **No active connections found.**\n\n💡 Use `/setup channel` to connect a channel to a hub and start chatting with other servers!',
      ),
    );

    container.addSeparatorComponents((separator) =>
      separator.setSpacing(SeparatorSpacingSize.Large),
    );

    const setupSection = new SectionBuilder()
      .addTextDisplayComponents(
        new TextDisplayBuilder().setContent(
          '### 🚀 Get Started\nConnect your first channel to start chatting with other servers!',
        ),
      )
      .setButtonAccessory(
        new ButtonBuilder()
          .setCustomId(createCompressedCustomId('connections', 'setup_help'))
          .setLabel('How to Connect')
          .setStyle(ButtonStyle.Primary)
          .setEmoji('❓'),
      );

    container.addSectionComponents(setupSection);
    container.addTextDisplayComponents(
      new TextDisplayBuilder().setContent(`**Server:** ${guildName}`),
    );

    return container;
  }

  /**
   * Build container for displaying connections with pagination
   */
  public buildConnectionsContainer(
    connections: ServerConnection[],
    guildName: string,
    hasManagePermission: boolean,
    page = 0,
  ): ContainerBuilder {
    const container = new ContainerBuilder();
    const itemsPerPage = 5;
    const totalPages = Math.ceil(connections.length / itemsPerPage);
    const isPaginated = connections.length > itemsPerPage;

    // Get connections for current page
    const connectionsToShow = isPaginated
      ? connections.slice(page * itemsPerPage, (page + 1) * itemsPerPage)
      : connections;

    // Header
    const pageInfo = isPaginated ? ` (Page ${page + 1}/${totalPages})` : '';
    container.addTextDisplayComponents(
      new TextDisplayBuilder().setContent(
        `## 📋 Server Connections (${connections.length})${pageInfo}\n🔗 Active connections:`,
      ),
    );

    container.addSeparatorComponents((separator) =>
      separator.setSpacing(SeparatorSpacingSize.Large),
    );

    // Add each connection
    for (const connection of connectionsToShow) {
      const section = new SectionBuilder()
        .addTextDisplayComponents(
          new TextDisplayBuilder().setContent(
            `### 🏠 ${connection.hubName}\n📍 <#${connection.channelId}>\n⏰ Connected <t:${Math.floor(connection.connectedAt.getTime() / 1000)}:R>`,
          ),
        )
        .setButtonAccessory(
          new ButtonBuilder()
            .setCustomId(
              createCompressedCustomId('connections', 'configure', [connection.channelId]),
            )
            .setLabel('Configure')
            .setStyle(ButtonStyle.Secondary)
            .setEmoji('⚙️'),
        );

      container.addSectionComponents(section);
    }

    // Pagination buttons
    if (isPaginated) {
      const row = new ActionRowBuilder<ButtonBuilder>();

      if (page > 0) {
        row.addComponents(
          new ButtonBuilder()
            .setCustomId(createCompressedCustomId('connections', 'page', [String(page - 1)]))
            .setLabel('◀️ Previous')
            .setStyle(ButtonStyle.Secondary),
        );
      }

      row.addComponents(
        new ButtonBuilder()
          .setCustomId('connections_page_indicator')
          .setLabel(`${page + 1} / ${totalPages}`)
          .setStyle(ButtonStyle.Secondary)
          .setDisabled(true),
      );

      if (page < totalPages - 1) {
        row.addComponents(
          new ButtonBuilder()
            .setCustomId(createCompressedCustomId('connections', 'page', [String(page + 1)]))
            .setLabel('Next ▶️')
            .setStyle(ButtonStyle.Secondary),
        );
      }

      container.addActionRowComponents(row);
    }

    // Management section
    if (hasManagePermission) {
      container.addSeparatorComponents((separator) =>
        separator.setSpacing(SeparatorSpacingSize.Small),
      );

      const manageSection = new SectionBuilder()
        .addTextDisplayComponents(
          new TextDisplayBuilder().setContent(
            "### 🛠️ Management\nManage your server's hub connections",
          ),
        )
        .setButtonAccessory(
          new ButtonBuilder()
            .setCustomId(createCompressedCustomId('connections', 'manage_help'))
            .setLabel('Manage Connections')
            .setStyle(ButtonStyle.Secondary)
            .setEmoji('⚙️'),
        );

      container.addSectionComponents(manageSection);
    }

    container.addTextDisplayComponents(
      new TextDisplayBuilder().setContent(`**Server:** ${guildName}`),
    );

    return container;
  }
}
