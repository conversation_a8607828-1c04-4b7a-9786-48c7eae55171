/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { SlashCommandBuilder, ApplicationCommandType, ContextMenuCommandBuilder, EmbedBuilder, MessageFlags } from 'discord.js';
import { injectable, inject } from 'inversify';
import { BaseCommandHandler, CommandCategory, type FlexibleCommandResponse } from '../BaseCommandHandler.js';
import { Context } from '../../../shared/context/Context.js';
import { TYPES } from '../../../shared/types/TYPES.js';
import type { MessageService } from '../../../domain/services/MessageService.js';
import type { HubService } from '../../../domain/services/HubService.js';
import type { UserService } from '../../../domain/services/UserService.js';

@injectable()
export class MessageInfoCommandHandler extends BaseCommandHandler {
  readonly metadata = {
    name: 'msginfo',
    description: 'Get information about an InterChat message',
    category: CommandCategory.INFORMATION,
    cooldown: 5, // 5 seconds
    guildOnly: true,
    permissions: [],
  };

  constructor(
    @inject(TYPES.MessageService) private readonly messageService: MessageService,
    @inject(TYPES.HubService) private readonly hubService: HubService,
    @inject(TYPES.UserService) private readonly userService: UserService,
  ) {
    super();
  }

  buildCommand() {
    return new SlashCommandBuilder()
      .setName(this.metadata.name)
      .setDescription(this.metadata.description)
      .addStringOption((option) =>
        option
          .setName('message')
          .setDescription('The message ID or message link to get information about')
          .setRequired(true),
      )
      .setContexts([0, 1]) // Guild and DM
      .setDefaultMemberPermissions(null);
  }

  /**
   * Build context menu command
   */
  buildContextMenuCommand(): ContextMenuCommandBuilder {
    return new ContextMenuCommandBuilder()
      .setName('Message Info')
      .setType(ApplicationCommandType.Message);
  }

  async execute(ctx: Context): Promise<FlexibleCommandResponse> {
    // Ensure we're in a guild
    if (!ctx.inGuild()) {
      return {
        success: false,
        message: '❌ This command can only be used in servers.',
        flags: [MessageFlags.Ephemeral],
      };
    }

    await ctx.deferReply({ flags: ['Ephemeral'] });

    try {
      // Get target message ID
      const targetId = ctx.getTargetMessageId('message');
      if (!targetId) {
        return {
          success: false,
          message: '❌ Invalid message ID or link provided.',
          flags: [MessageFlags.Ephemeral],
        };
      }

      // Find the original message
      const originalMessage = await this.messageService.findOriginalMessage(targetId);
      if (!originalMessage) {
        return {
          success: false,
          message: '❌ Message not found or was not sent through InterChat.',
          flags: [MessageFlags.Ephemeral],
        };
      }

      // Get the hub
      const hub = await this.hubService.getHub(originalMessage.hubId);
      if (!hub) {
        return {
          success: false,
          message: '❌ Hub not found for this message.',
          flags: [MessageFlags.Ephemeral],
        };
      }

      // Get author information
      const author = await this.userService.getUser(originalMessage.authorId);

      // Get connected servers count
      const connections = await this.messageService.getMessageConnections(originalMessage.id);

      // Create info embed
      const embed = new EmbedBuilder()
        .setTitle('📋 Message Information')
        .setColor(0x3498db)
        .addFields([
          {
            name: '👤 Author',
            value: `<@${originalMessage.authorId}> (${originalMessage.authorId})`,
            inline: true,
          },
          {
            name: '🏠 Hub',
            value: `${hub.name} (${hub.id})`,
            inline: true,
          },
          {
            name: '📅 Sent',
            value: `<t:${Math.floor(originalMessage.createdAt.getTime() / 1000)}:F>`,
            inline: true,
          },
          {
            name: '🔗 Message ID',
            value: `\`${originalMessage.id}\``,
            inline: true,
          },
          {
            name: '🌐 Connected Servers',
            value: `${connections.length}`,
            inline: true,
          },
          {
            name: '📝 Content Length',
            value: `${originalMessage.content?.length || 0} characters`,
            inline: true,
          },
        ])
        .setTimestamp();

      // Add content preview if available
      if (originalMessage.content) {
        const preview = originalMessage.content.length > 500
          ? `${originalMessage.content.substring(0, 500)}...`
          : originalMessage.content;

        embed.addFields({
          name: '📄 Content Preview',
          value: `\`\`\`${preview}\`\`\``,
        });
      }

      // Add author avatar if available
      if (author?.image) {
        embed.setThumbnail(`https://cdn.discordapp.com/avatars/${author.id}/${author.image}.png`);
      }

      return {
        success: true,
        embed,
        flags: [MessageFlags.Ephemeral],
      };
    }
    catch (_error) {
      return {
        success: false,
        message: '❌ An error occurred while fetching message information.',
        flags: [MessageFlags.Ephemeral],
      };
    }
  }
}
