/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { injectable } from 'inversify';
import { SlashCommandBuilder, EmbedBuilder } from 'discord.js';
import { BaseCommandHandler, CommandResult, CommandCategory } from '../BaseCommandHandler.js';
import type { Context } from '../../../shared/context/index.js';

@injectable()
export class PingCommandHandler extends BaseCommandHandler {
  readonly metadata = {
    name: 'ping',
    description: "🏓 Check the bot's latency and response time",
    category: CommandCategory.INFORMATION,
    cooldown: 3, // 3 seconds
    ownerOnly: false,
    guildOnly: false,
    permissions: [],
  };

  buildCommand(): SlashCommandBuilder {
    return new SlashCommandBuilder()
      .setName(this.metadata.name)
      .setDescription(this.metadata.description);
  }

  async execute(ctx: Context): Promise<CommandResult> {
    // Start timing for API latency
    const startTime = Date.now();

    // Send initial message
    await ctx.deferReply();

    // Calculate API latency
    const apiLatency = Date.now() - startTime;

    // Get WebSocket ping
    const wsLatency = ctx.client.ws.ping;

    // Determine status based on latency
    const getStatus = (ping: number) => {
      if (ping < 100) return { emoji: '🟢', text: 'Excellent' };
      if (ping < 200) return { emoji: '🟡', text: 'Good' };
      if (ping < 500) return { emoji: '🟠', text: 'Fair' };
      return { emoji: '🔴', text: 'Poor' };
    };

    const wsStatus = getStatus(wsLatency);
    const apiStatus = getStatus(apiLatency);

    const embed = new EmbedBuilder()
      .setColor(wsLatency < 200 ? 0x00ff00 : wsLatency < 500 ? 0xffff00 : 0xff0000)
      .setTitle('🏓 Pong!')
      .setDescription('Latency information for InterChat')
      .addFields([
        {
          name: '📡 WebSocket Latency',
          value: `${wsStatus.emoji} **${wsLatency}ms** (${wsStatus.text})`,
          inline: true,
        },
        {
          name: '🔄 API Latency',
          value: `${apiStatus.emoji} **${apiLatency}ms** (${apiStatus.text})`,
          inline: true,
        },
        {
          name: '⏱️ Uptime',
          value: `**${this.formatUptime(ctx.client.uptime || 0)}**`,
          inline: true,
        },
      ])
      .setFooter({
        text: `Cluster ${ctx.client.cluster?.id || 0} • Shard ${ctx.client.cluster?.shardList?.join(', ') || '0'}`,
      })
      .setTimestamp();

    await ctx.editReply({ embeds: [embed] });

    return {
      success: true,
      message: 'Ping command executed successfully',
      ephemeral: false,
    };
  }

  /**
   * Format uptime in a human-readable way
   */
  private formatUptime(milliseconds: number): string {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `${days}d ${hours % 24}h ${minutes % 60}m`;
    }
    else if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    }
    else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    }
    else {
      return `${seconds}s`;
    }
  }
}

export default PingCommandHandler;
