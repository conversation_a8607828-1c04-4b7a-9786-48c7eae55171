/**
 * Copyright (c) 2024 InterChat
 *
 * This file is part of InterChat, licensed under the AGPL-3.0 license.
 * See the LICENSE file in the root directory for license information.
 */

/**
 * Type definitions for the flexible command architecture
 *
 * Provides comprehensive type safety for all command patterns and responses.
 */

import type {
  ActionRowBuilder,
  AttachmentBuilder,
  ChatInputCommandInteraction,
  ContextMenuCommandInteraction,
  EmbedBuilder,
  MessageActionRowComponentBuilder,
  MessageFlags,
  MessageMentionOptions,
} from 'discord.js';
import type { Context } from '../../../shared/context/Context.js';

/**
 * Command execution result for structured responses
 */
export interface CommandResult {
  readonly success: boolean;
  readonly message?: string;
  readonly embed?: EmbedBuilder;
  readonly ephemeral?: boolean;
  readonly followUp?: string;
}

/**
 * Enhanced command execution result with Discord components support
 */
export interface EnhancedCommandResult extends CommandResult {
  readonly components?: ActionRowBuilder<MessageActionRowComponentBuilder>[];
  readonly files?: AttachmentBuilder[];
  readonly allowedMentions?: MessageMentionOptions;
  readonly flags?: MessageFlags[];
}

/**
 * Response mode for command execution
 */
export enum CommandResponseMode {
  /** Command handles response directly via interaction.reply() - returns void */
  DIRECT = 'direct',
  /** Command returns CommandResult object for registry to process */
  STRUCTURED = 'structured',
  /** Command returns enhanced result with full Discord features */
  ENHANCED = 'enhanced',
}

/**
 * Flexible command response - can be void, CommandResult, or EnhancedCommandResult
 */
export type FlexibleCommandResponse = void | CommandResult | EnhancedCommandResult;

/**
 * Type guard to check if response is CommandResult
 */
export function isCommandResult(response: FlexibleCommandResponse): response is CommandResult {
  return (
    response !== undefined &&
    response !== null &&
    typeof response === 'object' &&
    'success' in response
  );
}

/**
 * Type guard to check if response is EnhancedCommandResult
 */
export function isEnhancedCommandResult(
  response: FlexibleCommandResponse,
): response is EnhancedCommandResult {
  return (
    isCommandResult(response) &&
    ('components' in response ||
      'files' in response ||
      'allowedMentions' in response ||
      'flags' in response)
  );
}

/**
 * Type guard to check if response is void (direct handling)
 */
export function isDirectResponse(response: FlexibleCommandResponse): response is void {
  return response === undefined || response === null;
}

/**
 * Interface for subcommand handlers
 */
export interface ISubcommandHandler {
  readonly name: string;
  readonly description: string;
  execute(ctx: Context): Promise<FlexibleCommandResponse>;
}

/**
 * Subcommand registry type
 */
export type SubcommandRegistry = Map<string, ISubcommandHandler>;

/**
 * Command metadata interface
 */
export interface CommandMetadata {
  readonly name: string;
  readonly description: string;
  readonly category: CommandCategory;
  readonly cooldown?: number;
  readonly permissions?: ('STAFF' | 'DEVELOPER')[];
  readonly guildOnly?: boolean;
  readonly ownerOnly?: boolean;
  readonly premiumOnly?: boolean;
}

/**
 * Command categories
 */
export enum CommandCategory {
  DONATION = 'donation',
  HUB = 'hub',
  INFORMATION = 'information',
  MODERATION = 'moderation',
  STAFF = 'staff',
  CONFIG = 'config',
  USERPHONE = 'userphone',
  UTILITIES = 'utilities',
}

/**
 * Command execution context type
 */
export type CommandExecutionContext = Context;

/**
 * Command handler interface
 */
export interface ICommandHandler {
  readonly metadata: CommandMetadata;
  execute(ctx: CommandExecutionContext): Promise<FlexibleCommandResponse>;
  checkPermissions(ctx: CommandExecutionContext): Promise<boolean>;
}

/**
 * Response processing result
 */
export interface ResponseProcessingResult {
  readonly processed: boolean;
  readonly error?: Error;
}

/**
 * Command execution options
 */
export interface CommandExecutionOptions {
  readonly skipCooldown?: boolean;
  readonly skipPermissions?: boolean;
  readonly deferResponse?: boolean;
}

/**
 * Command registry interface
 */
export interface ICommandRegistry {
  executeCommand(
    interaction: ChatInputCommandInteraction | ContextMenuCommandInteraction,
    options?: CommandExecutionOptions,
  ): Promise<void>;
  getCommand(name: string): ICommandHandler | undefined;
  getAllCommands(): ICommandHandler[];
}

/**
 * Utility type for extracting command result type
 */
export type ExtractCommandResult<T> = T extends (...args: unknown[]) => Promise<infer R>
  ? R
  : never;

/**
 * Utility type for command handler methods
 */
export type CommandHandlerMethod<T extends ICommandHandler> = T['execute'];

/**
 * Type for command response validator
 */
export type CommandResponseValidator = (response: FlexibleCommandResponse) => boolean;

/**
 * Type for command error handler
 */
export type CommandErrorHandler = (
  error: Error,
  ctx: CommandExecutionContext,
) => Promise<CommandResult>;
