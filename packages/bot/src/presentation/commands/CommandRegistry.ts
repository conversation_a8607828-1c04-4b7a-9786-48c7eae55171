/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import {
  ChatInputCommandInteraction,
  Collection,
  InteractionEditReplyOptions,
  Message,
  MessageFlags,
  MessageReplyOptions,
  SlashCommandBuilder,
  SlashCommandOptionsOnlyBuilder,
  SlashCommandSubcommandsOnlyBuilder,
} from 'discord.js';
import { inject, injectable, multiInject } from 'inversify';
import * as Sentry from '@sentry/node';
import type { RedisCooldownService } from '../../services/RedisCooldownService.js';
import { InteractionContext } from '../../shared/context/index.js';
import { PrefixContext } from '../../shared/context/PrefixContext.js';
import { TYPES } from '../../shared/types/TYPES.js';
import {
  BaseCommandHandler,
  CommandResult,
  EnhancedCommandResult,
  FlexibleCommandResponse,
} from './BaseCommandHandler.js';

import { Logger } from '../../shared/utils/Logger.js';
import { SentryService } from '../../infrastructure/observability/SentryService.js';

/**
 * Command Registry
 *
 * Manages all Discord slash commands and their execution.
 * Implements the Registry pattern with dependency injection.
 */
@injectable()
export class CommandRegistry {
  private commands = new Collection<string, BaseCommandHandler>();
  // Remove in-memory cooldowns - now using Redis-based distributed cooldowns
  // private cooldowns = new Collection<string, Collection<string, number>>();

  constructor(
    @multiInject(TYPES.PresentationCommandHandler)
    private commandHandlers: BaseCommandHandler[],
    @inject(TYPES.RedisCooldownService)
    private cooldownService: RedisCooldownService,
    @inject(TYPES.SentryService)
    private sentry: SentryService,
  ) {
    this.registerCommands();
  }

  /**
   * Register all command handlers
   */
  private registerCommands(): void {
    for (const handler of this.commandHandlers) {
      this.commands.set(handler.metadata.name, handler);
      Logger.debug(`Registered command: ${handler.metadata.name}`);
    }
  }

  /**
   * Get all command data for Discord API registration
   */
  getCommandData(): (
    | SlashCommandBuilder
    | SlashCommandOptionsOnlyBuilder
    | SlashCommandSubcommandsOnlyBuilder
  )[] {
    return this.commands.map((command) => command.buildCommand());
  }

  /**
   * Execute a slash command
   */
  async executeCommand(interaction: ChatInputCommandInteraction): Promise<void> {
    const commandName = interaction.commandName;
    const command = this.commands.get(commandName);

    if (!command) {
      await this.handleUnknownCommand(interaction);
      return;
    }

    // Execute command with isolated Sentry scope
    await this.sentry.withScopeAsync(async (scope) => {
      // Set scoped context (won't affect other operations)
      this.setupCommandContext(interaction, command, scope);

      await this.sentry.startSpan(`command-${commandName}`, 'command.execute', async (span) => {
        try {
          await this.executeCommandWithMonitoring(interaction, command, span);
        }
        catch (error) {
          await this.handleCommandExecutionError(interaction, command, error, span);
        }
      });
    });
  }

  /**
   * Set up Sentry context for command execution
   */
  private setupCommandContext(
    interaction: ChatInputCommandInteraction,
    command: BaseCommandHandler,
    scope: Sentry.Scope,
  ): void {
    const commandName = command.metadata.name;

    scope.setContext('command', {
      name: commandName,
      category: command.metadata.category,
      guildId: interaction.guild?.id,
      channelId: interaction.channel?.id,
      userId: interaction.user.id,
      username: interaction.user.username,
      clusterId: interaction.client.cluster?.id,
      shardId: interaction.guild?.shardId,
    });

    scope.setUser({
      id: interaction.user.id,
      username: interaction.user.username,
    });

    scope.addBreadcrumb({
      message: `Command ${commandName} executed`,
      category: 'command.execute',
      level: 'info',
      timestamp: Date.now() / 1000,
    });
  }

  /**
   * Set up Sentry context for prefix command execution
   */
  private setupPrefixCommandContext(
    message: Message,
    command: BaseCommandHandler,
    scope: Sentry.Scope,
    args: string[],
  ): void {
    const commandName = command.metadata.name;

    scope.setContext('prefix_command', {
      name: commandName,
      category: command.metadata.category,
      guildId: message.guild?.id,
      channelId: message.channel?.id,
      userId: message.author.id,
      username: message.author.username,
      args: args.length,
      clusterId: message.client.cluster?.id,
      shardId: message.guild?.shardId,
    });

    scope.setUser({
      id: message.author.id,
      username: message.author.username,
    });

    scope.addBreadcrumb({
      message: `Prefix command ${commandName} executed`,
      category: 'command.prefix',
      level: 'info',
      timestamp: Date.now() / 1000,
    });
  }

  /**
   * Execute command with performance monitoring
   */
  private async executeCommandWithMonitoring(
    interaction: ChatInputCommandInteraction,
    command: BaseCommandHandler,
    span: Sentry.Span,
  ): Promise<void> {
    const commandName = command.metadata.name;

    span.setAttributes({
      'command.name': commandName,
      'command.category': command.metadata.category,
      'user.id': interaction.user.id,
      'guild.id': interaction.guild?.id || 'dm',
      'cluster.id': interaction.client.cluster?.id?.toString() || '0',
      'shard.id': interaction.guild?.shardId?.toString() || '0',
    });

    // Create Context from interaction
    const context = new InteractionContext(interaction);

    // Check cooldown
    if (!(await this.checkCooldown(interaction, command))) {
      span.setAttributes({ 'command.blocked': 'cooldown' });
      await this.handleCooldown(interaction, command);
      return;
    }

    // Check permissions
    if (!(await command.checkPermissions(context))) {
      span.setAttributes({ 'command.blocked': 'permissions' });
      await this.handlePermissionDenied(interaction);
      return;
    }

    // Execute command with performance monitoring
    const result = await this.sentry.startSpan(
      `command-${commandName}-handler`,
      'command.handler',
      async () => await command.execute(context),
    );

    // Set cooldown
    await this.setCooldown(interaction, command);

    // Handle response based on result type
    await this.handleFlexibleCommandResult(interaction, result);

    span.setAttributes({
      'command.success': true,
      'command.result_type': result === null || result === undefined ? 'void' : typeof result,
    });
  }

  /**
   * Handle command execution errors with detailed Sentry context
   */
  private async handleCommandExecutionError(
    interaction: ChatInputCommandInteraction,
    command: BaseCommandHandler,
    error: unknown,
    span: Sentry.Span,
  ): Promise<void> {
    const commandName = command.metadata.name;

    span.setAttributes({
      'command.success': false,
      'command.error': error instanceof Error ? error.message : 'Unknown error',
    });

    // Add detailed error context
    this.sentry.setContext('command_error', {
      commandName,
      category: command.metadata.category,
      errorType: error instanceof Error ? error.constructor.name : 'Unknown',
      errorMessage: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      interactionId: interaction.id,
      createdTimestamp: interaction.createdTimestamp,
    });

    // Add breadcrumb for error
    this.sentry.addBreadcrumb(
      `Command ${commandName} failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      'command.error',
      'error',
    );

    // Capture exception with context (replaces Logger.error)
    this.sentry.captureException(error, {
      command: commandName,
      category: command.metadata.category,
      guildId: interaction.guild?.id,
      userId: interaction.user.id,
      clusterId: interaction.client.cluster?.id,
      shardId: interaction.guild?.shardId,
    });

    await this.handleCommandError(interaction, error);
  }

  /**
   * Execute a prefix command
   */
  async executePrefixCommand(message: Message, commandName: string, args: string[]): Promise<void> {
    const command = this.commands.get(commandName);
    if (!command) return;

    // Execute prefix command with isolated Sentry scope
    await this.sentry.withScopeAsync(async (scope) => {
      // Set scoped context for prefix command
      this.setupPrefixCommandContext(message, command, scope, args);

      await this.sentry.startSpan(`prefix-command-${commandName}`, 'command.prefix', async (span) => {
        try {
          span.setAttributes({
            'command.name': commandName,
            'command.type': 'prefix',
            'command.category': command.metadata.category,
            'user.id': message.author.id,
            'guild.id': message.guild?.id || 'dm',
            'args.count': args.length,
          });

          // Create Context from message
          const context = new PrefixContext(message, args);

          // Check cooldown
          if (!(await this.checkPrefixCooldown(message, command))) {
            span.setAttributes({ 'command.blocked': 'cooldown' });
            await this.handlePrefixCooldown(message, command);
            return;
          }

          // Check permissions
          if (!(await command.checkPermissions(context))) {
            span.setAttributes({ 'command.blocked': 'permissions' });
            await this.handlePrefixPermissionDenied(message);
            return;
          }

          // Execute command
          const result = await command.execute(context);

          // Set cooldown
          await this.setPrefixCooldown(message, command);

          // Handle response based on result type
          await this.handleFlexiblePrefixCommandResult(message, result);

          span.setAttributes({
            'command.success': true,
            'command.result_type': result === null || result === undefined ? 'void' : typeof result,
          });
        }
        catch (error) {
          span.setAttributes({
            'command.success': false,
            'command.error': error instanceof Error ? error.message : 'Unknown error',
          });

          // Capture exception with context for prefix commands
          this.sentry.captureException(error, {
            command: commandName,
            type: 'prefix',
            category: command.metadata.category,
            guildId: message.guild?.id,
            userId: message.author.id,
            args: args.slice(0, 3), // Only first 3 args for privacy
          });

          await this.handlePrefixCommandError(message, error);
        }
      });
    });
  }

  /**
   * Check if user is on cooldown - supports both ChatInputCommandInteraction and Message
   * Now uses Redis-based distributed cooldown tracking for cluster compatibility
   */
  private async checkCooldown(
    interactionOrMessage: ChatInputCommandInteraction | Message,
    command: BaseCommandHandler,
  ): Promise<boolean> {
    const { cooldown } = command.metadata;
    if (!cooldown) return true;

    // Get user ID from either interaction or message
    const userId =
      interactionOrMessage instanceof Message
        ? interactionOrMessage.author.id
        : interactionOrMessage.user.id;
    const commandName = command.metadata.name;

    // Use Redis-based cooldown service for distributed tracking
    return await this.cooldownService.checkCooldown(commandName, userId);
  }

  /**
   * Set cooldown for user - supports both ChatInputCommandInteraction and Message
   * Now uses Redis-based distributed cooldown tracking for cluster compatibility
   */
  private async setCooldown(
    interactionOrMessage: ChatInputCommandInteraction | Message,
    command: BaseCommandHandler,
  ): Promise<void> {
    const { cooldown } = command.metadata;
    if (!cooldown) return;

    // Get user ID from either interaction or message
    const userId =
      interactionOrMessage instanceof Message
        ? interactionOrMessage.author.id
        : interactionOrMessage.user.id;
    const commandName = command.metadata.name;

    // Use Redis-based cooldown service for distributed tracking
    await this.cooldownService.setCooldown(commandName, userId, cooldown);
  }

  /**
   * Handle flexible command result - supports void, CommandResult, or EnhancedCommandResult
   */
  private async handleFlexibleCommandResult(
    interaction: ChatInputCommandInteraction,
    result: FlexibleCommandResponse,
  ): Promise<void> {
    // If result is void, command handled response directly - do nothing
    if (result === undefined || result === null) {
      return;
    }

    // Handle structured responses
    await this.handleStructuredCommandResult(interaction, result);
  }

  /**
   * Handle structured command result (CommandResult or EnhancedCommandResult)
   */
  private async handleStructuredCommandResult(
    interaction: ChatInputCommandInteraction,
    result: CommandResult | EnhancedCommandResult,
  ): Promise<void> {
    // Check if interaction was already handled
    if (interaction.replied || interaction.deferred) {
      // If already replied/deferred, we can only edit or follow up
      await this.handleAlreadyRepliedInteraction(interaction, result);
      return;
    }

    // Send initial response
    await interaction.reply({
      content: result.message,
      embeds: result.embed ? [result.embed] : [],
      flags: result.ephemeral ? [MessageFlags.Ephemeral] : [],
      components: result.components,
      files: 'files' in result && result.files ? result.files : undefined,
      allowedMentions:
        'allowedMentions' in result && result.allowedMentions ? result.allowedMentions : undefined,
    });

    // Send follow-up if specified
    if (result.followUp) {
      await interaction.followUp({ content: result.followUp, flags: [MessageFlags.Ephemeral] });
    }
  }

  /**
   * Handle response when interaction was already replied to or deferred
   */
  private async handleAlreadyRepliedInteraction(
    interaction: ChatInputCommandInteraction,
    result: CommandResult | EnhancedCommandResult,
  ): Promise<void> {
    const response: InteractionEditReplyOptions = {
      content: result.message,
      embeds: result.embed ? [result.embed] : [],
    };

    // Add enhanced features if available
    if ('components' in result && result.components) {
      response.components = result.components;
    }
    if ('files' in result && result.files) {
      response.files = result.files;
    }

    try {
      await interaction.editReply(response);
    }
    catch (error) {
      // If edit fails, try follow-up
      Logger.warn('Failed to edit reply, attempting follow-up:', error);
      await interaction.followUp({
        ...response,
        content: response.content || undefined,
        flags: result.ephemeral ? ['Ephemeral'] : [],
      });
    }

    // Send additional follow-up if specified
    if (result.followUp) {
      await interaction.followUp({
        content: result.followUp,
        flags: ['Ephemeral'],
      });
    }
  }

  /**
   * Handle unknown command
   */
  private async handleUnknownCommand(interaction: ChatInputCommandInteraction): Promise<void> {
    await interaction.reply({
      content: '❌ Unknown command.',
      flags: ['Ephemeral'],
    });
  }

  /**
   * Handle cooldown - now uses Redis-based cooldown service
   */
  private async handleCooldown(
    interaction: ChatInputCommandInteraction,
    command: BaseCommandHandler,
  ): Promise<void> {
    const userId = interaction.user.id;
    const commandName = command.metadata.name;

    // Get remaining cooldown time from Redis
    const timeLeft = await this.cooldownService.getRemainingCooldown(commandName, userId);

    await interaction.reply({
      content: `⏰ Please wait ${timeLeft.toFixed(1)} more seconds before using \`${commandName}\` again.`,
      flags: ['Ephemeral'],
    });
  }

  /**
   * Handle permission denied
   */
  private async handlePermissionDenied(interaction: ChatInputCommandInteraction): Promise<void> {
    await interaction.reply({
      content: '🔒 You do not have permission to use this command.',
      flags: ['Ephemeral'],
    });
  }

  /**
   * Handle command error
   */
  private async handleCommandError(
    interaction: ChatInputCommandInteraction,
    error: unknown,
  ): Promise<void> {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';

    const response = {
      content: `❌ An error occurred while executing this command: ${errorMessage}`,
    };

    try {
      if (interaction.replied || interaction.deferred) {
        await interaction.editReply(response);
      }
      else {
        await interaction.reply({ ...response, flags: ['Ephemeral'] });
      }
    }
    catch (replyError) {
      Logger.error('Failed to send error message:', replyError);
    }
  }

  /**
   * Get command by name
   */
  getCommand(name: string): BaseCommandHandler | undefined {
    return this.commands.get(name);
  }

  /**
   * Get all commands
   */
  getAllCommands(): BaseCommandHandler[] {
    return Array.from(this.commands.values());
  }

  /**
   * Get commands by category
   */
  getCommandsByCategory(category: string): BaseCommandHandler[] {
    return this.getAllCommands().filter((command) => command.metadata.category === category);
  }

  /**
   * Handle flexible command result for prefix commands
   */
  private async handleFlexiblePrefixCommandResult(
    message: Message,
    result: FlexibleCommandResponse,
  ): Promise<void> {
    // If result is void, command handled response directly - do nothing
    if (result === undefined || result === null) {
      return;
    }

    // Handle structured responses for prefix commands
    await this.handleStructuredPrefixCommandResult(message, result);
  }

  /**
   * Handle structured command result for prefix commands
   */
  private async handleStructuredPrefixCommandResult(
    message: Message,
    result: CommandResult | EnhancedCommandResult,
  ): Promise<void> {
    // Build response object
    const response: MessageReplyOptions = {};

    if (result.message) {
      response.content = result.message;
    }

    if (result.embed) {
      response.embeds = [result.embed];
    }

    // Add enhanced features if available
    if ('components' in result && result.components) {
      response.components = result.components;
    }
    if ('files' in result && result.files) {
      response.files = result.files;
    }
    if ('allowedMentions' in result && result.allowedMentions) {
      response.allowedMentions = result.allowedMentions;
    }

    // Send response
    await message.reply(response);

    // Send follow-up if specified
    if (result.followUp) {
      const channel = message.channel;
      if (channel && 'send' in channel) {
        await channel.send(result.followUp);
      }
    }
  }

  /**
   * Check cooldown for prefix commands
   */
  private async checkPrefixCooldown(
    message: Message,
    command: BaseCommandHandler,
  ): Promise<boolean> {
    return await this.checkCooldown(message, command);
  }

  /**
   * Set cooldown for prefix commands
   */
  private async setPrefixCooldown(message: Message, command: BaseCommandHandler): Promise<void> {
    await this.setCooldown(message, command);
  }

  /**
   * Handle prefix command cooldown
   */
  private async handlePrefixCooldown(message: Message, command: BaseCommandHandler): Promise<void> {
    const cooldownTime = command.metadata.cooldown || 3;
    await message.reply(`⏰ Please wait ${cooldownTime} seconds before using this command again.`);
  }

  /**
   * Handle prefix permission denied
   */
  private async handlePrefixPermissionDenied(message: Message): Promise<void> {
    await message.reply('❌ You do not have permission to use this command.');
  }

  /**
   * Handle prefix command error
   */
  private async handlePrefixCommandError(message: Message, error: unknown): Promise<void> {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    await message.reply(
      `❌ An error occurred while executing this command:\n\`\`\`js\n${errorMessage}\`\`\``,
    );
  }
}
