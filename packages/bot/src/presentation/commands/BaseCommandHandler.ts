/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import {
  ActionRowBuilder,
  AttachmentBuilder,
  ContainerBuilder,
  EmbedBuilder,
  MessageActionRowComponentBuilder,
  MessageFlags,
  MessageMentionOptions,
  SlashCommandBuilder,
  SlashCommandOptionsOnlyBuilder,
  SlashCommandSubcommandsOnlyBuilder,
} from 'discord.js';
import { injectable } from 'inversify';
import type { CommandContext } from '../../shared/context/CommandContext.js';
import { Context } from '../../shared/context/Context.js';
import { Logger } from '../../shared/utils/Logger.js';

/**
 * Command metadata interface
 */
export interface CommandMetadata {
  readonly name: string;
  readonly description: string;
  readonly category: CommandCategory;
  readonly cooldown?: number;
  readonly permissions?: ('STAFF' | 'DEVELOPER')[];
  readonly guildOnly?: boolean;
  readonly ownerOnly?: boolean;
  readonly premiumOnly?: boolean;
}

/**
 * Command categories
 */
export enum CommandCategory {
  DONATION = 'donation',
  HUB = 'hub',
  INFORMATION = 'information',
  MODERATION = 'moderation',
  STAFF = 'staff',
  CONFIG = 'config',
  USERPHONE = 'userphone',
  UTILITIES = 'utilities',
}

/**
 * Command execution result for structured responses
 */
export interface CommandResult {
  readonly success: boolean;
  readonly message?: string;
  readonly embed?: EmbedBuilder;
  readonly ephemeral?: boolean;
  readonly followUp?: string;
  readonly components?: ActionRowBuilder<MessageActionRowComponentBuilder>[] | ContainerBuilder[]; // Discord components (buttons, select menus, etc.)
}

/**
 * Enhanced command execution result with Discord components support
 */
export interface EnhancedCommandResult extends CommandResult {
  readonly components?: ActionRowBuilder<MessageActionRowComponentBuilder>[] | ContainerBuilder[];
  readonly files?: AttachmentBuilder[];
  readonly allowedMentions?: MessageMentionOptions;
  readonly flags?: MessageFlags[];
}

/**
 * Response mode for command execution
 */
export enum CommandResponseMode {
  /** Command handles response directly via interaction.reply() - returns void */
  DIRECT = 'direct',
  /** Command returns CommandResult object for registry to process */
  STRUCTURED = 'structured',
  /** Command returns enhanced result with full Discord features */
  ENHANCED = 'enhanced',
}

/**
 * Flexible command response - can be void, CommandResult, or EnhancedCommandResult
 */
export type FlexibleCommandResponse = void | CommandResult | EnhancedCommandResult;

/**
 * Interface for subcommand handlers
 */
export interface ISubcommandHandler {
  readonly name: string;
  readonly description: string;
  execute(ctx: Context): Promise<FlexibleCommandResponse>;
}

/**
 * Base class for subcommand handlers
 */
@injectable()
export abstract class BaseSubcommandHandler implements ISubcommandHandler {
  abstract readonly name: string;
  abstract readonly description: string;

  abstract execute(ctx: Context): Promise<FlexibleCommandResponse>;

  /**
   * Create a success embed
   */
  protected createSuccessEmbed(title: string, description: string): EmbedBuilder {
    return new EmbedBuilder()
      .setColor('#00FF00')
      .setTitle(title)
      .setDescription(description)
      .setTimestamp();
  }

  /**
   * Create an error embed
   */
  protected createErrorEmbed(title: string, description: string): EmbedBuilder {
    return new EmbedBuilder()
      .setColor('#FF0000')
      .setTitle(title)
      .setDescription(description)
      .setTimestamp();
  }

  /**
   * Create an info embed
   */
  protected createInfoEmbed(title: string, description: string): EmbedBuilder {
    return new EmbedBuilder()
      .setColor('#0099FF')
      .setTitle(title)
      .setDescription(description)
      .setTimestamp();
  }

  /**
   * Format user for display
   */
  protected formatUser(userId: string): string {
    return `<@${userId}>`;
  }

  /**
   * Format timestamp for display
   */
  protected formatTimestamp(date: Date): string {
    return `<t:${Math.floor(date.getTime() / 1000)}:F>`;
  }

  /**
   * Format relative timestamp
   */
  protected formatRelativeTimestamp(date: Date): string {
    return `<t:${Math.floor(date.getTime() / 1000)}:R>`;
  }
}

/**
 * Base Command Handler
 *
 * Provides common functionality for all Discord slash commands.
 * Implements the Command pattern with dependency injection support.
 */
@injectable()
export abstract class BaseCommandHandler {
  abstract readonly metadata: CommandMetadata;

  /**
   * Optional subcommand handlers for organized command structure
   */
  protected subcommands?: Map<string, ISubcommandHandler>;

  /**
   * Build the slash command data
   */
  abstract buildCommand():
    | SlashCommandBuilder
    | SlashCommandOptionsOnlyBuilder
    | SlashCommandSubcommandsOnlyBuilder;

  /**
   * Execute the command with flexible response support
   *
   * Commands can choose their response pattern:
   * - Return void: Command handles response directly via ctx.reply()
   * - Return CommandResult: Registry processes structured response
   * - Return EnhancedCommandResult: Registry processes with full Discord features
   */
  abstract execute(ctx: CommandContext): Promise<FlexibleCommandResponse>;

  /**
   * Register a subcommand handler
   */
  protected registerSubcommand(handler: ISubcommandHandler): void {
    if (!this.subcommands) {
      this.subcommands = new Map();
    }
    this.subcommands.set(handler.name, handler);
  }

  /**
   * Execute a subcommand if it exists
   */
  protected async executeSubcommand(
    subcommandName: string,
    ctx: Context,
  ): Promise<FlexibleCommandResponse> {
    if (!this.subcommands) {
      return {
        success: false,
        embed: this.createErrorEmbed(
          'No Subcommands',
          'This command has no subcommands available.',
        ),
        flags: [MessageFlags.Ephemeral],
      };
    }

    const handler = this.subcommands.get(subcommandName);
    if (!handler) {
      return {
        success: false,
        embed: this.createErrorEmbed(
          'Invalid Subcommand',
          `Subcommand '${subcommandName}' not found.`,
        ),
        flags: [MessageFlags.Ephemeral],
      };
    }

    return await handler.execute(ctx);
  }

  /**
   * Check if the user has permission to use this command
   */
  async checkPermissions(ctx: Context): Promise<boolean> {
    const { metadata } = this;

    // Check if command is owner only
    if (metadata.ownerOnly) {
      const ownerId = process.env.OWNER_ID;
      if (ctx.user.id !== ownerId) {
        return false;
      }
    }

    // Check if command is guild only
    if (metadata.guildOnly && !ctx.guild) {
      return false;
    }

    // Check specific permissions
    if (metadata.permissions && metadata.permissions.length > 0) {
      if (!ctx.guild || !ctx.member) {
        return false;
      }

      // This would need proper Discord.js permission checking
      // For now, return true (implement permission checking later)
    }

    return true;
  }

  /**
   * Create a standard error embed
   */
  protected createErrorEmbed(title: string, description: string): EmbedBuilder {
    return new EmbedBuilder()
      .setColor(0xff0000)
      .setTitle(`❌ ${title}`)
      .setDescription(description)
      .setTimestamp();
  }

  /**
   * Create a standard success embed
   */
  protected createSuccessEmbed(title: string, description: string): EmbedBuilder {
    return new EmbedBuilder()
      .setColor(0x00ff00)
      .setTitle(`✅ ${title}`)
      .setDescription(description)
      .setTimestamp();
  }

  /**
   * Create a standard info embed
   */
  protected createInfoEmbed(title: string, description: string): EmbedBuilder {
    return new EmbedBuilder()
      .setColor(0x0099ff)
      .setTitle(`ℹ️ ${title}`)
      .setDescription(description)
      .setTimestamp();
  }

  /**
   * Handle command errors gracefully
   */
  protected handleError(error: unknown, _ctx: Context): FlexibleCommandResponse {
    Logger.error(`Error in command ${this.metadata.name}:`, error);

    return {
      success: false,
      embed: this.createErrorEmbed(
        'Command Error',
        'An unexpected error occurred while executing this command. Please try again later.',
      ),
      flags: [MessageFlags.Ephemeral],
    };
  }

  /**
   * Format user for display
   */
  protected formatUser(userId: string): string {
    return `<@${userId}>`;
  }

  /**
   * Format timestamp for display
   */
  protected formatTimestamp(date: Date): string {
    return `<t:${Math.floor(date.getTime() / 1000)}:F>`;
  }

  /**
   * Format relative timestamp
   */
  protected formatRelativeTimestamp(date: Date): string {
    return `<t:${Math.floor(date.getTime() / 1000)}:R>`;
  }
}
