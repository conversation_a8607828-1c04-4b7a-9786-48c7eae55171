/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { SlashCommandBuilder, EmbedBuilder, PermissionFlagsBits, ChannelType, SlashCommandSubcommandsOnlyBuilder, MessageFlags, TextChannel } from 'discord.js';
import { injectable, inject } from 'inversify';
import { BaseCommandHandler, CommandCategory, type FlexibleCommandResponse } from '../BaseCommandHandler.js';
import { Context } from '../../../shared/context/Context.js';
import { TYPES } from '../../../shared/types/TYPES.js';
import type { HubService } from '../../../domain/services/HubService.js';
import type { ServerRepository } from '../../../domain/repositories/ServerRepository.js';
import type { WebhookService } from '../../../application/services/WebhookService.js';

@injectable()
export class SetupCommandHandler extends BaseCommandHandler {
  readonly metadata = {
    name: 'setup',
    description: 'Set up InterChat for your server',
    category: CommandCategory.CONFIG,
    cooldown: 30, // 30 seconds
    guildOnly: true,
    permissions: [],
  };

  constructor(
    @inject(TYPES.HubService) private readonly hubService: HubService,
    @inject(TYPES.ServerRepository) private readonly serverRepository: ServerRepository,
    @inject(TYPES.WebhookService) private readonly webhookService: WebhookService,
  ) {
    super();
  }

  buildCommand(): SlashCommandSubcommandsOnlyBuilder {
    return new SlashCommandBuilder()
      .setName(this.metadata.name)
      .setDescription(this.metadata.description)
      .addSubcommand((subcommand) =>
        subcommand
          .setName('channel')
          .setDescription('Set up a channel for InterChat')
          .addChannelOption((option) =>
            option
              .setName('channel')
              .setDescription('The channel to set up for InterChat')
              .setRequired(true)
              .addChannelTypes(ChannelType.GuildText),
          )
          .addStringOption((option) =>
            option
              .setName('hub')
              .setDescription('The hub to connect to')
              .setRequired(true)
              .setAutocomplete(true),
          ),
      )
      .addSubcommand((subcommand) =>
        subcommand
          .setName('create-hub')
          .setDescription('Create a new hub')
          .addStringOption((option) =>
            option
              .setName('name')
              .setDescription('The name of the hub')
              .setRequired(true)
              .setMinLength(3)
              .setMaxLength(50),
          )
          .addStringOption((option) =>
            option
              .setName('description')
              .setDescription('The description of the hub')
              .setRequired(false)
              .setMaxLength(200),
          ),
      )
      .addSubcommand((subcommand) =>
        subcommand
          .setName('info')
          .setDescription('Show setup information and status for your server'),
      )
      .setDefaultMemberPermissions(PermissionFlagsBits.ManageGuild)
      .setContexts([0]); // Guild only
  }

  async execute(ctx: Context): Promise<FlexibleCommandResponse> {
    // Ensure we're in a guild
    if (!ctx.inGuild()) {
      return {
        success: false,
        message: '❌ This command can only be used in servers.',
        flags: [MessageFlags.Ephemeral],
      };
    }

    // Check permissions
    if (!ctx.member.permissions.has(PermissionFlagsBits.ManageGuild)) {
      return {
        success: false,
        message: '❌ You need the "Manage Server" permission to use this command.',
        flags: [MessageFlags.Ephemeral],
      };
    }

    const subcommand = ctx.options.getSubcommand(true);

    switch (subcommand) {
      case 'channel':
        return await this.handleChannelSetup(ctx);
      case 'create-hub':
        return await this.handleHubCreation(ctx);
      case 'info':
        return await this.handleSetupInfo(ctx);
      default:
        return {
          success: false,
          message: '❌ Invalid subcommand.',
          flags: [MessageFlags.Ephemeral],
        };
    }
  }

  private async handleChannelSetup(ctx: Context): Promise<FlexibleCommandResponse> {
    if (!ctx.guild) {
      return {
        success: false,
        message: '❌ This command can only be used in servers.',
        flags: [MessageFlags.Ephemeral],
      };
    }

    const channel = ctx.options.getChannel('channel');
    const hubName = ctx.options.getString('hub');

    if (!channel || !hubName) {
      return {
        success: false,
        message: '❌ Missing required parameters.',
        flags: [MessageFlags.Ephemeral],
      };
    }

    // Find the hub
    const hub = await this.hubService.findHubByName(hubName);
    if (!hub) {
      return {
        success: false,
        message: '❌ Hub not found. Use autocomplete to find available hubs.',
        flags: [MessageFlags.Ephemeral],
      };
    }

    // Check if channel is already connected
    const existingConnection = await this.serverRepository.getServerConnection(
      ctx.guild.id,
      channel.id,
    );

    if (existingConnection) {
      return {
        success: false,
        message: '❌ This channel is already connected to a hub.',
        flags: [MessageFlags.Ephemeral],
      };
    }

    // Verify channel is a text channel
    if (!(channel instanceof TextChannel)) {
      return {
        success: false,
        message: '❌ Selected channel must be a text channel.',
        flags: [MessageFlags.Ephemeral],
      };
    }

    // Create or reuse webhook for the channel
    const webhookResult = await this.webhookService.getOrCreateWebhook(channel);
    if (!webhookResult.success) {
      return {
        success: false,
        message: `❌ Failed to setup webhook: ${webhookResult.error}`,
        flags: [MessageFlags.Ephemeral],
      };
    }

    // Create the connection with webhook URL
    await this.serverRepository.createServerConnection({
      guildId: ctx.guild.id,
      channelId: channel.id,
      hubId: hub.id,
      connectedBy: ctx.user.id,
      webhookURL: webhookResult.webhookURL,
    });

    return {
      success: true,
      message: `✅ Successfully connected ${channel} to hub **${hub.name}**!\n🔗 Webhook created and ready for messaging.`,
    };
  }

  private async handleHubCreation(ctx: Context): Promise<FlexibleCommandResponse> {
    const name = ctx.options.getString('name');
    const description = ctx.options.getString('description');

    if (!name) {
      return {
        success: false,
        message: '❌ Hub name is required.',
        flags: [MessageFlags.Ephemeral],
      };
    }

    // Check if hub name is available
    const existingHub = await this.hubService.findHubByName(name);
    if (existingHub) {
      return {
        success: false,
        message: '❌ A hub with this name already exists.',
        flags: [MessageFlags.Ephemeral],
      };
    }

    // Create the hub
    const hub = await this.hubService.createHub({
      name,
      description: description || undefined,
      ownerId: ctx.user.id,
      private: false,
    });

    return {
      success: true,
      message: `✅ Successfully created hub **${hub.name}**! You can now connect channels to it.`,
    };
  }

  private async handleSetupInfo(ctx: Context): Promise<FlexibleCommandResponse> {
    if (!ctx.inGuild()) return;

    // Get server connections
    const connections = await this.serverRepository.getServerConnections(ctx.guild.id);

    const embed = new EmbedBuilder()
      .setTitle('🛠️ InterChat Setup Information')
      .setColor(0x3498db)
      .setDescription(`Server: **${ctx.guild.name}**`)
      .addFields([
        {
          name: '📊 Status',
          value: connections.length > 0 ? '✅ Connected' : '❌ Not connected',
          inline: true,
        },
        {
          name: '🔗 Active Connections',
          value: connections.length.toString(),
          inline: true,
        },
        {
          name: '⚙️ Setup',
          value: 'Use `/setup channel` to connect a channel to a hub',
          inline: false,
        },
      ])
      .setTimestamp();

    // Add connection details if any exist
    if (connections.length > 0) {
      const connectionList = connections
        .map((conn) => `<#${conn.channelId}> → **${conn.hubName}**`)
        .join('\n');

      embed.addFields({
        name: '📋 Connected Channels',
        value: connectionList,
      });
    }

    return {
      success: true,
      embed,
      flags: [MessageFlags.Ephemeral],
    };
  }
}
