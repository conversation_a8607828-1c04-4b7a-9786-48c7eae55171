/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import {
  ButtonBuilder,
  ButtonStyle,
  ContainerBuilder,
  MessageFlags,
  SectionBuilder,
  SeparatorSpacingSize,
  SlashCommandBuilder,
  TextDisplayBuilder,
} from 'discord.js';
import { injectable } from 'inversify';
import { Context } from '../../../shared/context/Context.js';
import { CustomID } from '../../../shared/utils/CustomID.js';
import {
  BaseCommandHandler,
  CommandCategory,
  type FlexibleCommandResponse,
} from '../BaseCommandHandler.js';

function createCompressedCustomId(prefix: string, suffix: string, params: string[] = []): string {
  const handlerId = `${prefix}:${suffix}`;
  return CustomID.withOptions(handlerId, params, { forceCompression: true }).toString();
}

@injectable()
export default class PreferencesCommandHandler extends BaseCommandHandler {
  readonly metadata = {
    name: 'preferences',
    description: 'Configure your personal InterChat settings',
    category: CommandCategory.CONFIG,
    cooldown: 5,
    guildOnly: false,
    permissions: [],
  };

  buildCommand(): SlashCommandBuilder {
    return new SlashCommandBuilder()
      .setName(this.metadata.name)
      .setDescription(this.metadata.description);
  }

  async execute(ctx: Context): Promise<FlexibleCommandResponse> {
    try {
      const container = this.buildPreferencesContainer(ctx.user.id);

      await ctx.reply({
        components: [container],
        flags: ['IsComponentsV2'],
      });

      return { success: true };
    }
    catch (_error) {
      return {
        success: false,
        message: '❌ An error occurred while loading your preferences.',
        flags: [MessageFlags.Ephemeral],
      };
    }
  }

  private buildPreferencesContainer(userId: string): ContainerBuilder {
    const container = new ContainerBuilder();

    container.addTextDisplayComponents(
      new TextDisplayBuilder().setContent(
        '## ⚙️ Personal Preferences\nConfigure your InterChat experience',
      ),
    );

    container.addSeparatorComponents((separator) =>
      separator.setSpacing(SeparatorSpacingSize.Large),
    );

    // Notification settings
    const notificationSection = new SectionBuilder()
      .addTextDisplayComponents(
        new TextDisplayBuilder().setContent(
          '### 🔔 Notifications\nControl when you receive DM notifications',
        ),
      )
      .setButtonAccessory(
        new ButtonBuilder()
          .setCustomId(createCompressedCustomId('preferences', 'notifications', [userId]))
          .setLabel('Configure')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('🔔'),
      );

    container.addSectionComponents(notificationSection);

    // Privacy settings
    const privacySection = new SectionBuilder()
      .addTextDisplayComponents(
        new TextDisplayBuilder().setContent(
          '### 🔒 Privacy\nManage your data and visibility settings',
        ),
      )
      .setButtonAccessory(
        new ButtonBuilder()
          .setCustomId(createCompressedCustomId('preferences', 'privacy', [userId]))
          .setLabel('Configure')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('🔒'),
      );

    container.addSectionComponents(privacySection);

    // Display settings
    const displaySection = new SectionBuilder()
      .addTextDisplayComponents(
        new TextDisplayBuilder().setContent(
          '### 🎨 Display\nCustomize how messages appear to you',
        ),
      )
      .setButtonAccessory(
        new ButtonBuilder()
          .setCustomId(createCompressedCustomId('preferences', 'display', [userId]))
          .setLabel('Configure')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('🎨'),
      );

    container.addSectionComponents(displaySection);

    // Language settings
    const languageSection = new SectionBuilder()
      .addTextDisplayComponents(
        new TextDisplayBuilder().setContent(
          '### 🌐 Language\nChange your preferred language for InterChat',
        ),
      )
      .setButtonAccessory(
        new ButtonBuilder()
          .setCustomId(createCompressedCustomId('preferences', 'language', [userId]))
          .setLabel('Configure')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('🌐'),
      );

    container.addSectionComponents(languageSection);

    return container;
  }
}
