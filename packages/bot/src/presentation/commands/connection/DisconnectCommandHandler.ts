/*
 * Copyright (C) 2025 InterChat
 *
 *   buildCommand(): SlashCommandBuilder {
    return new SlashCommandBuilder()
      .setName(this.metadata.name)
      .setDescription(this.metadata.description)
      .addChannelOption((option) =>
        option
          .setName('channel')
          .setDescription('The channel to disconnect (defaults to current channel)')
          .setRequired(false)
          .addChannelTypes(ChannelType.GuildText),
      )
      .setDefaultMemberPermissions(PermissionFlagsBits.ManageChannels)
      .setContexts([0]) as SlashCommandBuilder; // Guild only
  }ree software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import {
  SlashCommandBuilder,
  EmbedBuilder,
  PermissionFlagsBits,
  ChannelType,
  ButtonBuilder,
  ButtonStyle,
  ActionRowBuilder,
  MessageFlags,
} from 'discord.js';
import { injectable, inject } from 'inversify';
import {
  BaseCommandHandler,
  CommandCategory,
  type FlexibleCommandResponse,
} from '../BaseCommandHandler.js';
import { Context } from '../../../shared/context/Context.js';
import { TYPES } from '../../../shared/types/TYPES.js';
import type { ServerRepository } from '../../../domain/repositories/ServerRepository.js';
import { CustomID } from '../../../shared/utils/CustomID.js';

@injectable()
export class DisconnectCommandHandler extends BaseCommandHandler {
  readonly metadata = {
    name: 'disconnect',
    description: 'Disconnect a channel from its hub',
    category: CommandCategory.UTILITIES,
    cooldown: 10, // 10 seconds
    guildOnly: true,
    permissions: [],
  };

  constructor(@inject(TYPES.ServerRepository) private readonly serverRepository: ServerRepository) {
    super();
  }

  buildCommand() {
    return new SlashCommandBuilder()
      .setName(this.metadata.name)
      .setDescription(this.metadata.description)
      .addChannelOption((option) =>
        option
          .setName('channel')
          .setDescription('The channel to disconnect (defaults to current channel)')
          .setRequired(false)
          .addChannelTypes(ChannelType.GuildText),
      )
      .setDefaultMemberPermissions(PermissionFlagsBits.ManageChannels)
      .setContexts([0]); // Guild only
  }

  async execute(ctx: Context): Promise<FlexibleCommandResponse> {
    // Ensure we're in a guild
    if (!ctx.inGuild()) {
      return {
        success: false,
        message: '❌ This command can only be used in servers.',
        flags: [MessageFlags.Ephemeral],
      };
    }

    // Check permissions
    if (!ctx.member.permissions.has(PermissionFlagsBits.ManageChannels)) {
      return {
        success: false,
        message: '❌ You need the "Manage Channels" permission to use this command.',
        flags: [MessageFlags.Ephemeral],
      };
    }

    const targetChannel = ctx.options.getChannel('channel') || ctx.channel;

    if (!targetChannel) {
      return {
        success: false,
        message: '❌ Could not determine the target channel.',
        flags: [MessageFlags.Ephemeral],
      };
    }

    // Check if channel is connected
    const connection = await this.serverRepository.getServerConnection(
      ctx.guild.id,
      targetChannel.id,
    );

    if (!connection) {
      return {
        success: false,
        message: `❌ ${targetChannel} is not connected to any hub.`,
        flags: [MessageFlags.Ephemeral],
      };
    }

    // Create confirmation embed
    const confirmEmbed = new EmbedBuilder()
      .setTitle('🔌 Confirm Disconnection')
      .setDescription(
        `Are you sure you want to disconnect ${targetChannel} from **${connection.hubName}**?\n\n` +
          "⚠️ **This action is permanent.** You'll need to reconnect the channel if you want to rejoin the hub.",
      )
      .setColor(0xe74c3c)
      .addFields([
        {
          name: '📍 Channel',
          value: `${targetChannel}`,
          inline: true,
        },
        {
          name: '🏠 Hub',
          value: connection.hubName,
          inline: true,
        },
        {
          name: '⏰ Connected',
          value: `<t:${Math.floor(connection.connectedAt.getTime() / 1000)}:R>`,
          inline: true,
        },
      ])
      .setFooter({ text: 'This action cannot be undone' });

    // Create confirmation buttons
    const confirmButton = new ButtonBuilder()
      .setCustomId(new CustomID('disconnect:confirm', [targetChannel.id]).toString())
      .setLabel('Disconnect')
      .setStyle(ButtonStyle.Danger)
      .setEmoji('🔌');

    const cancelButton = new ButtonBuilder()
      .setCustomId(`disconnect_cancel_${targetChannel.id}`)
      .setLabel('Cancel')
      .setStyle(ButtonStyle.Secondary)
      .setEmoji('❌');

    const actionRow = new ActionRowBuilder<ButtonBuilder>().addComponents(
      confirmButton,
      cancelButton,
    );

    return {
      success: true,
      embed: confirmEmbed,
      components: [actionRow],
      flags: [MessageFlags.Ephemeral],
    };
  }

  // Handle the confirmation button interaction
  async handleConfirmDisconnect(ctx: Context, channelId: string): Promise<FlexibleCommandResponse> {
    if (!ctx.inGuild()) {
      return {
        success: false,
        message: '❌ This command can only be used in servers.',
        flags: [MessageFlags.Ephemeral],
      };
    }

    // Verify the connection still exists
    const connection = await this.serverRepository.getServerConnection(ctx.guild.id, channelId);

    if (!connection) {
      return {
        success: false,
        message: '❌ This channel is no longer connected to any hub.',
        flags: [MessageFlags.Ephemeral],
      };
    }

    // Perform the disconnection
    await this.serverRepository.removeServerConnection(ctx.guild.id, channelId);

    // Create success embed
    const successEmbed = new EmbedBuilder()
      .setTitle('✅ Channel Disconnected')
      .setDescription(
        `Successfully disconnected <#${channelId}> from **${connection.hubName}**.\n\n` +
          '💡 Use `/setup channel` to connect to a hub again.',
      )
      .setColor(0x27ae60)
      .setTimestamp();

    return {
      success: true,
      embed: successEmbed,
      components: [], // Remove buttons
    };
  }

  async handleCancelDisconnect(): Promise<FlexibleCommandResponse> {
    const cancelEmbed = new EmbedBuilder()
      .setTitle('❌ Disconnection Cancelled')
      .setDescription('The channel remains connected to the hub.')
      .setColor(0x95a5a6);

    return {
      success: true,
      embed: cancelEmbed,
      components: [], // Remove buttons
      flags: [MessageFlags.Ephemeral],
    };
  }
}
