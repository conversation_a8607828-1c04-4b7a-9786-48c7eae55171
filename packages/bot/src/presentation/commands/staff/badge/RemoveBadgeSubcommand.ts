/**
 * Copyright (c) 2024 InterChat
 *
 * This file is part of InterChat, licensed under the AGPL-3.0 license.
 * See the LICENSE file in the root directory for license information.
 */

/**
 * Remove Badge Subcommand Handler
 *
 * <PERSON>les removing badges from users - demonstrates enhanced response patterns.
 */

import { injectable, inject } from 'inversify';
import { ChatInputCommandInteraction, ButtonBuilder, ButtonStyle, ActionRowBuilder, MessageFlags } from 'discord.js';
import { BaseSubcommandHandler, FlexibleCommandResponse, EnhancedCommandResult } from '../../BaseCommandHandler.js';
import { TYPES } from '../../../../shared/types/TYPES.js';
import type { IUserRepository } from '../../../../domain/repositories/UserRepositories.js';
import type { Context } from '../../../../shared/context/Context.js';

/**
 * Remove Badge Subcommand Handler
 *
 * Demonstrates enhanced response patterns with Discord components.
 */
@injectable()
export class RemoveBadgeSubcommand extends BaseSubcommandHandler {
  readonly name = 'remove';
  readonly description = 'Remove a badge from a user';

  constructor(
    @inject(TYPES.UserRepository)
    private readonly userRepository: IUserRepository,
  ) {
    super();
  }

  async execute(ctx: Context): Promise<FlexibleCommandResponse> {
    const interaction = ctx.interaction as ChatInputCommandInteraction;
    const targetUser = interaction.options.getUser('user', true);
    const badgeType = interaction.options.getString('badge', true);

    // Check if user exists in our system
    const user = await this.userRepository.findById(targetUser.id);

    if (!user) {
      return {
        success: false,
        embed: this.createErrorEmbed(
          'User Not Found',
          'This user is not registered in the InterChat system.',
        ),
        flags: [MessageFlags.Ephemeral],
      };
    }

    // Example of ENHANCED response pattern with Discord components
    const confirmButton = new ButtonBuilder()
      .setCustomId(`badge_remove_confirm_${targetUser.id}_${badgeType}`)
      .setLabel('Confirm Removal')
      .setStyle(ButtonStyle.Danger)
      .setEmoji('🗑️');

    const cancelButton = new ButtonBuilder()
      .setCustomId(`badge_remove_cancel_${targetUser.id}_${badgeType}`)
      .setLabel('Cancel')
      .setStyle(ButtonStyle.Secondary)
      .setEmoji('❌');

    const actionRow = new ActionRowBuilder<ButtonBuilder>()
      .addComponents(confirmButton, cancelButton);

    const embed = this.createInfoEmbed(
      'Confirm Badge Removal',
      `Are you sure you want to remove **${this.getBadgeName(badgeType)}** badge from ${targetUser.username}?\n\n` +
        `**Moderator:** ${interaction.user.username}\n` +
        `**Target:** ${targetUser.username} (${targetUser.id})\n\n` +
        '⚠️ This action cannot be undone.',
    );

    // Return enhanced result with components
    const enhancedResult: EnhancedCommandResult = {
      success: true,
      embed,
      components: [actionRow],
      // Example of additional Discord features
      allowedMentions: { parse: [] }, // Prevent mentions
      flags: [], // Could include MessageFlags if needed
    };

    return enhancedResult;
  }

  /**
   * Get display name for badge type
   */
  private getBadgeName(badgeType: string): string {
    const badgeNames: Record<string, string> = {
      'CHAMPION': '🏆 Champion',
      'DEVELOPER': '🛠️ Developer',
      'SUPPORTER': '🎭 Supporter',
      'VIP': '⭐ VIP',
      'BETA_TESTER': '🔧 Beta Tester',
    };

    return badgeNames[badgeType] || badgeType;
  }
}
