/**
 * Copyright (c) 2024 InterChat
 *
 * This file is part of InterChat, licensed under the AGPL-3.0 license.
 * See the LICENSE file in the root directory for license information.
 */

/**
 * Find Command Handler
 *
 * Handles finding users and servers for staff members.
 */

import { EmbedBuilder, MessageFlags, SlashCommandBuilder } from 'discord.js';
import { inject, injectable } from 'inversify';
import type { IUserRepository } from '../../../domain/repositories/UserRepositories.js';
import { Context } from '../../../shared/context/Context.js';
import { TYPES } from '../../../shared/types/TYPES.js';
import { BaseCommandHandler, CommandCategory, FlexibleCommandResponse } from '../BaseCommandHandler.js';

/**
 * Find Command Handler
 *
 * Provides search functionality for users and servers.
 */
@injectable()
export default class FindCommandHandler extends BaseCommandHandler {
  readonly metadata = {
    name: 'find',
    description: 'Find a user or server (Staff Only)',
    category: CommandCategory.STAFF,
    staffOnly: true,
    ownerOnly: false,
    guildOnly: false,
    cooldown: 3000,
  };

  constructor(
    @inject(TYPES.UserRepository)
    private readonly userRepository: IUserRepository,
  ) {
    super();
  }

  buildCommand() {
    return new SlashCommandBuilder()
      .setName(this.metadata.name)
      .setDescription(this.metadata.description)
      .addSubcommand((subcommand) =>
        subcommand
          .setName('user')
          .setDescription('Find a user by ID or username')
          .addStringOption((option) =>
            option
              .setName('query')
              .setDescription('User ID or username to search for')
              .setRequired(true),
          ),
      )
      .addSubcommand((subcommand) =>
        subcommand
          .setName('server')
          .setDescription('Find a server by ID')
          .addStringOption((option) =>
            option.setName('server_id').setDescription('Server ID to search for').setRequired(true),
          ),
      );
  }

  async execute(ctx: Context): Promise<FlexibleCommandResponse> {
    const subcommand = ctx.options.getSubcommand();

    if (subcommand === 'user') {
      return await this.findUser(ctx);
    }
    else if (subcommand === 'server') {
      return await this.findServer(ctx);
    }

    const embed = this.createErrorEmbed(
      'Invalid Subcommand',
      'Please use either `user` or `server` subcommand.',
    );

    return {
      success: false,
      embed,
      flags: [MessageFlags.Ephemeral],
    };
  }

  private async findUser(ctx: Context): Promise<FlexibleCommandResponse> {
    const query = ctx.options.getString('query', true);
    if (!query) {
      const embed = this.createErrorEmbed(
        'Invalid Input',
        'Please provide a valid user ID or username.',
      );

      return {
        success: false,
        embed,
        flags: [MessageFlags.Ephemeral],
      };
    }

    let user = null;

    // Check if query is a user ID (numeric string)
    if (/^\d{17,19}$/.test(query)) {
      user = await this.userRepository.findById(query);
    }
    else {
      // Search by username
      const users = await this.userRepository.searchByName(query, 1);
      user = users[0] || null;
    }

    if (!user) {
      const embed = this.createErrorEmbed(
        'User Not Found',
        `No user found matching: **${query}**`,
      );

      return {
        success: false,
        embed,
        flags: [MessageFlags.Ephemeral],
      };
    }

    // Create user info embed
    const embed = new EmbedBuilder()
      .setTitle('👤 User Found')
      .setColor(0x00ff00)
      .addFields(
        { name: 'User ID', value: user.id, inline: true },
        { name: 'Username', value: user.name || 'Unknown', inline: true },
        { name: 'Staff', value: user.isStaff ? 'Yes' : 'No', inline: true },
        { name: 'Banned', value: user.isBanned ? 'Yes' : 'No', inline: true },
        { name: 'Messages', value: user.activity.messageCount.toString(), inline: true },
        { name: 'Reputation', value: user.activity.reputation.toString(), inline: true },
        {
          name: 'Created',
          value: `<t:${Math.floor(user.createdAt.getTime() / 1000)}:F>`,
          inline: false,
        },
      )
      .setTimestamp();

    if (user.image) {
      embed.setThumbnail(user.image);
    }

    if (user.isBanned && user.banInfo.reason) {
      embed.addFields({ name: 'Ban Reason', value: user.banInfo.reason, inline: false });
    }

    return {
      success: true,
      embed,
      flags: [MessageFlags.Ephemeral],
    };
  }

  private async findServer(ctx: Context): Promise<FlexibleCommandResponse> {
    {
      const serverId = ctx.options.getString('server_id', true);

      if (!serverId) {
        const embed = this.createErrorEmbed(
          'Invalid Input',
          'Please provide a valid server ID.',
        );

        return {
          success: false,
          embed,
          flags: [MessageFlags.Ephemeral],
        };
      }

      // Validate server ID format
      if (!/^\d{17,19}$/.test(serverId)) {
        const embed = this.createErrorEmbed(
          'Invalid Server ID',
          'Please provide a valid Discord server ID.',
        );

        return {
          success: false,
          embed,
          flags: [MessageFlags.Ephemeral],
        };
      }

      try {
      // Try to find the guild
        let guild = ctx.client.guilds.cache.get(serverId);

        // If not found locally, try to fetch it
        if (!guild) {
          try {
            guild = await ctx.client.guilds.fetch(serverId);
          }
          catch {
          // Guild not found or bot not in guild
          }
        }

        if (!guild) {
          const embed = this.createErrorEmbed(
            'Server Not Found',
            `No server found with ID: **${serverId}**\n\nThe bot may not be in this server.`,
          );

          return {
            success: false,
            embed,
            flags: [MessageFlags.Ephemeral],
          };
        }

        // Create server info embed
        const embed = new EmbedBuilder()
          .setTitle('🌐 Server Found')
          .setColor(0x00ff00)
          .addFields(
            { name: 'Server ID', value: guild.id, inline: true },
            { name: 'Name', value: guild.name, inline: true },
            { name: 'Members', value: guild.memberCount?.toString() || 'Unknown', inline: true },
            { name: 'Owner', value: `<@${guild.ownerId}>`, inline: true },
            {
              name: 'Created',
              value: `<t:${Math.floor(guild.createdTimestamp / 1000)}:F>`,
              inline: false,
            },
            {
              name: 'Joined',
              value: guild.joinedTimestamp
                ? `<t:${Math.floor(guild.joinedTimestamp / 1000)}:F>`
                : 'Unknown',
              inline: false,
            },
          )
          .setTimestamp();

        if (guild.iconURL()) {
          embed.setThumbnail(guild.iconURL()!);
        }

        return {
          success: true,
          embed,
          flags: [MessageFlags.Ephemeral],
        };
      }
      catch {
        const embed = this.createErrorEmbed(
          'Server Search Failed',
          'An error occurred while searching for the server.',
        );

        return {
          success: false,
          embed,
          flags: [MessageFlags.Ephemeral],
        };
      }
    }
  }
}
