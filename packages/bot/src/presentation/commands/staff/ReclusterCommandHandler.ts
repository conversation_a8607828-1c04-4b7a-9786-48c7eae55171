/**
 * Copyright (c) 2024 InterChat
 *
 * This file is part of InterChat, licensed under the AGPL-3.0 license.
 * See the LICENSE file in the root directory for license information.
 */

/**
 * Recluster Command Handler
 *
 * Handles bot reclustering/restart functionality for developers.
 */

import { MessageFlags, SlashCommandBuilder } from 'discord.js';
import { injectable } from 'inversify';
import type { Context } from '../../../shared/context/Context.js';
import { BaseCommandHandler, CommandCategory, FlexibleCommandResponse } from '../BaseCommandHandler.js';

/**
 * Recluster Command Handler
 *
 * Provides bot restart functionality for developers.
 */
@injectable()
export default class ReclusterCommandHandler extends BaseCommandHandler {
  readonly metadata = {
    name: 'recluster',
    description: 'Reboot the bot (Developer only)',
    category: CommandCategory.STAFF,
    staffOnly: true,
    ownerOnly: true, // Only bot owners can use this
    guildOnly: false,
    cooldown: 10000, // 10 second cooldown
  };

  buildCommand() {
    return new SlashCommandBuilder()
      .setName(this.metadata.name)
      .setDescription(this.metadata.description);
  }

  async execute(ctx: Context): Promise<FlexibleCommandResponse> {
    try {
      // Check if user is a developer (this would be configured in your system)
      const isDeveloper = this.isDeveloper(ctx.user.id);

      if (!isDeveloper) {
        const embed = this.createErrorEmbed('Access Denied', 'No u 😏');

        return {
          success: false,
          embed,
          flags: [MessageFlags.Ephemeral],
        };
      }

      // Create success embed
      const embed = this.createSuccessEmbed('Reclustering Bot', "I'll be back! 🤖");

      // Send response first
      const result: FlexibleCommandResponse = {
        success: true,
        embed,
        flags: [MessageFlags.Ephemeral],
      };

      // Trigger recluster after a short delay to allow response to send
      setTimeout(() => {
        this.triggerRecluster(ctx);
      }, 1000);

      return result;
    }
    catch (_error) {
      const embed = this.createErrorEmbed(
        'Recluster Failed',
        'An error occurred while attempting to recluster the bot.',
      );

      return {
        success: false,
        embed,
        flags: [MessageFlags.Ephemeral],
      };
    }
  }

  private isDeveloper(userId: string): boolean {
    // This should be configured through environment variables or configuration
    const developers = process.env.DEVELOPER_IDS?.split(',') || [];
    return developers.includes(userId);
  }

  private async triggerRecluster(ctx: Context) {
    await ctx.client.cluster?.send('recluster');
  }
}
