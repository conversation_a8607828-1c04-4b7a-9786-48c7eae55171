/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { ChatInputCommandInteraction, SlashCommandBuilder, EmbedBuilder } from 'discord.js';
import { injectable, inject } from 'inversify';
import { TYPES } from '../../../shared/types/TYPES.js';
import {
  BaseCommandHandler,
  CommandMetadata,
  CommandCategory,
  CommandResult,
} from '../BaseCommandHandler.js';
import { GetUserPremiumUseCase } from '../../../application/use-cases/donations/GetUserPremiumUseCase.js';
import { InteractionDataConverter, PremiumStatusDisplay } from '../../dto/InteractionDto.js';
import type { Context } from '../../../shared/context/index.js';

/**
 * Premium Status Command Handler
 *
 * Handles the `/premium status` slash command for checking premium status.
 */
@injectable()
export class PremiumStatusCommandHandler extends BaseCommandHandler {
  readonly metadata: CommandMetadata = {
    name: 'premium-status',
    description: 'Check premium status for yourself or another user',
    category: CommandCategory.DONATION,
    cooldown: 5,
    guildOnly: false,
    ownerOnly: false,
    premiumOnly: false,
  };

  constructor(
    @inject(TYPES.GetUserPremiumUseCase)
    private readonly getUserPremiumUseCase: GetUserPremiumUseCase,
  ) {
    super();
  }

  buildCommand(): SlashCommandBuilder {
    return new SlashCommandBuilder()
      .setName(this.metadata.name)
      .setDescription(this.metadata.description)
      .addUserOption((option) =>
        option
          .setName('user')
          .setDescription('User to check premium status for (defaults to yourself)')
          .setRequired(false),
      ) as SlashCommandBuilder;
  }

  async execute(ctx: Context): Promise<CommandResult> {
    try {
      const interaction = ctx.interaction as ChatInputCommandInteraction;
      // Get target user (default to command executor)
      const targetUser = interaction.options.getUser('user') || interaction.user;
      const isOwnStatus = targetUser.id === interaction.user.id;

      // Execute use case
      const result = await this.getUserPremiumUseCase.execute({
        userId: targetUser.id,
      });

      // Format premium status for display
      const statusDisplay = InteractionDataConverter.formatPremiumStatus(targetUser, result);

      // Create response embed
      const embed = this.createPremiumStatusEmbed(statusDisplay, isOwnStatus);

      return {
        success: true,
        embed,
        ephemeral: !isOwnStatus, // Show publicly if checking someone else, privately if own status
      };
    }
    catch (error) {
      return this.handleError(error, ctx);
    }
  }

  private createPremiumStatusEmbed(
    status: PremiumStatusDisplay,
    isOwnStatus: boolean,
  ): EmbedBuilder {
    const embed = new EmbedBuilder()
      .setTitle(`${status.isPremium ? '⭐' : '👤'} Premium Status`)
      .setDescription(this.getPremiumDescription(status, isOwnStatus))
      .setColor(status.isPremium ? 0xffd700 : 0x808080)
      .setThumbnail(status.user.displayAvatarURL())
      .setTimestamp();

    // Add status fields
    embed.addFields([
      {
        name: '👤 User',
        value: `${status.user.username}`,
        inline: true,
      },
      {
        name: '🎖️ Status',
        value: status.isPremium ? 'Premium Member' : 'Regular User',
        inline: true,
      },
      {
        name: '🏆 Tier',
        value: status.tierName,
        inline: true,
      },
    ]);

    // Add premium-specific fields
    if (status.isPremium) {
      if (status.expiresAt) {
        embed.addFields([
          {
            name: '⏰ Expires',
            value: this.formatRelativeTimestamp(status.expiresAt),
            inline: true,
          },
        ]);
      }

      embed.addFields([
        {
          name: '💰 Total Donated',
          value: InteractionDataConverter.formatCurrency(status.totalDonated, 'USD'),
          inline: true,
        },
      ]);

      // Add premium benefits
      embed.addFields([
        {
          name: '✨ Premium Benefits',
          value: this.getPremiumBenefits(status.tier),
          inline: false,
        },
      ]);
    }
    else {
      // Add information for non-premium users
      embed.addFields([
        {
          name: '💎 Become Premium',
          value:
            'Support InterChat by [donating here](https://ko-fi.com/interchat) to unlock premium features!',
          inline: false,
        },
      ]);
    }

    return embed;
  }

  private getPremiumDescription(status: PremiumStatusDisplay, isOwnStatus: boolean): string {
    const userName = isOwnStatus ? 'You' : status.user.username;

    if (status.isPremium) {
      return `${userName} ${isOwnStatus ? 'are' : 'is'} a premium member! Thank you for supporting InterChat! 🎉`;
    }
    else {
      return `${userName} ${isOwnStatus ? 'are' : 'is'} currently a regular user.`;
    }
  }

  private getPremiumBenefits(tier: number): string {
    const baseBenefits = [
      '🎨 Custom hub names',
      '⚡ Priority support',
      '🚀 Early access to features',
      '💫 Premium badge',
    ];

    const tierBenefits: Record<number, string[]> = {
      1: baseBenefits,
      2: [...baseBenefits, '🔧 Advanced moderation tools'],
      3: [...baseBenefits, '🔧 Advanced moderation tools', '📊 Detailed analytics'],
      4: [
        ...baseBenefits,
        '🔧 Advanced moderation tools',
        '📊 Detailed analytics',
        '👑 VIP support',
      ],
    };

    const benefits = tierBenefits[tier] || baseBenefits;
    return benefits.join('\n');
  }
}
