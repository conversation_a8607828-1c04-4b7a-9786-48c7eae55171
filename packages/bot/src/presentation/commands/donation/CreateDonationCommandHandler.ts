/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { ChatInputCommandInteraction, SlashCommandBuilder } from 'discord.js';
import { injectable, inject } from 'inversify';
import { TYPES } from '../../../shared/types/TYPES.js';
import {
  BaseCommandHandler,
  CommandMetadata,
  CommandCategory,
  CommandResult,
} from '../BaseCommandHandler.js';
import { CreateDonationUseCase } from '../../../application/use-cases/donations/CreateDonationUseCase.js';
import { InteractionDataConverter } from '../../dto/InteractionDto.js';
import type { Context } from '../../../shared/context/index.js';

/**
 * Create Donation Command Handler
 *
 * Handles the `/donation create` slash command for processing donations.
 */
@injectable()
export class CreateDonationCommandHandler extends BaseCommandHandler {
  readonly metadata: CommandMetadata = {
    name: 'donation-create',
    description: 'Process a new donation and grant premium status',
    category: CommandCategory.DONATION,
    cooldown: 10,
    permissions: ['STAFF'],
    guildOnly: false,
    ownerOnly: false,
    premiumOnly: false,
  };

  constructor(
    @inject(TYPES.CreateDonationUseCase)
    private readonly createDonationUseCase: CreateDonationUseCase,
  ) {
    super();
  }

  buildCommand(): SlashCommandBuilder {
    return new SlashCommandBuilder()
      .setName(this.metadata.name)
      .setDescription(this.metadata.description)
      .addUserOption((option) =>
        option.setName('user').setDescription('The user who made the donation').setRequired(true),
      )
      .addNumberOption((option) =>
        option
          .setName('amount')
          .setDescription('Donation amount')
          .setRequired(true)
          .setMinValue(0.01),
      )
      .addStringOption((option) =>
        option
          .setName('currency')
          .setDescription('Currency of the donation')
          .setRequired(false)
          .addChoices(
            { name: 'USD ($)', value: 'USD' },
            { name: 'EUR (€)', value: 'EUR' },
            { name: 'GBP (£)', value: 'GBP' },
            { name: 'CAD (C$)', value: 'CAD' },
            { name: 'AUD (A$)', value: 'AUD' },
          ),
      )
      .addIntegerOption((option) =>
        option
          .setName('tier')
          .setDescription('Donation tier (1-4)')
          .setRequired(false)
          .setMinValue(1)
          .setMaxValue(4),
      )
      .addStringOption((option) =>
        option
          .setName('kofi_id')
          .setDescription('Ko-fi transaction ID (if applicable)')
          .setRequired(false),
      )
      .addStringOption((option) =>
        option
          .setName('message')
          .setDescription('Optional message from the donor')
          .setRequired(false),
      ) as SlashCommandBuilder;
  }

  async execute(ctx: Context): Promise<CommandResult> {
    try {
      const interaction = ctx.interaction as ChatInputCommandInteraction;
      // Defer reply since this might take a moment
      await interaction.deferReply({ ephemeral: true });

      // Extract data from interaction
      const donationData = InteractionDataConverter.extractDonationData(interaction);

      // Execute use case
      const result = await this.createDonationUseCase.execute({
        donorId: donationData.donor.id,
        amount: donationData.amount,
        currency: donationData.currency,
        tier: donationData.tier,
        kofiTransactionId: donationData.kofiTransactionId,
        metadata: {
          processedBy: interaction.user.id,
          processedAt: new Date().toISOString(),
          source: 'discord_command',
          donorUsername: donationData.donor.username,
          message: donationData.message,
        },
      });

      // Create response embed
      if (result.success) {
        const embed = this.createSuccessEmbed(
          'Donation Processed',
          this.formatSuccessMessage(donationData, result),
        );

        embed.addFields([
          {
            name: '💰 Amount',
            value: InteractionDataConverter.formatCurrency(
              donationData.amount,
              donationData.currency,
            ),
            inline: true,
          },
          {
            name: '👤 Donor',
            value: this.formatUser(donationData.donor.id),
            inline: true,
          },
          {
            name: '🎯 Tier',
            value: InteractionDataConverter.getTierName(donationData.tier),
            inline: true,
          },
        ]);

        if (result.premiumGranted && result.premiumExpiresAt) {
          embed.addFields([
            {
              name: '⭐ Premium Status',
              value: `Granted until ${this.formatTimestamp(result.premiumExpiresAt)}`,
              inline: false,
            },
          ]);
        }

        return {
          success: true,
          embed,
          ephemeral: true,
        };
      }
      else {
        return {
          success: false,
          embed: this.createErrorEmbed('Donation Failed', result.message),
          ephemeral: true,
        };
      }
    }
    catch (error) {
      return this.handleError(error, ctx);
    }
  }

  private formatSuccessMessage(
    donationData: { donor: { username: string }; amount: number; currency: string },
    result: { premiumGranted: boolean; message: string },
  ): string {
    const baseMessage = `Successfully processed donation from **${donationData.donor.username}**.`;

    if (result.premiumGranted) {
      return `${baseMessage}\n\n✨ Premium access has been granted! Thank you for supporting InterChat!`;
    }
    else {
      return `${baseMessage}\n\n📝 ${result.message}`;
    }
  }
}
