/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import {
  ButtonBuilder,
  ButtonStyle,
  ContainerBuilder,
  MessageFlags,
  SectionBuilder,
  SeparatorSpacingSize,
  SlashCommandBuilder,
  TextDisplayBuilder,
} from 'discord.js';
import { injectable } from 'inversify';
import { Context } from '../../../shared/context/index.js';
import { CustomID } from '../../../shared/utils/CustomID.js';
import {
  BaseCommandHandler,
  CommandCategory,
  type FlexibleCommandResponse,
} from '../BaseCommandHandler.js';

function createCompressedCustomId(prefix: string, suffix: string, params: string[] = []): string {
  const handlerId = `${prefix}:${suffix}`;
  return CustomID.withOptions(handlerId, params, { forceCompression: true }).toString();
}

@injectable()
export default class HubCommandHandler extends BaseCommandHandler {
  readonly metadata = {
    name: 'hub',
    description: 'Manage your InterChat hubs',
    category: CommandCategory.HUB,
    cooldown: 5,
    guildOnly: false,
    permissions: [],
  };

  buildCommand() {
    return new SlashCommandBuilder()
      .setName(this.metadata.name)
      .setDescription(this.metadata.description)
      .addSubcommand((subcommand) =>
        subcommand.setName('list').setDescription('List hubs you own or moderate'),
      )
      .addSubcommand((subcommand) =>
        subcommand
          .setName('info')
          .setDescription('Get detailed information about a hub')
          .addStringOption((option) =>
            option
              .setName('hub')
              .setDescription('The hub to get info about')
              .setRequired(true)
              .setAutocomplete(true),
          ),
      )
      .addSubcommand((subcommand) =>
        subcommand
          .setName('moderate')
          .setDescription('Open moderation panel for a hub')
          .addStringOption((option) =>
            option
              .setName('hub')
              .setDescription('The hub to moderate')
              .setRequired(true)
              .setAutocomplete(true),
          ),
      );
  }

  async execute(ctx: Context): Promise<FlexibleCommandResponse> {
    const subcommand = ctx.options.getSubcommand();

    try {
      switch (subcommand) {
        case 'list':
          return await this.handleListHubs(ctx);
        case 'info':
          return await this.handleHubInfo(ctx);
        case 'moderate':
          return await this.handleHubModerate(ctx);
        default:
          throw new Error(`Unknown subcommand: ${subcommand}`);
      }
    }
    catch (_error) {
      return {
        success: false,
        message: '❌ An error occurred while processing your request.',
        flags: [MessageFlags.Ephemeral],
      };
    }
  }

  private async handleListHubs(ctx: Context): Promise<FlexibleCommandResponse> {
    const container = new ContainerBuilder();

    container.addTextDisplayComponents(
      new TextDisplayBuilder().setContent('## 🏠 Your Hubs\nHubs you own or moderate'),
    );

    container.addSeparatorComponents((separator) =>
      separator.setSpacing(SeparatorSpacingSize.Large),
    );

    // TODO: Get actual user hubs from service
    const mockHubs = [
      { name: 'Gaming Central', role: 'Owner', members: 1250 },
      { name: 'Study Group', role: 'Moderator', members: 45 },
      { name: 'Tech Talk', role: 'Owner', members: 890 },
    ];

    if (mockHubs.length === 0) {
      container.addTextDisplayComponents(
        new TextDisplayBuilder().setContent(
          "### 📭 No Hubs Found\nYou don't own or moderate any hubs yet.\n\nUse `/hub-create` to create your first hub!",
        ),
      );
    }
    else {
      for (const hub of mockHubs) {
        const hubSection = new SectionBuilder()
          .addTextDisplayComponents(
            new TextDisplayBuilder().setContent(
              `### 🏠 ${hub.name}\n**Role:** ${hub.role}\n**Members:** ${hub.members} servers`,
            ),
          )
          .setButtonAccessory(
            new ButtonBuilder()
              .setCustomId(createCompressedCustomId('hub', 'manage', [hub.name]))
              .setLabel('Manage')
              .setStyle(ButtonStyle.Secondary)
              .setEmoji('⚙️'),
          );

        container.addSectionComponents(hubSection);
      }
    }

    await ctx.reply({
      components: [container],
      flags: ['IsComponentsV2'],
    });

    return { success: true };
  }

  private async handleHubInfo(ctx: Context): Promise<FlexibleCommandResponse> {
    const hubName = ctx.options.getString('hub');
    const container = new ContainerBuilder();

    container.addTextDisplayComponents(
      new TextDisplayBuilder().setContent(`## 🏠 ${hubName}\nDetailed hub information`),
    );

    container.addSeparatorComponents((separator) =>
      separator.setSpacing(SeparatorSpacingSize.Large),
    );

    // TODO: Get actual hub info from service
    const hubInfo = [
      '### 📊 Statistics\n**Connected Servers:** 125\n**Active Users:** 2,340\n**Messages Today:** 1,892',
      '### 🛡️ Moderation\n**Active Moderators:** 5\n**Recent Actions:** 12 (this week)',
      '### ⚙️ Settings\n**Visibility:** Public\n**Join Mode:** Open\n**Language Filter:** Enabled',
    ];

    for (const info of hubInfo) {
      container.addTextDisplayComponents(new TextDisplayBuilder().setContent(info));
    }

    await ctx.reply({
      components: [container],
      flags: ['IsComponentsV2'],
    });

    return { success: true };
  }

  private async handleHubModerate(ctx: Context): Promise<FlexibleCommandResponse> {
    const hubName = ctx.options.getString('hub');

    if (!hubName) {
      return {
        success: false,
        message: '❌ Hub name is required.',
        flags: [MessageFlags.Ephemeral],
      };
    }

    const container = new ContainerBuilder();

    container.addTextDisplayComponents(
      new TextDisplayBuilder().setContent(`## 🛡️ Moderation Panel\nManaging ${hubName}`),
    );

    container.addSeparatorComponents((separator) =>
      separator.setSpacing(SeparatorSpacingSize.Large),
    );

    // Moderation actions
    const actions = [
      { name: '🚫 Blacklist User', desc: 'Ban a user from the hub' },
      { name: '🔇 Mute Server', desc: 'Temporarily mute a server' },
      { name: '📋 View Reports', desc: 'Review recent user reports' },
      { name: '📊 Mod Logs', desc: 'View moderation history' },
    ];

    for (const action of actions) {
      const actionSection = new SectionBuilder()
        .addTextDisplayComponents(
          new TextDisplayBuilder().setContent(`### ${action.name}\n${action.desc}`),
        )
        .setButtonAccessory(
          new ButtonBuilder()
            .setCustomId(createCompressedCustomId('hub', 'mod_action', [hubName, action.name]))
            .setLabel('Execute')
            .setStyle(ButtonStyle.Secondary)
            .setEmoji('▶️'),
        );

      container.addSectionComponents(actionSection);
    }

    await ctx.reply({
      components: [container],
      flags: ['IsComponentsV2'],
    });

    return { success: true };
  }
}
