/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { EmbedBuilder, MessageFlags, SlashCommandBuilder } from 'discord.js';
import { inject, injectable } from 'inversify';
import type { GetHubUseCase } from '../../../application/use-cases/hub/GetHubUseCase.js';
import type { UpdateHubUseCase } from '../../../application/use-cases/hub/UpdateHubUseCase.js';
import { Hub, HubUpdateData } from '../../../domain/entities/Hub.js';
import type { Context } from '../../../shared/context/index.js';
import { TYPES } from '../../../shared/types/TYPES.js';
import { BaseCommandHandler, CommandCategory, type FlexibleCommandResponse } from '../BaseCommandHandler.js';

interface HubEditOptions {
  hubName: string;
  description: string | null;
  iconUrl: string | null;
  bannerUrl: string | null;
  welcomeMessage: string | null;
  isPrivate: boolean | null;
  nsfw: boolean | null;
  appealCooldown: number | null;
}

@injectable()
export default class HubEditCommandHandler extends BaseCommandHandler {
  readonly metadata = {
    name: 'hub-edit',
    description: '✏️ Edit your hub settings',
    category: CommandCategory.HUB,
    cooldown: 15, // 15 second cooldown
    permissions: [],
    guildOnly: false,
    ownerOnly: false,
  };

  constructor(
    @inject(TYPES.UpdateHubUseCase) private updateHubUseCase: UpdateHubUseCase,
    @inject(TYPES.GetHubUseCase) private getHubUseCase: GetHubUseCase,
  ) {
    super();
  }

  buildCommand() {
    return new SlashCommandBuilder()
      .setName(this.metadata.name)
      .setDescription(this.metadata.description)
      .addStringOption((option) =>
        option.setName('name').setDescription('The name of the hub to edit').setRequired(true),
      )
      .addStringOption((option) =>
        option
          .setName('description')
          .setDescription('New description for the hub (max 500 characters)')
          .setRequired(false)
          .setMaxLength(500),
      )
      .addStringOption((option) =>
        option.setName('icon-url').setDescription('New icon URL for the hub').setRequired(false),
      )
      .addStringOption((option) =>
        option
          .setName('banner-url')
          .setDescription('New banner URL for the hub')
          .setRequired(false),
      )
      .addStringOption((option) =>
        option
          .setName('welcome-message')
          .setDescription('New welcome message for the hub (max 1000 characters)')
          .setRequired(false)
          .setMaxLength(1000),
      )
      .addBooleanOption((option) =>
        option
          .setName('private')
          .setDescription('Whether the hub should be private')
          .setRequired(false),
      )
      .addBooleanOption((option) =>
        option
          .setName('nsfw')
          .setDescription('Whether the hub allows NSFW content')
          .setRequired(false),
      )
      .addIntegerOption((option) =>
        option
          .setName('appeal-cooldown')
          .setDescription('Appeal cooldown in hours (1-168)')
          .setRequired(false)
          .setMinValue(1)
          .setMaxValue(168),
      );
  }

  async execute(ctx: Context): Promise<FlexibleCommandResponse> {
    const options = this.parseOptions(ctx);

    // Validate options
    const validationResult = this.validateOptions(options);
    if (!validationResult.valid) {
      return this.createErrorResponse(validationResult.message);
    }

    // Defer the reply since this operation might take a moment
    await ctx.deferReply({ flags: ['Ephemeral'] });

    // Validate hub access and ownership
    const { valid, hub, message } = await this.validateHubAccess(options.hubName, ctx.user.id);
    if (!valid || !hub) {
      return this.createErrorResponse(message);
    }

    // Prepare update data and execute update
    const updateData = this.prepareUpdateData(options);
    const updateResult = await this.updateHubUseCase.execute({
      hubId: hub.id,
      userId: ctx.user.id,
      updateData,
    });

    // Handle result
    if (updateResult.success && updateResult.hub) {
      return this.createSuccessResponse(updateResult.hub, options);
    }
    else {
      return this.createErrorResponse(
        `❌ **Failed to update hub**\n\n${updateResult.error || 'An unknown error occurred'}`,
      );
    }
  }

  private parseOptions(ctx: Context): HubEditOptions {
    return {
      hubName: ctx.options.getString('name')!,
      description: ctx.options.getString('description'),
      iconUrl: ctx.options.getString('icon-url'),
      bannerUrl: ctx.options.getString('banner-url'),
      welcomeMessage: ctx.options.getString('welcome-message'),
      isPrivate: ctx.options.getBoolean('private'),
      nsfw: ctx.options.getBoolean('nsfw'),
      appealCooldown: ctx.options.getInteger('appeal-cooldown'),
    };
  }

  private validateOptions(options: HubEditOptions): { valid: boolean; message?: string } {
    if (!options.hubName) {
      return { valid: false, message: '❌ **Hub name is required.**' };
    }

    // Check if at least one field to update is provided
    const hasUpdates =
      options.description !== null ||
      options.iconUrl !== null ||
      options.bannerUrl !== null ||
      options.welcomeMessage !== null ||
      options.isPrivate !== null ||
      options.nsfw !== null ||
      options.appealCooldown !== null;

    if (!hasUpdates) {
      return { valid: false, message: '❌ **Please provide at least one field to update.**' };
    }

    return { valid: true };
  }

  private async validateHubAccess(
    hubName: string,
    userId: string,
  ): Promise<{ valid: boolean; message?: string; hub?: Hub }> {
    // Get the hub to validate ownership and existence
    const getResult = await this.getHubUseCase.execute({
      hubName,
      userId,
    });

    if (!getResult.success || !getResult.hub) {
      return {
        valid: false,
        message: `❌ **Hub not found or you don't have access to it.**\n\n${getResult.error || 'Hub does not exist'}`,
      };
    }

    const hub = getResult.hub;

    // Check if user is the owner
    if (!hub.isOwner(userId)) {
      return { valid: false, message: '❌ **Only the hub owner can edit the hub.**' };
    }

    return { valid: true, hub };
  }

  private prepareUpdateData(options: HubEditOptions): HubUpdateData {
    const updateData: HubUpdateData = {};
    if (options.description !== null) updateData.description = options.description;
    if (options.iconUrl !== null) updateData.iconUrl = options.iconUrl;
    if (options.bannerUrl !== null) updateData.bannerUrl = options.bannerUrl;
    if (options.welcomeMessage !== null) updateData.welcomeMessage = options.welcomeMessage;
    if (options.isPrivate !== null) updateData.private = options.isPrivate;
    if (options.nsfw !== null) updateData.nsfw = options.nsfw;
    if (options.appealCooldown !== null) updateData.appealCooldownHours = options.appealCooldown;

    return updateData;
  }

  private createSuccessResponse(updatedHub: Hub, options: HubEditOptions): FlexibleCommandResponse {
    const changedFields = this.getChangedFields(options);

    const embed = new EmbedBuilder()
      .setColor(0x00ff00)
      .setTitle('✏️ Hub Updated Successfully!')
      .setDescription(`The hub **${updatedHub.name}** has been updated.`)
      .addFields(
        { name: 'Updated Fields', value: changedFields.join(', '), inline: false },
        { name: 'Private', value: updatedHub.isPrivate ? 'Yes' : 'No', inline: true },
        { name: 'NSFW', value: updatedHub.isNsfw ? 'Yes' : 'No', inline: true },
        { name: 'Updated', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: true },
      );

    if (updatedHub.iconUrl) {
      embed.setThumbnail(updatedHub.iconUrl);
    }

    return {
      success: true,
      embed,
      flags: [MessageFlags.Ephemeral],
    };
  }

  private getChangedFields(options: HubEditOptions): string[] {
    const changedFields: string[] = [];

    if (options.description !== null) changedFields.push('Description');
    if (options.iconUrl !== null) changedFields.push('Icon URL');
    if (options.bannerUrl !== null) changedFields.push('Banner URL');
    if (options.welcomeMessage !== null) changedFields.push('Welcome Message');
    if (options.isPrivate !== null) changedFields.push('Privacy');
    if (options.nsfw !== null) changedFields.push('NSFW Setting');
    if (options.appealCooldown !== null) changedFields.push('Appeal Cooldown');

    return changedFields;
  }

  private createErrorResponse(message?: string): FlexibleCommandResponse {
    return {
      success: false,
      message,
      flags: [MessageFlags.Ephemeral],
    };
  }
}
