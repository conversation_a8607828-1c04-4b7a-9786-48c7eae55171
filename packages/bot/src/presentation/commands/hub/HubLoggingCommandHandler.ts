/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import {
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
  ChannelSelectMenuBuilder,
  ChannelType,
  ContainerBuilder,
  MessageFlags,
  RoleSelectMenuBuilder,
  SectionBuilder,
  SeparatorSpacingSize,
  SlashCommandBuilder,
  TextDisplayBuilder,
} from 'discord.js';
import { inject, injectable } from 'inversify';
import type { HubLogConfigService } from '../../../application/services/HubLogConfigService.js';
import type { HubService } from '../../../application/services/HubService.js';
import {
  HubLogConfig,
  type LogConfigType,
  type RoleIdLogConfig,
} from '../../../domain/entities/HubLogConfig.js';
import {
  isInteractionContext,
  type CommandContext,
} from '../../../shared/context/CommandContext.js';
import { TYPES } from '../../../shared/types/TYPES.js';
import { CustomID } from '../../../shared/utils/CustomID.js';
import { Logger } from '../../../shared/utils/Logger.js';
import {
  BaseCommandHandler,
  CommandCategory,
  FlexibleCommandResponse,
} from '../BaseCommandHandler.js';

const ALLOWED_CHANNEL_TYPES = [
  ChannelType.GuildText,
  ChannelType.PublicThread,
  ChannelType.PrivateThread,
  ChannelType.GuildAnnouncement,
] as const;

/**
 * Helper function to create compressed CustomIDs for interactions
 */
function createCompressedCustomId(prefix: string, suffix: string, params: string[] = []): string {
  const handlerId = `${prefix}:${suffix}`;
  return CustomID.withOptions(handlerId, params, { forceCompression: true }).toString();
}

@injectable()
export default class HubLoggingCommandHandler extends BaseCommandHandler {
  readonly metadata = {
    name: 'hub-logging',
    description: '🔎 Configure logging channels for your hub',
    category: CommandCategory.HUB,
    cooldown: 10,
    permissions: [],
    guildOnly: true,
    ownerOnly: false,
  };

  constructor(
    @inject(TYPES.HubService) private readonly hubService: HubService,
    @inject(TYPES.HubLogConfigService) private readonly hubLogConfigService: HubLogConfigService,
  ) {
    super();
  }

  defineSlashCommand() {
    return new SlashCommandBuilder()
      .setName(this.metadata.name)
      .setDescription(this.metadata.description)
      .addStringOption((option) =>
        option
          .setName('hub')
          .setDescription('The name of the hub to configure logging for')
          .setRequired(true)
          .setAutocomplete(true),
      ) as SlashCommandBuilder;
  }

  buildCommand(): SlashCommandBuilder {
    return this.defineSlashCommand();
  }

  async execute(ctx: CommandContext): Promise<FlexibleCommandResponse> {
    if (!isInteractionContext(ctx) || !ctx.interaction.isChatInputCommand()) {
      return {
        success: false,
        message: 'This command can only be used as a slash command.',
        flags: [MessageFlags.Ephemeral],
      };
    }

    if (!ctx.guild) {
      return {
        success: false,
        message: '❌ This command can only be used in servers.',
        flags: [MessageFlags.Ephemeral],
      };
    }

    const hubName = ctx.interaction.options.getString('hub', true);

    try {
      // Get the hub by name
      const hub = await this.hubService.findHubByName(hubName);
      if (!hub) {
        return {
          success: false,
          message: `❌ Hub "${hubName}" not found.`,
          flags: [MessageFlags.Ephemeral],
        };
      }

      // TODO: Check if user has permission to manage this hub
      // For now, we'll skip this check and implement it later

      // Get the hub log configuration
      const logConfig = await this.hubLogConfigService.getHubLogConfig(hub.id);

      // Build the logging configuration container
      const container = await this.buildLoggingContainer(hub.id, ctx.user.id, logConfig);

      await ctx.reply({
        components: [container],
        flags: ['IsComponentsV2'],
      });

      return { success: true };
    }
    catch (error) {
      Logger.error('Failed to execute hub logging command', { hubName, error });
      return {
        success: false,
        message: '❌ An error occurred while configuring hub logging.',
        flags: [MessageFlags.Ephemeral],
      };
    }
  }

  /**
   * Build the main logging configuration container
   */
  async buildLoggingContainer(
    hubId: string,
    userId: string,
    logConfig: HubLogConfig | null,
  ): Promise<ContainerBuilder> {
    const container = new ContainerBuilder();

    // Header
    const headerText = new TextDisplayBuilder().setContent(
      '## 🔎 Hub Logging Configuration\nConfigure where different types of logs are sent in your server.',
    );
    container.addTextDisplayComponents(headerText);

    // Add separator
    container.addSeparatorComponents((separator) =>
      separator.setSpacing(SeparatorSpacingSize.Large),
    );

    // Add current log settings
    const channelStr = 'Channel';
    const roleStr = 'Role';
    const x_icon = '❌';

    // Create a section for each log type
    for (const type of HubLogConfig.logTypes) {
      const channelId = logConfig?.getChannelId(type);
      const roleId = logConfig?.getRoleId(type);

      const channelStatus = channelId ? `<#${channelId}>` : x_icon;
      const roleStatus = roleId
        ? `<@&${roleId}>`
        : HubLogConfig.logsWithRoleId.includes(type as RoleIdLogConfig)
          ? x_icon
          : 'N/A';

      const section = new SectionBuilder()
        .addTextDisplayComponents(
          new TextDisplayBuilder().setContent(
            `### ${this.toTitleCase(type)}\n${this.getLogTypeDescription(type)}\n**${channelStr}:** ${channelStatus}\n**${roleStr}:** ${roleStatus}`,
          ),
        )
        .setButtonAccessory(
          new ButtonBuilder()
            .setCustomId(createCompressedCustomId('hubLogging', 'configure', [userId, hubId, type]))
            .setLabel('Configure')
            .setStyle(ButtonStyle.Secondary)
            .setEmoji('⚙️'),
        );

      container.addSectionComponents(section);
    }

    // Add refresh button
    container.addActionRowComponents(
      new ActionRowBuilder<ButtonBuilder>().addComponents(
        new ButtonBuilder()
          .setCustomId(createCompressedCustomId('hubLogging', 'refresh', [userId, hubId]))
          .setEmoji('🔄')
          .setStyle(ButtonStyle.Secondary)
          .setLabel('Refresh'),
      ),
    );

    return container;
  }

  /**
   * Build the specific log type configuration container
   */
  async buildLogTypeContainer(
    hubId: string,
    userId: string,
    logType: LogConfigType,
    logConfig: HubLogConfig | null,
  ): Promise<ContainerBuilder> {
    const container = new ContainerBuilder();

    // Add header
    const headerText = new TextDisplayBuilder().setContent(
      `## Configuring \`${this.toTitleCase(logType)}\` Logs\nUse the options below to set up where these logs will be sent.`,
    );
    container.addTextDisplayComponents(headerText);

    // Add description
    const descriptionText = new TextDisplayBuilder().setContent(
      `### ${this.toTitleCase(logType)}\n${this.getLogTypeDescription(logType)}`,
    );
    container.addTextDisplayComponents(descriptionText);

    // Add separator
    container.addSeparatorComponents((separator) =>
      separator.setSpacing(SeparatorSpacingSize.Large),
    );

    // Add channel selection
    const channelSelectText = new TextDisplayBuilder().setContent(
      `### Channel Selection\nSelect a channel where ${this.toTitleCase(logType)} logs will be sent:`,
    );
    container.addTextDisplayComponents(channelSelectText);

    // Add channel select menu
    const channelId = logConfig?.getChannelId(logType);
    const channelSelectRow = new ActionRowBuilder<ChannelSelectMenuBuilder>().addComponents(
      new ChannelSelectMenuBuilder()
        .setCustomId(createCompressedCustomId('hubLogging', 'channel', [userId, hubId, logType]))
        .setPlaceholder('Select a channel to send logs to')
        .addChannelTypes(...ALLOWED_CHANNEL_TYPES)
        .setDefaultChannels(channelId ? [channelId] : [])
        .setMinValues(0),
    );
    container.addActionRowComponents(channelSelectRow);

    // Add role selection if applicable
    if (HubLogConfig.logsWithRoleId.includes(logType as RoleIdLogConfig)) {
      const roleSelectText = new TextDisplayBuilder().setContent(
        `### Role Mention\nSelect a role to be pinged when ${this.toTitleCase(logType)} logs are sent:`,
      );
      container.addTextDisplayComponents(roleSelectText);

      const roleId = logConfig?.getRoleId(logType);
      const roleSelectRow = new ActionRowBuilder<RoleSelectMenuBuilder>().addComponents(
        new RoleSelectMenuBuilder()
          .setCustomId(createCompressedCustomId('hubLogging', 'role', [userId, hubId, logType]))
          .setPlaceholder('Select a role to ping when logs are sent')
          .setDefaultRoles(roleId ? [roleId] : [])
          .setMinValues(0),
      );
      container.addActionRowComponents(roleSelectRow);
    }

    // Add back button
    container.addActionRowComponents(
      new ActionRowBuilder<ButtonBuilder>().addComponents(
        new ButtonBuilder()
          .setCustomId(createCompressedCustomId('hubLogging', 'back', [userId, hubId]))
          .setLabel('Back to Overview')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('⬅️'),
      ),
    );

    return container;
  }

  /**
   * Get a description for a log type
   */
  private getLogTypeDescription(logType: LogConfigType): string {
    switch (logType) {
      case 'modLogs':
        return 'Records all moderation actions taken within your hub';
      case 'joinLeaves':
        return 'Tracks servers joining or leaving your hub';
      case 'appeals':
        return 'Tracks blacklist appeal requests from users';
      case 'reports':
        return 'Receives notifications when content is reported by users';
      case 'networkAlerts':
        return 'Receives important system notifications and alerts';
      case 'messageModeration':
        return 'Records message deletions and edits by moderators';
      default:
        return 'Log type configuration';
    }
  }

  /**
   * Convert text to title case
   */
  private toTitleCase(str: string): string {
    return str.replace(/([A-Z])/g, ' $1').replace(/^./, (s) => s.toUpperCase());
  }
}
