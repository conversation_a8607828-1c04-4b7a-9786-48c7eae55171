/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { EmbedBuilder, MessageFlags, SlashCommandBuilder } from 'discord.js';
import { inject, injectable } from 'inversify';
import type {
  ListHubsRequest,
  ListHubsUseCase,
} from '../../../application/use-cases/hub/ListHubsUseCase.js';
import type { Hub } from '../../../domain/entities/Hub.js';
import type { Context } from '../../../shared/context/index.js';
import { TYPES } from '../../../shared/types/TYPES.js';
import { Logger } from '../../../shared/utils/Logger.js';
import {
  BaseCommandHandler,
  CommandCategory,
  FlexibleCommandResponse,
} from '../BaseCommandHandler.js';

interface ListOptions {
  type: string;
  search: string | null;
  limit: number;
}

@injectable()
export default class HubListCommandHandler extends BaseCommandHandler {
  readonly metadata = {
    name: 'hub-list',
    description: '📋 List hubs',
    category: CommandCategory.HUB,
    cooldown: 10,
    permissions: [],
    guildOnly: false,
    ownerOnly: false,
  };

  constructor(@inject(TYPES.ListHubsUseCase) private listHubsUseCase: ListHubsUseCase) {
    super();
  }

  buildCommand() {
    return new SlashCommandBuilder()
      .setName(this.metadata.name)
      .setDescription(this.metadata.description)
      .addStringOption((option) =>
        option
          .setName('type')
          .setDescription('What type of hubs to list')
          .setRequired(false)
          .addChoices(
            { name: 'My Hubs', value: 'owned' },
            { name: 'Public Hubs', value: 'public' },
            { name: 'Popular Hubs', value: 'popular' },
            { name: 'Search by Name', value: 'search' },
          ),
      )
      .addStringOption((option) =>
        option
          .setName('search')
          .setDescription('Search for hubs by name (only used with "Search by Name" type)')
          .setRequired(false)
          .setMinLength(3)
          .setMaxLength(32),
      )
      .addIntegerOption((option) =>
        option
          .setName('limit')
          .setDescription('Maximum number of hubs to show (1-20)')
          .setRequired(false)
          .setMinValue(1)
          .setMaxValue(20),
      );
  }

  async execute(ctx: Context): Promise<FlexibleCommandResponse> {
    try {
      // Parse and validate options
      const options = this.parseOptions(ctx);
      const validationResult = this.validateOptions(options);

      if (!validationResult.valid) {
        return this.createErrorResponse(validationResult.message || '');
      }

      // Defer the reply since this operation might take a moment
      await ctx.deferReply({ flags: ['Ephemeral'] });

      // Get list request parameters based on options
      const request = this.buildListRequest(options, ctx.user.id);
      const result = await this.listHubsUseCase.execute(request);

      if (!result.success || !result.hubs) {
        return this.createErrorResponse('❌ **Failed to retrieve hubs.**');
      }

      // Create response
      return this.createListResponse(result.hubs, options, ctx.user.id);
    }
    catch (error) {
      Logger.error('Error executing hub list command:', error);
      return this.createErrorResponse('❌ **An unexpected error occurred.**');
    }
  }

  /**
   * Parse command options
   */
  private parseOptions(ctx: Context): ListOptions {
    return {
      type: ctx.options.getString('type') || 'popular',
      search: ctx.options.getString('search'),
      limit: ctx.options.getInteger('limit') || 10,
    };
  }

  /**
   * Validate command options
   */
  private validateOptions(options: ListOptions): { valid: boolean; message?: string } {
    if (options.type === 'search' && !options.search) {
      return {
        valid: false,
        message: '❌ **Search term is required when using "Search by Name" type.**',
      };
    }

    return { valid: true };
  }

  /**
   * Build a request for the ListHubsUseCase based on options
   */
  private buildListRequest(options: ListOptions, userId: string): ListHubsRequest {
    const request: ListHubsRequest = { limit: options.limit };

    switch (options.type) {
      case 'owned':
        request.userId = userId;
        request.includePrivate = true;
        break;
      case 'public':
        request.includePrivate = false;
        break;
      case 'popular':
        request.onlyPopular = true;
        break;
      case 'search':
        request.searchPattern = options.search ?? undefined;
        request.includePrivate = false;
        break;
      default:
        request.onlyPopular = true;
    }

    return request;
  }

  /**
   * Create a response with the list of hubs
   */
  private createListResponse(
    hubs: Hub[],
    options: ListOptions,
    userId: string,
  ): FlexibleCommandResponse {
    if (hubs.length === 0) {
      return {
        success: true,
        message: this.getEmptyResultMessage(options),
        flags: [MessageFlags.Ephemeral],
      };
    }

    const embed = this.createHubListEmbed(hubs, options, userId);
    return {
      success: true,
      embed,
      flags: [MessageFlags.Ephemeral],
    };
  }

  /**
   * Get appropriate message for empty results
   */
  private getEmptyResultMessage(options: ListOptions): string {
    switch (options.type) {
      case 'owned':
        return "📋 **You don't own any hubs yet.**\n\nUse `/hub-create` to create your first hub!";
      case 'search':
        return `📋 **No hubs found matching "${options.search}".**`;
      case 'public':
        return '📋 **No public hubs found.**';
      case 'popular':
        return '📋 **No popular hubs found.**';
      default:
        return '📋 **No hubs found.**';
    }
  }

  /**
   * Create an embed to display the list of hubs
   */
  private createHubListEmbed(hubs: Hub[], options: ListOptions, userId: string): EmbedBuilder {
    const embed = new EmbedBuilder()
      .setColor(0x0099ff)
      .setTitle(this.getEmbedTitle(options))
      .setTimestamp();

    const description = this.getEmbedDescription(options, hubs.length);
    embed.setDescription(description);

    // Add hub entries (limit to reasonable number)
    const displayHubs = hubs.slice(0, Math.min(hubs.length, 10));

    displayHubs.forEach((hub) => {
      embed.addFields({
        name: this.formatHubName(hub, userId),
        value: this.formatHubListEntry(hub),
        inline: false,
      });
    });

    // Add note if there are more results
    if (hubs.length > 10) {
      embed.addFields({
        name: 'Note',
        value: `Only showing first 10 results. ${hubs.length - 10} more hub${hubs.length - 10 === 1 ? '' : 's'} available.`,
        inline: false,
      });
    }

    return embed;
  }

  /**
   * Format hub name with appropriate icons
   */
  private formatHubName(hub: Hub, userId: string): string {
    const isOwner = hub.isOwner(userId);
    const ownerIndicator = isOwner ? ' 👑' : '';
    const privacyIndicator = hub.isPrivate ? '🔒' : '🌐';
    const nsfwIndicator = hub.isNsfw ? '🔞' : '';

    return `${privacyIndicator} ${hub.name}${ownerIndicator}${nsfwIndicator}`;
  }

  /**
   * Get appropriate title for the embed based on options
   */
  private getEmbedTitle(options: ListOptions): string {
    switch (options.type) {
      case 'owned':
        return '📋 Your Hubs';
      case 'public':
        return '📋 Public Hubs';
      case 'popular':
        return '📋 Popular Hubs';
      case 'search':
        return `🔍 Hub Search: "${options.search}"`;
      default:
        return '📋 Hub List';
    }
  }

  /**
   * Get appropriate description for the embed based on options and results
   */
  private getEmbedDescription(options: ListOptions, resultCount: number): string {
    let description = `Showing ${resultCount} `;

    switch (options.type) {
      case 'owned':
        description += 'of your hubs';
        break;
      case 'public':
        description += 'public hubs';
        break;
      case 'popular':
        description += 'popular hubs';
        break;
      case 'search':
        description += `results for "${options.search}"`;
        break;
      default:
        description += 'hubs';
    }

    description += '\n\n-# **Legend**: 🌐 Public | 🔒 Private | 👑 Owner | 🔞 NSFW';
    return description;
  }

  /**
   * Format a hub entry for the list
   */
  private formatHubListEntry(hub: Hub): string {
    const description = hub.description
      ? hub.description.length > 100
        ? `${hub.description.substring(0, 97)}...`
        : hub.description
      : 'No description';

    return `${description}`;
  }

  /**
   * Create error response
   */
  private createErrorResponse(message: string): FlexibleCommandResponse {
    return {
      success: false,
      message,
      flags: [MessageFlags.Ephemeral],
    };
  }
}
