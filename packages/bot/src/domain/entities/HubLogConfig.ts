/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import type { HubLogConfig as PrismaHubLogConfig } from '../../../../../build/generated/prisma/client/index.js';

export type LogConfigType = 'modLogs' | 'joinLeaves' | 'appeals' | 'reports' | 'networkAlerts' | 'messageModeration';
export type RoleIdLogConfig = 'appeals' | 'reports' | 'networkAlerts' | 'messageModeration';

export interface HubLogConfigCreationData {
  hubId: string;
  modLogsChannelId?: string;
  modLogsRoleId?: string;
  joinLeavesChannelId?: string;
  joinLeavesRoleId?: string;
  appealsChannelId?: string;
  appealsRoleId?: string;
  reportsChannelId?: string;
  reportsRoleId?: string;
  networkAlertsChannelId?: string;
  networkAlertsRoleId?: string;
  messageModerationChannelId?: string;
  messageModerationRoleId?: string;
}

export interface HubLogConfigUpdateData {
  modLogsChannelId?: string | null;
  modLogsRoleId?: string | null;
  joinLeavesChannelId?: string | null;
  joinLeavesRoleId?: string | null;
  appealsChannelId?: string | null;
  appealsRoleId?: string | null;
  reportsChannelId?: string | null;
  reportsRoleId?: string | null;
  networkAlertsChannelId?: string | null;
  networkAlertsRoleId?: string | null;
  messageModerationChannelId?: string | null;
  messageModerationRoleId?: string | null;
}

export class HubLogConfig {
  readonly id: string;
  readonly hubId: string;
  readonly modLogsChannelId?: string;
  readonly modLogsRoleId?: string;
  readonly joinLeavesChannelId?: string;
  readonly joinLeavesRoleId?: string;
  readonly appealsChannelId?: string;
  readonly appealsRoleId?: string;
  readonly reportsChannelId?: string;
  readonly reportsRoleId?: string;
  readonly networkAlertsChannelId?: string;
  readonly networkAlertsRoleId?: string;
  readonly messageModerationChannelId?: string;
  readonly messageModerationRoleId?: string;

  constructor(data: PrismaHubLogConfig) {
    this.id = data.id;
    this.hubId = data.hubId;
    this.modLogsChannelId = data.modLogsChannelId ?? undefined;
    this.modLogsRoleId = data.modLogsRoleId ?? undefined;
    this.joinLeavesChannelId = data.joinLeavesChannelId ?? undefined;
    this.joinLeavesRoleId = data.joinLeavesRoleId ?? undefined;
    this.appealsChannelId = data.appealsChannelId ?? undefined;
    this.appealsRoleId = data.appealsRoleId ?? undefined;
    this.reportsChannelId = data.reportsChannelId ?? undefined;
    this.reportsRoleId = data.reportsRoleId ?? undefined;
    this.networkAlertsChannelId = data.networkAlertsChannelId ?? undefined;
    this.networkAlertsRoleId = data.networkAlertsRoleId ?? undefined;
    this.messageModerationChannelId = data.messageModerationChannelId ?? undefined;
    this.messageModerationRoleId = data.messageModerationRoleId ?? undefined;
  }

  static readonly logTypes: LogConfigType[] = [
    'modLogs',
    'joinLeaves',
    'appeals',
    'reports',
    'networkAlerts',
    'messageModeration',
  ];

  static readonly logsWithRoleId: RoleIdLogConfig[] = [
    'appeals',
    'reports',
    'networkAlerts',
    'messageModeration',
  ];

  /**
   * Get the channel ID for a specific log type
   */
  getChannelId(type: LogConfigType): string | undefined {
    switch (type) {
      case 'modLogs':
        return this.modLogsChannelId;
      case 'joinLeaves':
        return this.joinLeavesChannelId;
      case 'appeals':
        return this.appealsChannelId;
      case 'reports':
        return this.reportsChannelId;
      case 'networkAlerts':
        return this.networkAlertsChannelId;
      case 'messageModeration':
        return this.messageModerationChannelId;
      default:
        return undefined;
    }
  }

  /**
   * Get the role ID for a specific log type (if applicable)
   */
  getRoleId(type: LogConfigType): string | undefined {
    if (!HubLogConfig.logsWithRoleId.includes(type as RoleIdLogConfig)) {
      return undefined;
    }

    switch (type) {
      case 'appeals':
        return this.appealsRoleId;
      case 'reports':
        return this.reportsRoleId;
      case 'networkAlerts':
        return this.networkAlertsRoleId;
      case 'messageModeration':
        return this.messageModerationRoleId;
      default:
        return undefined;
    }
  }

  /**
   * Check if a log type is configured (has a channel)
   */
  isConfigured(type: LogConfigType): boolean {
    return this.getChannelId(type) !== undefined;
  }

  /**
   * Check if a log type has role mentions configured
   */
  hasRoleMention(type: LogConfigType): boolean {
    return this.getRoleId(type) !== undefined;
  }

  /**
   * Create a new HubLogConfig from Prisma data
   */
  static fromPrisma(data: PrismaHubLogConfig): HubLogConfig {
    return new HubLogConfig(data);
  }

  /**
   * Convert to Prisma update data
   */
  toPrismaUpdateData(): HubLogConfigUpdateData {
    return {
      modLogsChannelId: this.modLogsChannelId ?? null,
      modLogsRoleId: this.modLogsRoleId ?? null,
      joinLeavesChannelId: this.joinLeavesChannelId ?? null,
      joinLeavesRoleId: this.joinLeavesRoleId ?? null,
      appealsChannelId: this.appealsChannelId ?? null,
      appealsRoleId: this.appealsRoleId ?? null,
      reportsChannelId: this.reportsChannelId ?? null,
      reportsRoleId: this.reportsRoleId ?? null,
      networkAlertsChannelId: this.networkAlertsChannelId ?? null,
      networkAlertsRoleId: this.networkAlertsRoleId ?? null,
      messageModerationChannelId: this.messageModerationChannelId ?? null,
      messageModerationRoleId: this.messageModerationRoleId ?? null,
    };
  }
}
