/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { BusinessRuleViolationError } from '../../shared/errors/DomainError.js';
import { DomainEvent } from '../../infrastructure/events/EventBus.js';
import { Money, DonationTier } from '../value-objects/DonationValueObjects.js';

/**
 * Donation Status Enumeration
 */
export enum DonationStatus {
  PENDING = 'PENDING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  REFUNDED = 'REFUNDED',
  CANCELLED = 'CANCELLED',
}

/**
 * Donation Source Enumeration
 */
export enum DonationSource {
  KOFI = 'KOFI',
  PAYPAL = 'PAYPAL',
  STRIPE = 'STRIPE',
  PATREON = 'PATREON',
  OTHER = 'OTHER',
}

/**
 * Donation Domain Entity
 *
 * Aggregate root for donation-related operations.
 * Handles business logic for processing donations and managing premium status.
 */
export class Donation {
  private domainEvents: DomainEvent[] = [];

  private constructor(
    public readonly id: string,
    public readonly userId: string,
    public readonly amount: Money,
    public readonly source: DonationSource,
    public readonly sourceTransactionId: string,
    public readonly donatedAt: Date,
    private _status: DonationStatus,
    public readonly message?: string,
    public readonly donorName?: string,
    public readonly donorEmail?: string,
    public readonly metadata?: Record<string, unknown>,
  ) {
    this.validateDonation();
  }

  /**
   * Create a new donation
   */
  static create(
    id: string,
    userId: string,
    amount: Money,
    source: DonationSource,
    sourceTransactionId: string,
    donatedAt: Date = new Date(),
    message?: string,
    donorName?: string,
    donorEmail?: string,
    metadata?: Record<string, unknown>,
  ): Donation {
    const donation = new Donation(
      id,
      userId,
      amount,
      source,
      sourceTransactionId,
      donatedAt,
      DonationStatus.PENDING,
      message,
      donorName,
      donorEmail,
      metadata,
    );

    // Raise domain event
    donation.addDomainEvent(new DonationCreatedEvent(
      donation.id,
      donation.userId,
      donation.amount,
      donation.source,
      donation.donatedAt,
    ));

    return donation;
  }

  /**
   * Reconstitute donation from persistence
   */
  static fromPersistence(
    id: string,
    userId: string,
    amount: Money,
    source: DonationSource,
    sourceTransactionId: string,
    donatedAt: Date,
    status: DonationStatus,
    message?: string,
    donorName?: string,
    donorEmail?: string,
    metadata?: Record<string, unknown>,
  ): Donation {
    return new Donation(
      id,
      userId,
      amount,
      source,
      sourceTransactionId,
      donatedAt,
      status,
      message,
      donorName,
      donorEmail,
      metadata,
    );
  }

  get status(): DonationStatus {
    return this._status;
  }

  /**
   * Mark donation as completed
   */
  markAsCompleted(): void {
    if (this._status !== DonationStatus.PENDING) {
      throw new BusinessRuleViolationError(
        `Cannot complete donation with status ${this._status}`,
      );
    }

    this._status = DonationStatus.COMPLETED;

    this.addDomainEvent(new DonationCompletedEvent(
      this.id,
      this.userId,
      this.amount,
      this.source,
    ));
  }

  /**
   * Mark donation as failed
   */
  markAsFailed(reason?: string): void {
    if (this._status === DonationStatus.COMPLETED) {
      throw new BusinessRuleViolationError('Cannot fail a completed donation');
    }

    this._status = DonationStatus.FAILED;

    this.addDomainEvent(new DonationFailedEvent(
      this.id,
      this.userId,
      this.amount,
      reason,
    ));
  }

  /**
   * Refund the donation
   */
  refund(reason?: string): void {
    if (this._status !== DonationStatus.COMPLETED) {
      throw new BusinessRuleViolationError('Can only refund completed donations');
    }

    this._status = DonationStatus.REFUNDED;

    this.addDomainEvent(new DonationRefundedEvent(
      this.id,
      this.userId,
      this.amount,
      reason,
    ));
  }

  /**
   * Check if donation qualifies for a tier
   */
  qualifiesForTier(tier: DonationTier): boolean {
    if (this._status !== DonationStatus.COMPLETED) {
      return false;
    }

    return tier.qualifiesForTier(this.amount);
  }

  /**
   * Check if donation is active (completed and not refunded)
   */
  isActive(): boolean {
    return this._status === DonationStatus.COMPLETED;
  }

  /**
   * Get domain events and clear them
   */
  getDomainEvents(): DomainEvent[] {
    const events = [...this.domainEvents];
    this.domainEvents = [];
    return events;
  }

  /**
   * Add a domain event
   */
  private addDomainEvent(event: DomainEvent): void {
    this.domainEvents.push(event);
  }

  private validateDonation(): void {
    if (!this.id || this.id.trim().length === 0) {
      throw new BusinessRuleViolationError('Donation ID cannot be empty');
    }

    if (!this.userId || this.userId.trim().length === 0) {
      throw new BusinessRuleViolationError('User ID cannot be empty');
    }

    if (!this.sourceTransactionId || this.sourceTransactionId.trim().length === 0) {
      throw new BusinessRuleViolationError('Source transaction ID cannot be empty');
    }

    if (this.donatedAt > new Date()) {
      throw new BusinessRuleViolationError('Donation date cannot be in the future');
    }
  }
}

/**
 * User Premium Status Entity
 *
 * Manages a user's premium subscription status and benefits.
 */
export class UserPremiumStatus {
  private domainEvents: DomainEvent[] = [];

  private constructor(
    public readonly userId: string,
    private _tier: DonationTier | null,
    private _expiresAt: Date | null,
    private _isActive: boolean,
    private _totalDonated: Money,
    private _lastDonationAt: Date | null,
  ) {
    this.validateStatus();
  }

  /**
   * Create new premium status for user
   */
  static create(userId: string): UserPremiumStatus {
    return new UserPremiumStatus(
      userId,
      null,
      null,
      false,
      Money.zero('USD'),
      null,
    );
  }

  /**
   * Reconstitute from persistence
   */
  static fromPersistence(
    userId: string,
    tier: DonationTier | null,
    expiresAt: Date | null,
    isActive: boolean,
    totalDonated: Money,
    lastDonationAt: Date | null,
  ): UserPremiumStatus {
    return new UserPremiumStatus(
      userId,
      tier,
      expiresAt,
      isActive,
      totalDonated,
      lastDonationAt,
    );
  }

  get tier(): DonationTier | null {
    return this._tier;
  }

  get expiresAt(): Date | null {
    return this._expiresAt;
  }

  get isActive(): boolean {
    return this._isActive && (this._expiresAt === null || this._expiresAt > new Date());
  }

  get totalDonated(): Money {
    return this._totalDonated;
  }

  get lastDonationAt(): Date | null {
    return this._lastDonationAt;
  }

  /**
   * Process a completed donation
   */
  processDonation(donation: Donation): void {
    if (!donation.isActive()) {
      throw new BusinessRuleViolationError('Cannot process inactive donation');
    }

    // Update totals
    this._totalDonated = this._totalDonated.add(donation.amount);
    this._lastDonationAt = donation.donatedAt;

    // Check if user qualifies for Ko-fi Supporter tier
    const kofiTier = DonationTier.createKofiSupporter();

    if (donation.qualifiesForTier(kofiTier)) {
      this.grantPremiumTier(kofiTier, this.calculateExpiryDate());
    }

    this.addDomainEvent(new UserDonationProcessedEvent(
      this.userId,
      donation.id,
      donation.amount,
      this._totalDonated,
    ));
  }

  /**
   * Grant premium tier to user
   */
  grantPremiumTier(tier: DonationTier, expiresAt: Date | null = null): void {
    const previousTier = this._tier;
    const wasActive = this._isActive;

    this._tier = tier;
    this._expiresAt = expiresAt;
    this._isActive = true;

    if (!wasActive || !previousTier?.equals(tier)) {
      this.addDomainEvent(new UserPremiumStatusChangedEvent(
        this.userId,
        true,
        tier.name,
        expiresAt,
      ));
    }
  }

  /**
   * Revoke premium status
   */
  revokePremiumStatus(_reason?: string): void {
    if (!this._isActive) {
      return; // Already inactive
    }

    this._isActive = false;
    this._expiresAt = new Date(); // Expire immediately

    this.addDomainEvent(new UserPremiumStatusChangedEvent(
      this.userId,
      false,
      null,
      null,
    ));
  }

  /**
   * Check if user has a specific benefit
   */
  hasBenefit(benefit: string): boolean {
    return this.isActive && this._tier?.hasBenefit(benefit) === true;
  }

  /**
   * Get domain events and clear them
   */
  getDomainEvents(): DomainEvent[] {
    const events = [...this.domainEvents];
    this.domainEvents = [];
    return events;
  }

  private addDomainEvent(event: DomainEvent): void {
    this.domainEvents.push(event);
  }

  private calculateExpiryDate(): Date {
    // Ko-fi supporters get 35 days (monthly + 5 day grace period)
    const expiryDate = new Date();
    expiryDate.setDate(expiryDate.getDate() + 35);
    return expiryDate;
  }

  private validateStatus(): void {
    if (!this.userId || this.userId.trim().length === 0) {
      throw new BusinessRuleViolationError('User ID cannot be empty');
    }
  }
}

// Domain Events

export class DonationCreatedEvent extends DomainEvent {
  constructor(
    public readonly donationId: string,
    public readonly userId: string,
    public readonly amount: Money,
    public readonly source: DonationSource,
    public readonly donatedAt: Date,
  ) {
    super('donation.created', donationId, 1, false); // Local event
  }

  protected getData(): Record<string, unknown> {
    return {
      donationId: this.donationId,
      userId: this.userId,
      amount: this.amount.toJSON(),
      source: this.source,
      donatedAt: this.donatedAt.toISOString(),
    };
  }
}

export class DonationCompletedEvent extends DomainEvent {
  constructor(
    public readonly donationId: string,
    public readonly userId: string,
    public readonly amount: Money,
    public readonly source: DonationSource,
  ) {
    super('donation.completed', donationId, 1, true); // Broadcast to all clusters
  }

  protected getData(): Record<string, unknown> {
    return {
      donationId: this.donationId,
      userId: this.userId,
      amount: this.amount.toJSON(),
      source: this.source,
    };
  }
}

export class DonationFailedEvent extends DomainEvent {
  constructor(
    public readonly donationId: string,
    public readonly userId: string,
    public readonly amount: Money,
    public readonly reason?: string,
  ) {
    super('donation.failed', donationId, 1, false); // Local event
  }

  protected getData(): Record<string, unknown> {
    return {
      donationId: this.donationId,
      userId: this.userId,
      amount: this.amount.toJSON(),
      reason: this.reason,
    };
  }
}

export class DonationRefundedEvent extends DomainEvent {
  constructor(
    public readonly donationId: string,
    public readonly userId: string,
    public readonly amount: Money,
    public readonly reason?: string,
  ) {
    super('donation.refunded', donationId, 1, true); // Broadcast to all clusters
  }

  protected getData(): Record<string, unknown> {
    return {
      donationId: this.donationId,
      userId: this.userId,
      amount: this.amount.toJSON(),
      reason: this.reason,
    };
  }
}

export class UserDonationProcessedEvent extends DomainEvent {
  constructor(
    public readonly userId: string,
    public readonly donationId: string,
    public readonly donationAmount: Money,
    public readonly totalDonated: Money,
  ) {
    super('user.donation.processed', userId, 1, false); // Local event
  }

  protected getData(): Record<string, unknown> {
    return {
      userId: this.userId,
      donationId: this.donationId,
      donationAmount: this.donationAmount.toJSON(),
      totalDonated: this.totalDonated.toJSON(),
    };
  }
}

export class UserPremiumStatusChangedEvent extends DomainEvent {
  constructor(
    public readonly userId: string,
    public readonly isPremium: boolean,
    public readonly tier: string | null,
    public readonly expiresAt: Date | null,
  ) {
    super('user.premium.status.changed', userId, 1, true); // Broadcast to all clusters
  }

  protected getData(): Record<string, unknown> {
    return {
      userId: this.userId,
      isPremium: this.isPremium,
      tier: this.tier,
      expiresAt: this.expiresAt?.toISOString() || null,
    };
  }
}
