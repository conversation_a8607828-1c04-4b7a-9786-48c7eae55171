/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import type { DomainEvent } from '../../infrastructure/events/EventBus.js';

/**
 * Event Bus Interface
 *
 * Defines the contract for event publishing and subscription.
 * Implementations may be local (in-memory) or distributed (Redis-based).
 */
export interface IEventBus {
  /**
   * Publish a single event
   */
  publish<T extends DomainEvent>(event: T): Promise<void>;

  /**
   * Subscribe to events of a specific type
   */
  subscribe<T extends DomainEvent>(
    eventType: string,
    handler: (event: T) => Promise<void>,
  ): Promise<void>;

  /**
   * Unsubscribe from events of a specific type
   */
  unsubscribe(
    eventType: string,
    handler: (event: DomainEvent) => Promise<void>,
  ): Promise<void>;
}
