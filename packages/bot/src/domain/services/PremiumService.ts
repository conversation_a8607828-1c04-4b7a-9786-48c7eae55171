/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

// Placeholder service interface for migration

export interface PremiumRedemptionResult {
  success: boolean;
  message: string;
  benefit: string;
  duration: string;
  expiresAt: Date;
}

export interface PremiumService {
  redeemPremiumCode(userId: string, code: string): Promise<PremiumRedemptionResult>;
}
