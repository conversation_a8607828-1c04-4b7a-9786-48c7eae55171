/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

// Placeholder service interface for migration

export interface User {
  id: string;
  image?: string;
}

export interface InboxMessage {
  id: string;
  from: string;
  subject: string;
  content: string;
  createdAt: Date;
  read: boolean;
}

export interface DailyReward {
  amount: number;
  currency: string;
  streak: number;
}

export interface Achievement {
  id: string;
  name: string;
  description: string;
  rarity: string;
  earnedAt: Date;
  iconUrl?: string;
}

export interface AchievementReward {
  amount: number;
  currency: string;
}

export interface ClaimableRewards {
  canClaimDaily: boolean;
  nextDailyClaimTime: Date;
  unclaimedAchievements: Achievement[];
  isPremium: boolean;
  premiumExpiresAt?: Date;
}

export interface UserService {
  getUser(userId: string): Promise<User | null>;
  getInboxMessages(userId: string, unreadOnly: boolean): Promise<InboxMessage[]>;
  getInboxMessage(userId: string, messageId: string): Promise<InboxMessage | null>;
  markInboxMessageAsRead(userId: string, messageId: string): Promise<void>;
  clearReadInboxMessages(userId: string): Promise<number>;
}
