/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

// Placeholder service interface for migration

import type { OriginalMessage } from './MessageService.js';
import type { Hub } from './HubService.js';

export interface ModerationResult {
  deletedCount: number;
  totalCount: number;
}

export interface EditResult {
  editedCount: number;
  totalCount: number;
}

export interface Report {
  id: string;
  messageId: string;
  hubId: string;
  reporterId: string;
  reportedUserId: string;
  reason: string;
  details?: string;
  guildId: string;
  channelId: string;
}

export interface ReportData {
  messageId: string;
  hubId: string;
  reporterId: string;
  reportedUserId: string;
  reason: string;
  details?: string;
  guildId: string;
  channelId: string;
}

export interface ModerationService {
  canUserDeleteMessage(
    userId: string,
    message: OriginalMessage,
    hub: Hub,
  ): Promise<boolean>;
  isDeleteInProgress(messageId: string): Promise<boolean>;
  deleteMessageFromHub(
    hubId: string,
    messageId: string,
    userId: string,
  ): Promise<ModerationResult>;
  logMessageDeletion(
    message: OriginalMessage,
    hub: Hub,
    userId: string,
    username: string,
  ): Promise<void>;
  editMessageInHub(
    hubId: string,
    messageId: string,
    content: string,
    userId: string,
  ): Promise<EditResult>;
  logMessageEdit(
    message: OriginalMessage,
    hub: Hub,
    userId: string,
    username: string,
    newContent: string,
  ): Promise<void>;
  getRecentReports(userId: string, messageId: string): Promise<Report[]>;
  createReport(data: ReportData): Promise<Report>;
  notifyHubModerators(hub: Hub, report: Report): Promise<void>;
}
