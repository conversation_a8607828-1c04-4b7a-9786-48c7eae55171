/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

// Placeholder repository interface for migration

export interface ServerConnection {
  id: string;
  guildId: string;
  channelId: string;
  hubId: string;
  hubName: string;
  connectedBy: string;
  connectedAt: Date;
}

export interface ServerConnectionData {
  guildId: string;
  channelId: string;
  hubId: string;
  connectedBy: string;
  webhookURL?: string;
}

export interface ServerRepository {
  getServerConnection(
    guildId: string,
    channelId: string,
  ): Promise<ServerConnection | null>;
  createServerConnection(data: ServerConnectionData): Promise<void>;
  getServerConnections(guildId: string): Promise<ServerConnection[]>;
  removeServerConnection(guildId: string, channelId: string): Promise<void>;
}
