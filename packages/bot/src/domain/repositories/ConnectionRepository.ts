/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

/**
 * Hub Connection Repository Interface
 *
 * Provides methods for querying and managing hub connections in the database.
 */
export interface IConnectionRepository {
  /**
   * Find a connection by channel ID
   */
  findByChannelId(channelId: string): Promise<HubConnectionData | null>;

  /**
   * Find all active connections for a hub, excluding a specific channel
   */
  findActiveByHubId(hubId: string, excludeChannelId?: string): Promise<HubConnectionData[]>;

  /**
   * Find all connections for a hub
   */
  findByHubId(hubId: string): Promise<HubConnectionData[]>;

  /**
   * Update the last active timestamp for a connection
   */
  updateLastActive(connectionId: string): Promise<void>;
}

/**
 * Hub Connection Data
 * Represents a connection between a Discord channel and a hub
 */
export interface HubConnectionData {
  id: string;
  channelId: string;
  connected: boolean;
  compact: boolean;
  webhookURL: string;
  parentId: string | null;
  hubId: string;
  embedColor: string | null;
  serverId: string;
  lastActive: Date;
}
