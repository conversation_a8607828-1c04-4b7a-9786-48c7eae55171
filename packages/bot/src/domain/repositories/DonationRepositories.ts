/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { Donation, UserPremiumStatus, DonationSource } from '../entities/Donation.js';
import { Money } from '../value-objects/DonationValueObjects.js';

/**
 * Donation Repository Interface
 *
 * Defines the contract for donation data access.
 * Implementation will be in the infrastructure layer.
 */
export interface IDonationRepository {
  /**
   * Save a donation
   */
  save(donation: Donation): Promise<void>;

  /**
   * Find donation by ID
   */
  findById(id: string): Promise<Donation | null>;

  /**
   * Find donation by source transaction ID
   */
  findBySourceTransactionId(
    sourceTransactionId: string,
    source: DonationSource,
  ): Promise<Donation | null>;

  /**
   * Find all donations for a user
   */
  findByUserId(userId: string): Promise<Donation[]>;

  /**
   * Find donations within date range
   */
  findByDateRange(startDate: Date, endDate: Date): Promise<Donation[]>;

  /**
   * Get total donated amount for a user
   */
  getTotalDonatedByUser(userId: string): Promise<Money>;

  /**
   * Get all active donations (completed and not refunded)
   */
  findActiveDonations(): Promise<Donation[]>;

  /**
   * Find donations by source
   */
  findBySource(source: DonationSource): Promise<Donation[]>;
}

/**
 * User Premium Status Repository Interface
 *
 * Manages user premium subscription data.
 */
export interface IUserPremiumRepository {
  /**
   * Save user premium status
   */
  save(status: UserPremiumStatus): Promise<void>;

  /**
   * Find premium status by user ID
   */
  findByUserId(userId: string): Promise<UserPremiumStatus | null>;

  /**
   * Find all active premium users
   */
  findAllActivePremium(): Promise<UserPremiumStatus[]>;

  /**
   * Find users with expired premium status
   */
  findExpiredPremium(): Promise<UserPremiumStatus[]>;

  /**
   * Find users by tier
   */
  findByTier(tierName: string): Promise<UserPremiumStatus[]>;

  /**
   * Get premium user count
   */
  getActivePremiumCount(): Promise<number>;
}

/**
 * Search criteria for donations
 */
export interface DonationSearchCriteria {
  userId?: string;
  source?: DonationSource;
  minAmount?: Money;
  maxAmount?: Money;
  startDate?: Date;
  endDate?: Date;
  status?: string[];
  limit?: number;
  offset?: number;
}

/**
 * Paginated result
 */
export interface PaginatedResult<T> {
  items: T[];
  total: number;
  hasMore: boolean;
  offset: number;
  limit: number;
}

/**
 * Extended donation repository for complex queries
 */
export interface IDonationQueryRepository {
  /**
   * Search donations with criteria
   */
  search(criteria: DonationSearchCriteria): Promise<PaginatedResult<Donation>>;

  /**
   * Get donation statistics
   */
  getDonationStats(): Promise<DonationStats>;

  /**
   * Get top donors
   */
  getTopDonors(limit: number): Promise<TopDonor[]>;

  /**
   * Get donation trends by month
   */
  getDonationTrends(months: number): Promise<DonationTrend[]>;
}

/**
 * Donation statistics
 */
export interface DonationStats {
  totalDonations: number;
  totalAmount: Money;
  activeDonors: number;
  averageDonation: Money;
  thisMonthDonations: number;
  thisMonthAmount: Money;
}

/**
 * Top donor information
 */
export interface TopDonor {
  userId: string;
  totalDonated: Money;
  donationCount: number;
  firstDonationAt: Date;
  lastDonationAt: Date;
}

/**
 * Donation trend data
 */
export interface DonationTrend {
  month: string; // YYYY-MM format
  donationCount: number;
  totalAmount: Money;
  uniqueDonors: number;
}
