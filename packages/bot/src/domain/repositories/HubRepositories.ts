/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { Hub } from '../entities/Hub.js';

/**
 * Hub Repository Interface
 *
 * Defines the contract for hub persistence operations.
 * Implementations should handle data persistence concerns while
 * the domain layer focuses on business logic.
 */
export interface IHubRepository {
  /**
   * Save a hub to persistent storage
   */
  save(hub: Hub): Promise<void>;

  /**
   * Find a hub by its ID
   */
  findById(id: string): Promise<Hub | null>;

  /**
   * Find a hub by its name (case-insensitive)
   */
  findByName(name: string): Promise<Hub | null>;

  /**
   * Find all hubs owned by a specific user
   */
  findByOwnerId(ownerId: string): Promise<Hub[]>;

  /**
   * Find hubs that a user moderates (including owned hubs)
   */
  findModeratedByUserId(userId: string, roles?: string[]): Promise<Hub[]>;

  /**
   * Find hubs by name pattern with optional filters
   */
  findByNamePattern(
    pattern: string,
    options?: {
      caseSensitive?: boolean;
      searchType?: 'contains' | 'equals' | 'startsWith' | 'endsWith';
      isPrivate?: boolean;
      take?: number;
    }
  ): Promise<Hub[]>;

  /**
   * Find popular public hubs
   */
  findPopular(limit?: number): Promise<Hub[]>;

  /**
   * Delete a hub
   */
  delete(hubId: string): Promise<void>;

  /**
   * Check if a hub name exists
   */
  existsByName(name: string): Promise<boolean>;

  /**
   * Get hub count for a user
   */
  countByOwnerId(ownerId: string): Promise<number>;

  /**
   * Find hubs with connection and activity filters
   */
  findWithFilters(filters: {
    hasConnections?: boolean;
    lastActiveAfter?: Date;
    isPrivate?: boolean;
    skip?: number;
    take?: number;
  }): Promise<Hub[]>;
}
