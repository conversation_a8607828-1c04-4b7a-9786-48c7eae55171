/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import type { HubLogConfig, HubLogConfigCreationData, HubLogConfigUpdateData } from '../entities/HubLogConfig.js';

/**
 * Repository interface for Hub Log Configuration operations
 */
export interface HubLogConfigRepository {
  /**
   * Get hub log configuration by hub ID
   */
  getByHubId(hubId: string): Promise<HubLogConfig | null>;

  /**
   * Create a new hub log configuration
   */
  create(data: HubLogConfigCreationData): Promise<HubLogConfig>;

  /**
   * Update an existing hub log configuration
   */
  update(hubId: string, data: HubLogConfigUpdateData): Promise<HubLogConfig>;

  /**
   * Delete hub log configuration
   */
  delete(hubId: string): Promise<void>;

  /**
   * Upsert hub log configuration (create if doesn't exist, update if exists)
   */
  upsert(hubId: string, data: HubLogConfigUpdateData): Promise<HubLogConfig>;
}
