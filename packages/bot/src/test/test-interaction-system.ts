/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { Container } from 'inversify';
import 'reflect-metadata';

import { InteractionRegistry } from '../presentation/interactions/InteractionRegistry.js';
import { DynamicInteractionLoader } from '../infrastructure/loaders/DynamicInteractionLoader.js';
import { BaseInteractionHandler } from '../presentation/interactions/BaseInteractionHandler.js';
import { ClusterEventBus } from '../infrastructure/events/EventBus.js';
import { TYPES } from '../shared/types/TYPES.js';

// Mock interaction handler for testing
class TestInteractionHandler extends BaseInteractionHandler {
  readonly handlerId = 'test_handler';

  async handleButton(): Promise<void> {
    // Test implementation
  }
}

describe('Interaction System', () => {
  let container: Container;
  let interactionRegistry: InteractionRegistry;
  let loader: DynamicInteractionLoader;

  beforeEach(() => {
    container = new Container();

    // Bind required services
    container.bind(TYPES.Container).toConstantValue(container);
    container.bind(TYPES.EventBus).toConstantValue(new ClusterEventBus('0'));
    container.bind(TYPES.InteractionRegistry).to(InteractionRegistry).inSingletonScope();
    container.bind(TYPES.DynamicInteractionLoader).to(DynamicInteractionLoader).inSingletonScope();

    interactionRegistry = container.get<InteractionRegistry>(TYPES.InteractionRegistry);
    loader = container.get<DynamicInteractionLoader>(TYPES.DynamicInteractionLoader);
  });

  describe('InteractionRegistry', () => {
    it('should register interaction handlers', () => {
      const handler = new TestInteractionHandler();
      interactionRegistry.register(handler);

      const handlers = interactionRegistry.getHandlers();
      expect(handlers.size).toBe(1);
      expect(handlers.has('test_handler')).toBe(true);
    });

    it('should find handlers by custom ID', () => {
      const handler = new TestInteractionHandler();
      interactionRegistry.register(handler);

      // Test exact match
      const found = interactionRegistry.findHandler('test_handler');
      expect(found).toBe(handler);

      // Test prefix match (should work with our BaseInteractionHandler.canHandle)
      const foundPrefix = interactionRegistry.findHandler('test_handler:param1');
      expect(foundPrefix).toBe(handler);
    });
  });

  describe('DynamicInteractionLoader', () => {
    it('should have correct diagnostic structure', () => {
      const diagnostics = loader.getDiagnostics();

      expect(diagnostics).toHaveProperty('totalHandlers');
      expect(diagnostics).toHaveProperty('handlerIds');
      expect(Array.isArray(diagnostics.handlerIds)).toBe(true);
      expect(typeof diagnostics.totalHandlers).toBe('number');
    });
  });

  describe('BaseInteractionHandler', () => {
    it('should handle custom ID parameter extraction', () => {
      const handler = new TestInteractionHandler();

      // Test parameter extraction
      const params = handler.extractParams('test_handler:param1:param2');
      expect(params).toEqual(['param1', 'param2']);

      // Test custom ID building
      const customId = handler.buildCustomId('param1', 'param2');
      expect(customId).toBe('test_handler:param1:param2');
    });

    it('should handle canHandle logic correctly', () => {
      const handler = new TestInteractionHandler();

      expect(handler.canHandle('test_handler')).toBe(true);
      expect(handler.canHandle('test_handler:param')).toBe(true);
      expect(handler.canHandle('other_handler')).toBe(false);
    });
  });

  describe('Integration', () => {
    it('should work together for basic interaction routing', () => {
      const handler = new TestInteractionHandler();
      interactionRegistry.register(handler);

      const diagnostics = loader.getDiagnostics();
      const registryHandlers = interactionRegistry.getHandlers();

      // Verify both systems see each other's state
      expect(registryHandlers.size).toBeGreaterThanOrEqual(0);
      expect(typeof diagnostics.totalHandlers).toBe('number');
    });
  });
});

console.log('✅ Interaction system test created successfully!');
