/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import lz from 'lz-string';

/**
 * Parsed custom ID interface for both legacy and modern systems
 */
export interface ParsedCustomId {
  prefix: string;
  suffix?: string;
  expiry?: number;
  args: string[];
}

/**
 * Configuration options for CustomID generation
 */
export interface CustomIdOptions {
  /** Whether to force compression even for short custom IDs */
  forceCompression?: boolean;
  /** Maximum length before compression is applied (default: 45) */
  compressionThreshold?: number;
  /** Expiry date for the custom ID */
  expiry?: Date;
}

/**
 * This class provides compressed custom ID generation to stay under Discord's
 * 100-character limit.
 *
 * Features:
 * - Automatic compression when needed
 * - Integration with BaseInteractionHandler
 * - Support for expiry dates
 * - Validation and error handling
 */
export class CustomID {
  private handlerId: string;
  private parameters: string[];
  private options: CustomIdOptions;

  constructor(handlerId: string, parameters: string[] = [], options: CustomIdOptions = {}) {
    this.handlerId = handlerId;
    this.parameters = parameters;
    this.options = {
      forceCompression: false,
      compressionThreshold: 45,
      ...options,
    };

    this.validateInputs();
  }

  /**
   * Create a CustomID from handler ID and parameters (modern system style)
   */
  static fromHandler(handlerId: string, ...params: string[]): CustomID {
    return new CustomID(handlerId, params);
  }

  /**
   * Create a CustomID with compression options
   */
  static withOptions(handlerId: string, params: string[], options: CustomIdOptions): CustomID {
    return new CustomID(handlerId, params, options);
  }

  /**
   * Add parameters to the custom ID
   */
  addParams(...params: string[]): this {
    this.parameters.push(...params.filter((p) => p.length > 0));
    return this;
  }

  /**
   * Set expiry date for the custom ID
   */
  setExpiry(date: Date): this {
    this.options.expiry = date;
    return this;
  }

  /**
   * Generate the final custom ID string
   */
  toString(): string {
    const customId = this.buildRawCustomId();

    // Check if compression is needed
    const needsCompression =
      this.options.forceCompression || customId.length > (this.options.compressionThreshold || 45);

    if (needsCompression) {
      const compressed = this.compressCustomId(customId);

      // Validate final length
      if (compressed.length > 100) {
        throw new Error(
          `Custom ID too long even after compression: ${compressed.length} chars. ` +
            `Original: "${customId}"`,
        );
      }

      return compressed;
    }

    return customId;
  }

  /**
   * Parse a custom ID string (handles both compressed and uncompressed)
   */
  static parse(customIdString: string): ParsedCustomId {
    // Try to decompress first (legacy system compatibility)
    const decompressed = lz.decompressFromUTF16(customIdString);
    const rawCustomId = decompressed || customIdString;

    return this.parseRawCustomId(rawCustomId);
  }

  /**
   * Check if a custom ID is compressed
   */
  static isCompressed(customIdString: string): boolean {
    try {
      const decompressed = lz.decompressFromUTF16(customIdString);
      return decompressed !== null && decompressed !== customIdString;
    }
    catch {
      return false;
    }
  }

  getCompressionRatio(): { original: number; compressed: number; ratio: number } {
    const original = this.buildRawCustomId();
    const compressed = this.compressCustomId(original);

    return {
      original: original.length,
      compressed: compressed.length,
      ratio: compressed.length / original.length,
    };
  }

  /**
   * Validate inputs to prevent common errors
   */
  private validateInputs(): void {
    if (!this.handlerId || this.handlerId.trim().length === 0) {
      throw new Error('Handler ID cannot be empty');
    }

    // Check for invalid characters that could break parsing
    const invalidChars = ['&'];
    for (const param of this.parameters) {
      if (invalidChars.some((char) => param.includes(char))) {
        throw new Error(`Parameter cannot contain invalid characters: ${invalidChars.join(', ')}`);
      }
    }

    if (this.handlerId.includes('&')) {
      throw new Error('Handler ID cannot contain "&" character');
    }
  }

  /**
   * Build the raw uncompressed custom ID
   */
  private buildRawCustomId(): string {
    let customId = this.handlerId;

    // Add expiry if specified
    if (this.options.expiry) {
      this.parameters.push(`ex=${this.options.expiry.getTime()}`);
    }

    // Add parameters
    if (this.parameters.length > 0) {
      customId = `${customId}&${this.parameters.join('&')}`;
    }

    return customId;
  }

  /**
   * Compress a custom ID using lz-string
   */
  private compressCustomId(rawCustomId: string): string {
    const compressed = lz.compressToUTF16(rawCustomId);
    if (!compressed) {
      throw new Error('Failed to compress custom ID');
    }
    return compressed;
  }

  /**
   * Parse raw (uncompressed) custom ID
   */
  private static parseRawCustomId(rawCustomId: string): ParsedCustomId {
    const parts = rawCustomId.split('&');
    const handlerPart = parts[0];
    const args = parts.slice(1);

    // Split handler part into prefix and suffix
    const colonIndex = handlerPart.indexOf(':');
    const prefix = colonIndex === -1 ? handlerPart : handlerPart.substring(0, colonIndex);
    const suffix = colonIndex === -1 ? undefined : handlerPart.substring(colonIndex + 1);

    // Extract expiry from arguments
    const expiryArg = args.find((arg) => arg.startsWith('ex='));
    const expiry = expiryArg ? parseInt(expiryArg.replace('ex=', ''), 10) : undefined;

    // Filter out expiry arguments
    const filteredArgs = args.filter((arg) => !arg.startsWith('ex='));

    return {
      prefix,
      suffix,
      expiry,
      args: filteredArgs,
    };
  }
}
