/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import {
  ActionRowBuilder,
  ButtonBuilder,
  ButtonInteraction,
  ButtonStyle,
  ComponentType,
  EmbedBuilder,
  InteractionCollector,
  MessageActionRowComponentBuilder,
  MessageCollector,
  MessageFlags,
} from 'discord.js';
import { injectable } from 'inversify';
import type { Context } from '../context/Context.js';
import { Logger } from '../utils/Logger.js';

/**
 * Pagination content generator function type
 * This function is called to generate content for each page
 */
export type PageContentGenerator<T = unknown> = (
  pageIndex: number,
  itemsOnPage: T[],
  totalPages: number,
  totalItems: number,
) => Promise<{
  embeds?: EmbedBuilder[];
  content?: string;
}>;

/**
 * Configuration options for the pagination manager
 */
export interface PaginationOptions<T = unknown> {
  /** Items to paginate */
  items: T[];
  /** Number of items per page */
  itemsPerPage: number;
  /** Function to generate page content */
  contentGenerator: PageContentGenerator<T>;
  /** Pagination timeout in milliseconds (default: 300000 - 5 minutes) */
  timeout?: number;
  /** Whether to show page info (default: true) */
  showPageInfo?: boolean;
  /** Custom button labels */
  buttonLabels?: {
    first?: string;
    previous?: string;
    next?: string;
    last?: string;
    stop?: string;
  };
  /** Whether the pagination should be ephemeral (default: false) */
  ephemeral?: boolean;
}

@injectable()
export class PaginationManager<T = unknown> {
  private currentPage = 0;
  private totalPages = 0;
  private collector?: InteractionCollector<ButtonInteraction> | MessageCollector;
  private isActive = false;

  constructor(private readonly options: PaginationOptions<T>) {
    this.totalPages = Math.ceil(options.items.length / options.itemsPerPage);
  }

  /**
   * Start the pagination interaction
   */
  async start(ctx: Context): Promise<void> {
    if (this.options.items.length === 0) {
      await ctx.reply({
        content: '❌ No items to display.',
        flags: this.options.ephemeral ? [MessageFlags.Ephemeral] : [],
      });
      return;
    }

    if (this.totalPages <= 1) {
      // If only one page, show without pagination controls
      const content = await this.generatePageContent(0);
      await ctx.reply({
        ...content,
        flags: this.options.ephemeral ? [MessageFlags.Ephemeral] : [],
      });
      return;
    }

    // Start with first page
    const initialContent = await this.generatePageContent(0);
    const components = this.createPaginationButtons();

    await ctx.reply({
      ...initialContent,
      components,
      flags: this.options.ephemeral ? [MessageFlags.Ephemeral] : [],
    });

    await this.setupCollector(ctx);
  }

  /**
   * Generate content for a specific page
   */
  private async generatePageContent(pageIndex: number): Promise<{
    embeds?: EmbedBuilder[];
    content?: string;
  }> {
    const startIndex = pageIndex * this.options.itemsPerPage;
    const endIndex = Math.min(startIndex + this.options.itemsPerPage, this.options.items.length);
    const itemsOnPage = this.options.items.slice(startIndex, endIndex);

    const content = await this.options.contentGenerator(
      pageIndex,
      itemsOnPage,
      this.totalPages,
      this.options.items.length,
    );

    // Add page info to first embed if enabled
    if (this.options.showPageInfo !== false && content.embeds && content.embeds.length > 0) {
      const embed = content.embeds[0];
      const currentFooter = embed.data.footer?.text || '';
      const pageInfo = `Page ${pageIndex + 1}/${this.totalPages}`;
      const separator = currentFooter ? ' • ' : '';

      embed.setFooter({
        text: `${currentFooter}${separator}${pageInfo}`,
        iconURL: embed.data.footer?.icon_url,
      });
    }

    return content;
  }

  /**
   * Create pagination button components
   */
  private createPaginationButtons(): ActionRowBuilder<MessageActionRowComponentBuilder>[] {
    const labels = this.options.buttonLabels || {};

    const row = new ActionRowBuilder<MessageActionRowComponentBuilder>().addComponents(
      new ButtonBuilder()
        .setCustomId('pagination_first')
        .setLabel(labels.first || '⏪')
        .setStyle(ButtonStyle.Secondary)
        .setDisabled(this.currentPage === 0),
      new ButtonBuilder()
        .setCustomId('pagination_previous')
        .setLabel(labels.previous || '◀️')
        .setStyle(ButtonStyle.Secondary)
        .setDisabled(this.currentPage === 0),
      new ButtonBuilder()
        .setCustomId('pagination_stop')
        .setLabel(labels.stop || '⏹️')
        .setStyle(ButtonStyle.Danger),
      new ButtonBuilder()
        .setCustomId('pagination_next')
        .setLabel(labels.next || '▶️')
        .setStyle(ButtonStyle.Secondary)
        .setDisabled(this.currentPage === this.totalPages - 1),
      new ButtonBuilder()
        .setCustomId('pagination_last')
        .setLabel(labels.last || '⏩')
        .setStyle(ButtonStyle.Secondary)
        .setDisabled(this.currentPage === this.totalPages - 1),
    );

    return [row];
  }

  /**
   * Setup the button interaction collector
   */
  private async setupCollector(ctx: Context): Promise<void> {
    try {
      const message = await ctx.fetchReply();

      this.collector = message?.createMessageComponentCollector({
        componentType: ComponentType.Button,
        time: this.options.timeout || 300000, // 5 minutes
        filter: (interaction: ButtonInteraction) =>
          interaction.user.id === ctx.user.id && interaction.customId.startsWith('pagination_'),
      });

      this.isActive = true;

      this.collector?.on('collect', async (interaction: ButtonInteraction) => {
        try {
          await this.handleButtonInteraction(interaction);
        }
        catch (error) {
          Logger.error('Error handling pagination button:', error);
          await interaction
            .reply({
              content: '❌ An error occurred while processing your request.',
              flags: [MessageFlags.Ephemeral],
            })
            .catch(() => null);
        }
      });

      this.collector?.on('end', () => {
        this.cleanup();
      });
    }
    catch (error) {
      Logger.error('Error setting up pagination collector:', error);
      this.cleanup();
    }
  }

  /**
   * Handle button interactions
   */
  private async handleButtonInteraction(interaction: ButtonInteraction): Promise<void> {
    const action = interaction.customId.replace('pagination_', '');

    switch (action) {
      case 'first':
        this.currentPage = 0;
        break;
      case 'previous':
        this.currentPage = Math.max(0, this.currentPage - 1);
        break;
      case 'next':
        this.currentPage = Math.min(this.totalPages - 1, this.currentPage + 1);
        break;
      case 'last':
        this.currentPage = this.totalPages - 1;
        break;
      case 'stop':
        this.cleanup();
        await interaction.update({
          components: [],
        });
        return;
    }

    const content = await this.generatePageContent(this.currentPage);
    const components = this.createPaginationButtons();

    await interaction.update({
      ...content,
      components,
    });
  }

  /**
   * Cleanup the pagination manager
   */
  private cleanup(): void {
    if (this.collector && !this.collector.ended) {
      this.collector.stop();
    }
    this.isActive = false;
  }

  /**
   * Check if pagination is currently active
   */
  public get active(): boolean {
    return this.isActive;
  }

  /**
   * Get current page number (0-indexed)
   */
  public get page(): number {
    return this.currentPage;
  }

  /**
   * Get total number of pages
   */
  public get pages(): number {
    return this.totalPages;
  }

  /**
   * Stop the pagination manually
   */
  public stop(): void {
    this.cleanup();
  }
}

/**
 * Create a simple paginated embed list
 */
export async function createPaginatedEmbedList<T>(
  items: T[],
  formatter: (item: T, index: number) => string,
  options: {
    title: string;
    description?: string;
    itemsPerPage?: number;
    color?: number;
    thumbnail?: string;
  } = { title: 'Items' },
): Promise<PaginationOptions<T>> {
  const itemsPerPage = options.itemsPerPage || 10;

  return {
    items,
    itemsPerPage,
    contentGenerator: async (pageIndex, itemsOnPage, _totalPages, totalItems) => {
      const embed = new EmbedBuilder()
        .setTitle(options.title)
        .setColor(options.color || 0x3498db)
        .setTimestamp();

      if (options.description) {
        embed.setDescription(options.description);
      }

      if (options.thumbnail) {
        embed.setThumbnail(options.thumbnail);
      }

      if (itemsOnPage.length > 0) {
        const startIndex = pageIndex * itemsPerPage;
        const itemList = itemsOnPage
          .map((item, index) => formatter(item, startIndex + index))
          .join('\n');

        embed.addFields({
          name: `Items ${startIndex + 1}-${startIndex + itemsOnPage.length} of ${totalItems}`,
          value: itemList || 'No items to display',
        });
      }
      else {
        embed.setDescription('No items to display');
      }

      return { embeds: [embed] };
    },
  };
}
