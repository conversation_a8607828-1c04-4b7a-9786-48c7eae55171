/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import {
  APIActionRowComponent,
  APIInteractionGuildMember,
  APIComponentInMessageActionRow,
  APIModalInteractionResponseCallbackData,
  ActionRowData,
  BitFieldResolvable,
  ChatInputCommandInteraction,
  ContextMenuCommandInteraction,
  Guild,
  GuildMember,
  InteractionEditReplyOptions,
  InteractionReplyOptions,
  InteractionResponse,
  JSONEncodable,
  Message,
  MessageActionRowComponentBuilder,
  MessageActionRowComponentData,
  MessageContextMenuCommandInteraction,
  MessageComponentInteraction,
  MessageEditOptions,
  MessageFlags,
  MessageFlagsString,
  MessagePayload,
  MessageReplyOptions,
  ModalComponentData,
  ModalSubmitInteraction,
  UserContextMenuCommandInteraction,
  CacheType,
  Client,
  EmbedBuilder,
} from 'discord.js';
import { ContextOptions } from './ContextOptions.js';
import { Logger } from '../utils/Logger.js';

/**
 * Base interface for all context interactions
 */
export interface BaseContextInteraction {
  channel: Message['channel'] | null;
  channelId: string | null;
  guild: Guild | null;
  guildId: string | null;
  client: Client;
  member: GuildMember | APIInteractionGuildMember | null;
}

/**
 * Valid interaction types that can be used with Context
 */
export type ValidContextInteractions<C extends CacheType = CacheType> =
  | Message<C extends 'cached' ? true : false>
  | ChatInputCommandInteraction<C>
  | ContextMenuCommandInteraction<C>
  | MessageComponentInteraction<C>
  | ModalSubmitInteraction<C>;

/**
 * Base context type definition
 */
export interface ContextT<T = ValidContextInteractions, R = Message | InteractionResponse> {
  interaction: T;
  responseType: R;
}

/**
 * Translation keys interface (placeholder - replace with actual translation system)
 */
export interface TranslationKeys {
  [key: string]: string;
}

/**
 * Supported locale codes (placeholder - replace with actual locale system)
 */
export type SupportedLocaleCodes =
  | 'en'
  | 'es'
  | 'fr'
  | 'de'
  | 'it'
  | 'pt'
  | 'ru'
  | 'ja'
  | 'ko'
  | 'zh';

/**
 * Abstract base class for all context types
 */
export abstract class Context<T extends ContextT = ContextT> {
  public readonly interaction: T['interaction'];
  protected readonly _options: ContextOptions;

  /**
   * Whether the interaction has been deferred
   */
  abstract get deferred(): boolean;

  /**
   * Whether the interaction has been replied to
   */
  abstract get replied(): boolean;

  /**
   * Create a new Context instance
   * @param interaction The Discord.js interaction object
   */
  constructor(interaction: T['interaction']) {
    this.interaction = interaction;
    this._options = new ContextOptions(this);
  }

  /**
   * Get the options for this context
   */
  public get options() {
    return this._options;
  }

  /**
   * Get the channel for this interaction
   */
  public get channel() {
    return this.interaction.channel;
  }

  /**
   * Get the channel ID for this interaction
   */
  public get channelId() {
    return this.interaction.channelId;
  }

  /**
   * Get the guild for this interaction
   * Returns Guild if in a cached guild, otherwise Guild | null
   *
   * Use inGuild() type guard before accessing this property to ensure non-null access
   */
  public get guild() {
    return this.interaction.guild;
  }

  /**
   * Get the guild ID for this interaction
   * Returns string if in a cached guild, otherwise string | null
   *
   * Use inGuild() type guard before accessing this property to ensure non-null access
   */
  public get guildId() {
    return this.interaction.guildId;
  }

  /**
   * Get the user for this interaction
   */
  public get user() {
    return this.interaction instanceof Message ? this.interaction.author : this.interaction.user;
  }

  /**
   * Get the user ID for this interaction - unified access for both Message and Interaction
   * This method provides consistent user ID access for cooldown systems and other features
   */
  public get userId(): string {
    return this.interaction instanceof Message
      ? this.interaction.author.id
      : this.interaction.user.id;
  }

  /**
   * Get the member for this interaction
   * Returns GuildMember if in a cached guild, otherwise null
   */
  public get member() {
    return this.interaction.member;
  }

  /**
   * Get the client for this interaction
   */
  public get client() {
    return this.interaction.client;
  }

  /**
   * Type guard to check if this interaction is in a guild
   * When this returns true, guild, guildId, and member properties are guaranteed to be non-null
   * @returns Type predicate that narrows guild, guildId, and member to non-null values
   */
  public inGuild(): this is Context<T> & {
    guild: Guild;
    guildId: string;
    member: GuildMember;
    channelId: string;
  } {
    if (this.interaction instanceof Message) return this.interaction.inGuild();
    return this.interaction.inCachedGuild();
  }

  /**
   * Get an emoji by name (placeholder - implement emoji system)
   * @param name The emoji name
   * @returns The emoji string or empty string if client is not ready
   */
  public getEmoji(_name: string): string {
    if (!this.client?.isReady()) return '';
    // TODO: Implement emoji system
    return '';
  }

  /**
   * Get the locale for the current user (placeholder - implement locale system)
   * @returns The user's locale
   */
  public async getLocale(): Promise<SupportedLocaleCodes> {
    // TODO: Implement locale fetching
    return 'en';
  }

  /**
   * Get the target message ID for this interaction
   * @param name The option name containing the message ID or link
   */
  public getTargetMessageId(name: string | null): string | null {
    try {
      if (this.interaction instanceof MessageContextMenuCommandInteraction) {
        return this.interaction.targetId;
      }

      if (this.interaction instanceof Message && this.interaction.reference) {
        return this.interaction.reference.messageId ?? null;
      }

      if (!name) return null;

      const value = this.options.getString(name);
      if (!value) return null;

      // TODO: Implement extractMessageId utility
      return null;
    }
    catch (error) {
      Logger.error('Error getting target message ID:', error);
      return null;
    }
  }

  /**
   * Get the target user for this interaction
   * @param name The option name containing the user mention or ID
   */
  public async getTargetUser(name?: string) {
    try {
      if (this.interaction instanceof UserContextMenuCommandInteraction) {
        return this.interaction.targetId;
      }

      if (!name) return null;

      return await this.options.getUser(name);
    }
    catch (error) {
      Logger.error('Error getting target user:', error);
      return null;
    }
  }

  /**
   * Get the target message for this interaction
   * @param name The option name containing the message ID or link
   */
  public async getTargetMessage(name: string | null): Promise<Message | null> {
    try {
      if (this.interaction instanceof MessageContextMenuCommandInteraction) {
        return this.interaction.targetMessage;
      }

      const targetMessageId = this.getTargetMessageId(name);
      if (!targetMessageId || !this.interaction.channel) return null;

      return await this.interaction.channel.messages.fetch(targetMessageId).catch(() => null);
    }
    catch (error) {
      Logger.error('Error getting target message:', error);
      return null;
    }
  }

  /**
   * Reply with an embed
   * @param desc The description or translation key
   * @param opts Additional options for the embed
   */
  async replyEmbed<K extends keyof TranslationKeys>(
    desc: K | (string & NonNullable<unknown>),
    opts?: {
      t?: { [Key in TranslationKeys[K]]: string };
      content?: string;
      title?: string;
      components?: readonly (
        | JSONEncodable<APIActionRowComponent<APIComponentInMessageActionRow>>
        | ActionRowData<MessageActionRowComponentData | MessageActionRowComponentBuilder>
        | APIActionRowComponent<APIComponentInMessageActionRow>
      )[];
      flags?: BitFieldResolvable<
        Extract<
          MessageFlagsString,
          'Ephemeral' | 'SuppressEmbeds' | 'SuppressNotifications' | 'IsComponentsV2'
        >,
        | MessageFlags.Ephemeral
        | MessageFlags.SuppressEmbeds
        | MessageFlags.SuppressNotifications
        | MessageFlags.IsComponentsV2
      >;
      edit?: boolean;
    },
  ): Promise<T['responseType'] | null> {
    try {
      const _locale = await this.getLocale();
      // TODO: Implement translation system
      const description = desc as string;

      const embed = new EmbedBuilder()
        .setDescription(description)
        .setTitle(opts?.title || null)
        .setColor(0x3498db)
        .setTimestamp();

      const message = { content: opts?.content, embeds: [embed], components: opts?.components };

      if (opts?.edit) {
        return await this.editOrReply({ ...message, content: message.content });
      }
      return await this.reply({ ...message, flags: opts?.flags });
    }
    catch (error) {
      Logger.error('Error replying with embed:', error);
      return null;
    }
  }

  /**
   * Edit the reply if already replied, otherwise send a new reply
   * @param data The data for the reply
   * @param flags Message flags to apply
   */
  async editOrReply(
    data: string | Omit<MessageEditOptions, 'flags'> | Omit<InteractionEditReplyOptions, 'flags'>,
    flags: Extract<
      MessageFlagsString,
      'Ephemeral' | 'SuppressEmbeds' | 'IsComponentsV2' | 'SuppressNotifications'
    >[] = [],
  ): Promise<T['responseType'] | null> {
    try {
      const data_ = typeof data === 'string' ? { content: data } : { ...data };

      if (this.deferred || this.replied) {
        const supportedFlags = ['SuppressEmbeds', 'IsComponentsV2'] as const;
        return await this.editReply({
          ...data_,
          flags: supportedFlags.filter((flag) => flags.includes(flag)),
        });
      }

      if (typeof data === 'string') {
        return await this.reply(data);
      }
      else {
        return await this.reply({
          ...data,
          // @ts-expect-error some weird typeerror in djs
          flags,
        });
      }
    }
    catch (error) {
      Logger.error('Error in editOrReply:', error);
      return null;
    }
  }

  /**
   * Defer the reply to this interaction
   * @param opts Options for deferring
   */
  abstract deferReply(opts?: { flags?: ['Ephemeral'] }): Promise<T['responseType'] | null>;

  /**
   * Edit the reply to this interaction
   * @param data The new data for the reply
   */
  abstract editReply(
    data: string | MessageEditOptions | InteractionEditReplyOptions,
  ): Promise<T['responseType'] | null>;

  /**
   * Reply to this interaction
   * @param data The data for the reply
   */
  abstract reply(
    data:
      | string
      | MessagePayload
      | MessageReplyOptions
      | InteractionReplyOptions
      | MessageEditOptions,
  ): Promise<T['responseType']>;

  /**
   * Delete the reply to this interaction
   */
  abstract deleteReply(): Promise<void>;

  /**
   * Show a modal for this interaction
   * @param data The modal data
   */
  abstract showModal(
    data:
      | JSONEncodable<APIModalInteractionResponseCallbackData>
      | ModalComponentData
      | APIModalInteractionResponseCallbackData,
  ): Promise<void>;
}
