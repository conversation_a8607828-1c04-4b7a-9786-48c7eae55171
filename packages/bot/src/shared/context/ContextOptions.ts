/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import {
  ChatInputCommandInteraction,
  User,
  Attachment,
} from 'discord.js';
import type { Context } from './Context.js';

/**
 * Custom error class for option retrieval errors
 */
export class OptionError extends Error {
  constructor(
    message: string,
    public readonly optionName: string,
  ) {
    super(message);
    this.name = 'OptionError';
  }
}

/**
 * Context options handler for retrieving command options from interactions
 */
export class ContextOptions {
  constructor(private readonly context: Context) {}

  /**
   * Get a string option
   */
  getString(name: string): string | null {
    const interaction = this.context.interaction;

    if (interaction instanceof ChatInputCommandInteraction) {
      const value = interaction.options.getString(name);
      return value;
    }

    // For prefix commands, we'll need to implement argument parsing
    // This is a simplified version - the full implementation would be more complex
    return null;
  }

  /**
   * Get an integer option
   */
  getInteger(name: string): number | null {
    const interaction = this.context.interaction;

    if (interaction instanceof ChatInputCommandInteraction) {
      const value = interaction.options.getInteger(name);
      return value;
    }

    return null;
  }

  /**
   * Get a number option
   */
  getNumber(name: string): number | null {
    const interaction = this.context.interaction;

    if (interaction instanceof ChatInputCommandInteraction) {
      const value = interaction.options.getNumber(name);
      return value;
    }

    return null;
  }

  /**
   * Get a boolean option
   */
  getBoolean(name: string): boolean {
    const interaction = this.context.interaction;

    if (interaction instanceof ChatInputCommandInteraction) {
      const value = interaction.options.getBoolean(name);
      return value ?? false; // Default to false if not provided;
    }

    return false;
  }

  /**
   * Get a user option
   */
  async getUser(name: string): Promise<User | null> {
    const interaction = this.context.interaction;

    if (interaction instanceof ChatInputCommandInteraction) {
      const value = interaction.options.getUser(name);
      return value;
    }

    return null;
  }

  /**
   * Get a member option
   */
  getMember(name: string) {
    const interaction = this.context.interaction;

    if (interaction instanceof ChatInputCommandInteraction) {
      const value = interaction.options.getMember(name);
      return value;
    }

    return null;
  }

  /**
   * Get a channel option
   */
  getChannel(name: string) {
    const interaction = this.context.interaction;

    if (interaction instanceof ChatInputCommandInteraction) {
      const value = interaction.options.getChannel(name);
      return value;
    }

    return null;
  }

  /**
   * Get a role option
   */
  getRole(name: string) {
    const interaction = this.context.interaction;

    if (interaction instanceof ChatInputCommandInteraction) {
      const value = interaction.options.getRole(name);
      return value;
    }

    return null;
  }

  /**
   * Get an attachment option
   */
  getAttachment(name: string): Attachment | null {
    const interaction = this.context.interaction;

    if (interaction instanceof ChatInputCommandInteraction) {
      const value = interaction.options.getAttachment(name);
      return value;
    }

    return null;
  }

  /**
   * Get a mentionable option
   */
  getMentionable(name: string) {
    const interaction = this.context.interaction;

    if (interaction instanceof ChatInputCommandInteraction) {
      const value = interaction.options.getMentionable(name);
      return value;
    }

    return null;
  }

  /**
   * Get the subcommand name
   */
  getSubcommand(required?: boolean): string | null {
    const interaction = this.context.interaction;

    if (interaction instanceof ChatInputCommandInteraction) {
      try {
        return interaction.options.getSubcommand(required ?? false);
      }
      catch {
        return null;
      }
    }

    return null;
  }

  /**
   * Get the subcommand group name
   */
  getSubcommandGroup(required?: boolean): string | null {
    const interaction = this.context.interaction;

    if (interaction instanceof ChatInputCommandInteraction) {
      try {
        return interaction.options.getSubcommandGroup(required ?? false);
      }
      catch {
        return null;
      }
    }

    return null;
  }
}
