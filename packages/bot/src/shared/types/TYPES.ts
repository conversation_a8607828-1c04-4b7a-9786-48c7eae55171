/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

/**
 * Dependency Injection Symbols
 *
 * This file contains all the symbols used for dependency injection
 * throughout the application. Using symbols ensures type safety
 * and prevents naming conflicts.
 */

export const TYPES = {
  // Repositories
  HubRepository: Symbol.for('IHubRepository'),
  HubLogConfigRepository: Symbol.for('IHubLogConfigRepository'),
  UserRepository: Symbol.for('IUserRepository'),
  DonationRepository: Symbol.for('IDonationRepository'),
  UserPremiumRepository: Symbol.for('IUserPremiumRepository'),
  MessageRepository: Symbol.for('IMessageRepository'),
  ServerRepository: Symbol.for('IServerRepository'),
  ConnectionRepository: Symbol.for('IConnectionRepository'),

  // Domain Services
  HubService: Symbol.for('HubService'),
  PremiumService: Symbol.for('PremiumService'),
  DonationManager: Symbol.for('DonationManager'),
  DonationDomainService: Symbol.for('DonationDomainService'),
  UserDomainService: Symbol.for('UserDomainService'),
  ModerationService: Symbol.for('ModerationService'),
  UserService: Symbol.for('UserService'),
  MessageService: Symbol.for('MessageService'),

  // Application Services
  CommandHandler: Symbol.for('CommandHandler'),
  EventHandler: Symbol.for('EventHandler'),
  InteractionHandler: Symbol.for('InteractionHandler'),
  MessageProcessingService: Symbol.for('MessageProcessingService'),
  ContentFilterService: Symbol.for('ContentFilterService'),
  BlacklistService: Symbol.for('BlacklistService'),
  AntiSwearActionService: Symbol.for('AntiSwearActionService'),
  HubLogConfigService: Symbol.for('HubLogConfigService'),
  WebhookService: Symbol.for('WebhookService'),

  // Application Use Cases
  CreateDonationUseCase: Symbol.for('CreateDonationUseCase'),
  GetUserPremiumUseCase: Symbol.for('GetUserPremiumUseCase'),
  CreateUserUseCase: Symbol.for('CreateUserUseCase'),
  GetUserUseCase: Symbol.for('GetUserUseCase'),
  UpdateUserUseCase: Symbol.for('UpdateUserUseCase'),
  ProcessDonationUseCase: Symbol.for('ProcessDonationUseCase'),
  GrantPremiumUseCase: Symbol.for('GrantPremiumUseCase'),
  GetStatisticsUseCase: Symbol.for('GetStatisticsUseCase'),

  // Hub Use Cases
  CreateHubUseCase: Symbol.for('CreateHubUseCase'),
  UpdateHubUseCase: Symbol.for('UpdateHubUseCase'),
  DeleteHubUseCase: Symbol.for('DeleteHubUseCase'),
  GetHubUseCase: Symbol.for('GetHubUseCase'),
  ListHubsUseCase: Symbol.for('ListHubsUseCase'),

  // Infrastructure
  EventBus: Symbol.for('IEventBus'),
  Database: Symbol.for('DatabaseConnection'),
  CacheManager: Symbol.for('ICacheManager'),
  Logger: Symbol.for('ILogger'),
  Configuration: Symbol.for('Configuration'),

  // External Services
  DiscordClient: Symbol.for('DiscordClient'),
  RedisClient: Symbol.for('RedisClient'),
  PrismaClient: Symbol.for('PrismaClient'),

  // Utilities
  Validator: Symbol.for('IValidator'),
  Serializer: Symbol.for('ISerializer'),
  EventEmitter: Symbol.for('IEventEmitter'),
  PaginationManager: Symbol.for('PaginationManager'),

  // Command Handlers (Presentation Layer)
  PresentationCommandHandler: Symbol.for('PresentationCommandHandler'), // Multi-inject symbol for all command handlers
  CommandRegistry: Symbol.for('CommandRegistry'),
  CommandDeploymentService: Symbol.for('CommandDeploymentService'),
  RedisCooldownService: Symbol.for('RedisCooldownService'),
  DynamicCommandLoader: Symbol.for('DynamicCommandLoader'),

  // Interaction Handlers (Presentation Layer)
  InteractionRegistry: Symbol.for('InteractionRegistry'),
  DynamicInteractionLoader: Symbol.for('DynamicInteractionLoader'),
  PresentationInteractionHandler: Symbol.for('PresentationInteractionHandler'), // Multi-inject symbol for all interaction handlers

  // Infrastructure Handlers
  CommandBridge: Symbol.for('CommandBridge'),
  HybridCommandHandler: Symbol.for('HybridCommandHandler'),
  InteractionCreateEventHandler: Symbol.for('InteractionCreateEventHandler'),
  DiscordEventHandler: Symbol.for('DiscordEventHandler'), // Multi-inject symbol for all Discord event handlers

  // Core Services
  Container: Symbol.for('Container'),

  // Event Handlers (Presentation Layer)
  DonationCreatedNotificationHandler: Symbol.for('DonationCreatedNotificationHandler'),
  PremiumGrantedNotificationHandler: Symbol.for('PremiumGrantedNotificationHandler'),
  PremiumExpiredNotificationHandler: Symbol.for('PremiumExpiredNotificationHandler'),
  EventHandlerRegistryService: Symbol.for('EventHandlerRegistryService'),

  // Cluster
  ClusterId: Symbol.for('ClusterId'),

  // Sentry Service
  SentryService: Symbol.for('SentryService'),
} as const;

export type DITypes = typeof TYPES;
