/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import BaseCommand from '#src/core/BaseCommand.js';
import type Context from '#src/core/CommandContext/Context.js';
import type { Connection, Hub } from '#src/generated/prisma/client/client.js';
import { PaginationManager } from '#src/utils/ui/PaginationManager.js';
import { fetchUserLocale } from '#src/utils/Utils.js';
import { type supportedLocaleCodes } from '#src/utils/Locale.js';
import db from '#src/utils/Db.js';
import { getEmoji } from '#src/utils/EmojiUtils.js';
import { CustomID } from '#src/utils/CustomID.js';
import Constants from '#src/utils/Constants.js';
import {
  ContainerBuilder,
  TextDisplayBuilder,
  SectionBuilder,
  ButtonBuilder,
  ButtonStyle,
  MessageFlags,
  SeparatorSpacingSize,
} from 'discord.js';
import { stripIndents } from 'common-tags';

export default class ConnectionsCommand extends BaseCommand {
  constructor() {
    super({
      name: 'connections',
      description: 'Manage all hub connections in this server',
      types: { slash: true, prefix: true },
      contexts: { guildOnly: true },
    });
  }

  async execute(ctx: Context): Promise<void> {
    const connections = await db.connection.findMany({
      where: { serverId: ctx.guild?.id },
      include: { hub: true },
    });

    const locale = await fetchUserLocale(ctx.user.id);

    if (connections.length === 0) {
      const container = new ContainerBuilder();

      // Add header with friendly message
      container.addTextDisplayComponents(
        new TextDisplayBuilder().setContent(
          stripIndents`
          # ${getEmoji('info_icon', ctx.client)} No Connections Found

          This server isn't connected to any hubs yet. Get started by connecting to popular hubs!

          ## Quick Start
          ${getEmoji('arrow_right', ctx.client)} Use \`/setup\` to join your first hub
          ${getEmoji('arrow_right', ctx.client)} Browse popular hubs at [interchat.fun/hubs](${Constants.Links.Website}/hubs)
          `,
        ),
      );

      // Add action buttons
      container.addActionRowComponents((row) => {
        const setupButton = new ButtonBuilder()
          .setCustomId(new CustomID('connections:setup').toString())
          .setLabel('Run Setup')
          .setStyle(ButtonStyle.Primary)
          .setEmoji(getEmoji('wand_icon', ctx.client));

        const browseButton = new ButtonBuilder()
          .setLabel('Browse Hubs')
          .setStyle(ButtonStyle.Link)
          .setURL(`${Constants.Links.Website}/hubs`)
          .setEmoji(getEmoji('globe_icon', ctx.client));

        return row.addComponents(setupButton, browseButton);
      });

      await ctx.reply({
        components: [container],
        flags: [MessageFlags.IsComponentsV2],
      });
      return;
    }

    // Use pagination if there are 5 or more connections
    if (connections.length >= 5) {
      const paginationManager = new PaginationManager({
        client: ctx.client,
        identifier: `connections-${ctx.guild?.id}`,
        items: connections,
        itemsPerPage: 5,
        contentGenerator: (pageIndex, itemsOnPage, totalPages, totalItems) =>
          this.buildConnectionsContainer(
            itemsOnPage,
            locale,
            ctx,
            pageIndex,
            totalPages,
            totalItems,
          ),
      });

      await paginationManager.start(ctx);
    }
    else {
      // Display all connections without pagination
      const container = this.buildConnectionsContainer(
        connections,
        locale,
        ctx,
        0,
        1,
        connections.length,
      );

      await ctx.reply({
        components: [container],
        flags: [MessageFlags.IsComponentsV2],
      });
    }
  }

  private buildConnectionsContainer(
    connections: Array<Connection & { hub: Hub | null }>,
    locale: supportedLocaleCodes,
    ctx: Context,
    pageIndex: number,
    totalPages: number,
    totalItems: number,
  ): ContainerBuilder {
    const container = new ContainerBuilder();

    // Add header
    const headerText =
      totalPages > 1
        ? `## ${getEmoji('connect', ctx.client)} Hub Connections (${pageIndex + 1}/${totalPages})`
        : `## ${getEmoji('connect', ctx.client)} Hub Connections`;

    container.addTextDisplayComponents(
      new TextDisplayBuilder().setContent(
        stripIndents`
        ${headerText}

        Manage your server's connections to InterChat hubs. Use the buttons below to configure each connection.

        **Total Connections:** ${totalItems}
        `,
      ),
    );

    // Add separator
    container.addSeparatorComponents((separator) =>
      separator.setSpacing(SeparatorSpacingSize.Small),
    );

    // Add each connection as a section
    for (const connection of connections) {
      const section = new SectionBuilder();

      const statusEmoji = connection.connected
        ? getEmoji('connect', ctx.client)
        : getEmoji('disconnect', ctx.client);

      const statusText = connection.connected ? 'Connected' : 'Paused';

      const hubName = connection.hub?.name || 'Unknown Hub';
      const channelMention = `<#${connection.channelId}>`;

      // Connection details
      const connectionDetails = stripIndents`
        **Status:** ${statusEmoji} ${statusText}
        **Channel:** ${channelMention}
        **Hub:** ${hubName}
        ${connection.compact ? '**Format:** Compact' : '**Format:** Full'}
        ${connection.profanity ? '**Profanity Filter:** Enabled' : '**Profanity Filter:** Disabled'}
      `;

      section.addTextDisplayComponents(new TextDisplayBuilder().setContent(connectionDetails));

      // Add configure button
      const configButton = new ButtonBuilder()
        .setCustomId(new CustomID('connections:configure').setArgs(connection.channelId).toString())
        .setLabel('Configure')
        .setStyle(ButtonStyle.Secondary)
        .setEmoji(getEmoji('gear_icon', ctx.client));

      section.setButtonAccessory(configButton);
      container.addSectionComponents(section);
    }

    // Add footer with helpful links
    container.addSeparatorComponents((separator) =>
      separator.setSpacing(SeparatorSpacingSize.Small),
    );

    container.addTextDisplayComponents(
      new TextDisplayBuilder().setContent(
        stripIndents`
        ## Need Help?
        ${getEmoji('question_icon', ctx.client)} Visit our [Support Server](${Constants.Links.SupportInvite})
        ${getEmoji('globe_icon', ctx.client)} Browse more hubs at [interchat.fun/hubs](${Constants.Links.Website}/hubs)
        `,
      ),
    );

    return container;
  }
}
