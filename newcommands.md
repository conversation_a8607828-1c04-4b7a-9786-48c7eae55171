# InterChat v5 Command Modernization Plan

## Current Command Analysis

After analyzing the existing legacy commands in `src/commands/`, I've identified the following categories and commands:

### Current Command Structure:

**Information Commands:**
- `about` - Learn about InterChat
- `achievements` - View user achievements
- `donate` - Support development
- `help` - Command help system
- `invite` - Get bot invite
- `profile` - View user profile
- `rules` - View hub rules
- `stats` - Bot statistics
- `support` - Get support
- `tutorial` - Interactive tutorial
- `vote` - Vote for the bot

**Main/Core Commands:**
- `setup` - Setup InterChat in server
- `claim` - Claim rewards
- `connection` - Manage connections (edit, pause, unpause, list)
- `deleteMsg` - Delete messages
- `editMsg` - Edit messages
- `inbox` - View inbox
- `leaderboard` - View leaderboards (messages, calls, votes, achievements)
- `messageInfo` - Get message info
- `modActions` - Moderation panel
- `report` - Report messages

**Hub Management Commands:**
- `connect` - Connect to hub
- `disconnect` - Disconnect from hub
- `warn` - Warn users
- `blacklist` - Blacklist users/servers (user, server, list)
- `unblacklist` - Unblacklist users/servers (user, server)
- `hub` - Main hub management with subcommands:
  - `config` (rules, logging, welcome, settings, anti-swear, set-appeal-cooldown)
  - `invite` (create, revoke, list)
  - `moderator` (add, remove, edit, list)
  - `announce` - Send announcements
  - `create` - Create new hub
  - `delete` - Delete hub
  - `edit` - Edit hub
  - `infractions` - View infractions
  - `servers` - View connected servers
  - `transfer` - Transfer ownership
  - `visibility` - Set hub visibility
  - `set-nsfw` - Set NSFW status
  - `rename` - Rename hub

**Configuration Commands:**
- `badges` - Manage user badges
- `config` - Server configuration (set-invite)
- `set` - User preferences (language, reply_mentions)

**Staff Commands:**
- `badge` - Manage user badges (add, remove)
- `ban` - Ban users/servers
- `bans` - View bans
- `unban` - Unban users/servers
- `find` - Find users/servers (user, server)
- `leave` - Leave server
- `recluster` - Trigger recluster
- `view_reported_call` - View reported calls
- `debug` - Debug commands (fix-server)
- `dev` - Development commands (send-alert)

**Userphone Commands:**
- `call` - Make voice calls
- `hangup` - End calls
- `skip` - Skip current call

---

## Proposed v5 Command Structure

### 🎯 Design Goals:
- **Simplified naming** - Clear, concise command names
- **Logical grouping** - Commands organized by function
- **Reduced complexity** - Fewer top-level commands
- **Better discoverability** - Intuitive command structure
- **Modern UX** - Streamlined user experience

### 📋 New Command Categories:

## 1. **Core Commands** (Essential functionality)

### `/setup`
- **Description**: Setup InterChat in your server
- **Subcommands**:
  - `channel` - Connect an existing channel to a hub
  - `create-hub` - Create a new hub and connect to it
- **Changes**: ✅ Keep the excellent UX - clear, simple, effective

### `/disconnect`
- **Description**: Disconnect from a hub
- **Options**:
  - `hub` (string, autocomplete) - Hub to disconnect from
- **Changes**: Simple disconnect command

### `/connection`
- **Description**: Manage your existing connections
- **Subcommands**:
  - `edit` - Edit connection settings (colors, formatting)
  - `pause` - Temporarily pause a connection
  - `unpause` - Resume a paused connection
  - `list` - View all connections in this server
- **Changes**: Focused on managing existing connections

### `/inbox`
- **Description**: View your message inbox and notifications
- **Changes**: Simplified interface

## 2. **Hub Management** (Hub owners/moderators)

### `/hub`
- **Description**: Manage your hubs
- **Subcommands**:
  - `delete` - Delete your hub
  - `edit` - Edit hub settings (name, description, etc.)
  - `info` - View detailed hub information
  - `list` - List your hubs
  - `logging` - Configure logging settings
  - `transfer` - Transfer hub ownership
  - `visibility` - Set hub visibility (public/private)

### `/moderator`
- **Description**: Manage hub moderators
- **Subcommands**:
  - `add` - Add a moderator
  - `remove` - Remove a moderator
  - `list` - List moderators
  - `permissions` - Edit moderator permissions

### `/invite`
- **Description**: Manage hub invites
- **Subcommands**:
  - `create` - Create hub invite
  - `revoke` - Revoke an invite
  - `list` - List active invites

### `/announce`
- **Description**: Send announcements to hub
- **Options**:
  - `hub` (string, autocomplete) - Target hub
  - `message` (string) - Announcement message

## 3. **Moderation** (Message & user management)

### `/moderate`
- **Description**: Open moderation panel for a message
- **Options**:
  - `message` (string) - Message ID to moderate
- **Changes**: Renamed from `modActions`, clearer purpose

### `/report`
- **Description**: Report a message or user
- **Options**:
  - `message` (string) - Message ID to report
- **Changes**: Streamlined reporting

### `/warn`
- **Description**: Warn a user in a hub
- **Options**:
  - `user` (user) - User to warn
  - `hub` (string, autocomplete) - Hub context
  - `reason` (string) - Warning reason

### `/ban`
- **Description**: Ban a user or server from hub
- **Options**:
  - `target` (string) - User ID or server ID
  - `hub` (string, autocomplete) - Hub to ban from
  - `reason` (string) - Ban reason
  - `duration` (string, choices) - Ban duration

### `/unban`
- **Description**: Remove a ban from hub
- **Options**:
  - `target` (string, autocomplete) - Banned user/server
  - `hub` (string, autocomplete) - Hub to unban from

## 4. **Settings** (User & server preferences)

### `/preferences`
- **Description**: Manage your personal preferences
- **Subcommands**:
  - `language` - Set your language
  - `mentions` - Configure mention settings
  - `notifications` - Notification preferences
- **Changes**: Renamed from `set`, more descriptive

### `/server`
- **Description**: Configure server settings
- **Subcommands**:
  - `invite` - Set server invite link
  - `badges` - Manage server badges
  - `logging` - Configure logging
- **Changes**: Consolidated server configuration

## 5. **Information** (Help & stats)

### `/help`
- **Description**: Get help with InterChat commands
- **Options**:
  - `command` (string, autocomplete, optional) - Specific command help
- **Changes**: ✅ Already well-designed

### `/info`
- **Description**: Get information about InterChat
- **Subcommands**:
  - `bot` - About InterChat
  - `user` - View user profile
  - `message` - Get message information
  - `hub` - View hub information
- **Changes**: Consolidated information commands

### `/stats`
- **Description**: View statistics and leaderboards
- **Subcommands**:
  - `bot` - Bot statistics
  - `user` - User statistics
  - `leaderboard` - Global leaderboards
- **Changes**: Consolidated stats commands

### `/tutorial`
- **Description**: Interactive InterChat tutorial
- **Changes**: ✅ Keep as-is, good onboarding

## 6. **Premium Features** (Enhanced functionality)

### `/premium`
- **Description**: Manage premium features
- **Subcommands**:
  - `status` - Check premium status
  - `features` - View premium features
  - `donate` - Support development
- **Changes**: Consolidated premium/donation commands

### `/customize`
- **Description**: Customize your InterChat experience
- **Subcommands**:
  - `colors` - Set embed colors
  - `format` - Message formatting options
  - `welcome` - Custom welcome messages
- **Changes**: New category for premium customization

## 7. **Staff Commands** (Admin only)

### `/admin`
- **Description**: Administrative commands
- **Subcommands**:
  - `find` - Find users/servers
  - `badge` - Manage badges
  - `debug` - Debug tools
  - `maintenance` - Maintenance commands
- **Changes**: Consolidated admin commands

---

## 🔄 Migration Strategy

### Phase 1: Core Commands (Week 1-2)
- Keep `/setup channel` and `/setup create-hub` (already excellent UX)
- Implement `/connection` for managing existing connections
- Create `/preferences` to replace `/set`
- Update `/moderate` (renamed from `modActions`)

### Phase 2: Hub Management (Week 3-4)
- Modernize `/hub` command structure (remove `create`, add `logging`)
- Implement `/moderator` and `/invite` commands
- Update `/announce` and `/warn`

### Phase 3: Information & Settings (Week 5-6)
- Consolidate into `/info` and `/stats`
- Create `/server` configuration command
- Implement `/premium` and `/customize`

### Phase 4: Advanced Features (Week 7-8)
- Staff `/admin` command consolidation
- Legacy command deprecation
- Documentation and help updates

---

## 🎨 UX Improvements

### Better Autocomplete
- Smart hub suggestions based on user's permissions
- Recent items prioritized
- Fuzzy search support

### Interactive Components
- Rich embeds with action buttons
- Progressive disclosure for complex settings
- Confirmation dialogs for destructive actions

### Contextual Help
- Inline help text in command options
- Examples in command descriptions
- Progressive onboarding flow

### Hub Discovery
- **Dashboard-based hub discovery** - Browse and join hubs through the web dashboard
- **Invite system** - Join hubs through invite codes shared by friends
- **No command-line hub browsing** - Keep it simple and visual

### Error Handling
- Clear error messages with suggestions
- Automatic retry mechanisms
- Graceful degradation

---

## 📊 Command Count Reduction

**Before**: ~50+ commands and subcommands scattered across categories
**After**: ~12 simple commands with interactive interfaces

### Key Philosophy: **Interactive UIs > Subcommands**
- **Instead of** `/connection edit` → **Use** `/connections` (opens panel with edit buttons)
- **Instead of** `/hub create` → **Use** `/setup create-hub` (clearer purpose)
- **Instead of** `/moderator add` → **Use** `/hub` (opens hub panel with moderator section)
- **Instead of** `/info user` → **Use** `/info` with type choice

### Benefits:
- **No more memorizing subcommands** - Everything is visual and clickable
- **Faster navigation** - Click instead of typing long commands
- **Self-discovering** - UI shows what's possible
- **Less confusing** - One command per major function
- **Mobile-friendly** - Buttons work better than typing on mobile

### Key Design Decisions:
- **No complex subcommand trees** - Users hate them
- **Rich interactive interfaces** - Modern Discord UI components
- **Single clear purpose per command** - No ambiguity
- **Dashboard integration** - Complex browsing stays visual

---

## 🚀 New Features to Consider

### Dashboard Hub Discovery
- **Visual hub browsing** - Browse hubs by category, popularity, activity
- **Advanced filtering** - Filter by language, topic, server count
- **Preview system** - See hub activity and rules before joining
- **Recommendation engine** - Suggest hubs based on server interests

### `/quick-actions`
- Fast access to common tasks
- Customizable per-user shortcuts
- Recent actions history

### `/templates`
- Pre-configured hub templates
- Message templates for announcements
- Quick-start configurations

### `/analytics`
- Hub activity insights
- Message statistics
- Engagement metrics

### `/automation`
- Automated moderation rules
- Scheduled announcements
- Event triggers

---

This modernized command structure focuses on user experience, logical organization, and reduced complexity while maintaining all existing functionality. The key insight is keeping the excellent `/setup` UX while moving hub discovery to the dashboard where it belongs - visual, searchable, and much more user-friendly than command-line browsing.
