import { CustomID } from './src/utils/CustomID.js';

// Test various custom ID patterns to measure their lengths
const testCases = [
  // Simple patterns
  { name: 'Simple prefix', customId: new CustomID().setIdentifier('modPanel').toString() },
  { name: 'Prefix with suffix', customId: new CustomID().setIdentifier('modPanel', 'banUser').toString() },

  // Common patterns with message IDs (18-20 characters)
  { name: 'With message ID', customId: new CustomID('modPanel:banUser', ['1234567890123456789']).toString() },
  { name: 'Multiple args', customId: new CustomID('modPanel:banUser', ['1234567890123456789', 'reason']).toString() },

  // Complex patterns found in codebase
  { name: 'Badge removal', customId: new CustomID().setIdentifier('badge_remove', 'confirm').setArgs('1234567890123456789', 'developer').toString() },
  { name: 'Hub blacklist', customId: new CustomID('blacklist_reason_modal', ['hub', '1234567890123456789', '1234567890123456789', '30d']).toString() },
  { name: 'Connection change', customId: new CustomID().setIdentifier('connection', 'change_channel_btn').setArgs('1234567890123456789', '9876543210987654321').toString() },

  // Very long patterns
  { name: 'Long hub name', customId: new CustomID('hub_blacklist_custom', ['1234567890123456789', '9876543210987654321', 'very_long_reason_text_that_could_be_provided_by_user']).toString() },
  { name: 'Complex pagination', customId: new CustomID().setIdentifier('pagination', 'select_page').setArgs('leaderboard', 'messages', 'server', '1234567890123456789').toString() },
];

console.log('=== Custom ID Length Analysis ===\n');
console.log('Discord Limit: 100 characters\n');

testCases.forEach(testCase => {
  const length = testCase.customId.length;
  const status = length > 50 ? '❌ TOO LONG' : length > 30 ? '⚠️  LONG' : '✅ OK';
  console.log(`${status} (${length} chars) ${testCase.name}`);
  console.log(`   ${testCase.customId}\n`);
});

// Test what happens without compression
console.log('=== Without Compression ===\n');
const longCase = new CustomID('hub_blacklist_custom', ['1234567890123456789', '9876543210987654321', 'very_long_reason_text_that_could_be_provided_by_user']);
let customIdStr = longCase.customId;
if (longCase.data.length > 0) {
  for (const element of longCase.data) {
    customIdStr += `&${element}`;
  }
}
console.log(`Uncompressed length: ${customIdStr.length} chars`);
console.log(`Uncompressed: ${customIdStr}`);
