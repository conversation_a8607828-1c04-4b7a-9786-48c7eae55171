"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { AlertTriangle, RefreshCw, Clock } from "lucide-react";
import { useState } from "react";
import { BeginnerFriendlyErrorDisplay } from "@/components/ui/beginner-friendly-error";
import { getBeginnerFriendlyError } from "@/lib/error-messages";

interface ConnectionErrorBoundaryProps {
  error: {
    message: string;
    status?: number;
  };
  onRetry: () => void;
  isRetrying?: boolean;
}

export function ConnectionErrorBoundary({
  error,
  onRetry,
  isRetrying = false,
}: ConnectionErrorBoundaryProps) {
  const [retryCount, setRetryCount] = useState(0);
  const [showLegacyView, setShowLegacyView] = useState(false);

  const handleRetry = () => {
    setRetryCount(prev => prev + 1);
    onRetry();
  };

  // Create an error object that our beginner-friendly system can understand
  const errorForMapping = error.status
    ? new Error(`${error.message} (Status: ${error.status})`)
    : new Error(error.message);

  const friendlyError = getBeginnerFriendlyError(errorForMapping, {
    action: "loading connection",
    component: "ConnectionErrorBoundary"
  });

  // If user wants to see the new beginner-friendly error display
  if (!showLegacyView) {
    return (
      <div className="min-h-[400px] flex items-center justify-center p-4">
        <div className="max-w-2xl w-full">
          <BeginnerFriendlyErrorDisplay
            error={friendlyError}
            onRetry={handleRetry}
            isRetrying={isRetrying}
          />

          {/* Option to switch to legacy view */}
          <div className="mt-4 text-center">
            <Button
              onClick={() => setShowLegacyView(true)}
              variant="ghost"
              size="sm"
              className="text-gray-500 hover:text-gray-300"
            >
              Show technical details
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // Legacy error display for comparison/fallback
  const getErrorMessage = () => {
    if (error.status === 429) {
      return {
        title: "Rate Limit Exceeded",
        description: "We're making too many requests to Discord's API. Please wait a moment before trying again.",
        icon: <Clock className="h-8 w-8 text-yellow-500" />,
        suggestion: "This usually resolves itself within a few minutes. You can try refreshing the page or waiting a bit longer.",
      };
    }

    if (error.status === 403) {
      return {
        title: "Access Denied",
        description: "You don't have permission to access this connection.",
        icon: <AlertTriangle className="h-8 w-8 text-red-500" />,
        suggestion: "You need 'Manage Channels' permission on the Discord server to view/edit connections.",
      };
    }

    if (error.status === 404) {
      return {
        title: "Connection Not Found",
        description: "The connection you're looking for doesn't exist or has been deleted.",
        icon: <AlertTriangle className="h-8 w-8 text-red-500" />,
        suggestion: "The connection may have been removed. Please check your connections list or contact support if you believe this is an error.",
      };
    }

    if (error.message.includes("rate limit") || error.message.includes("429")) {
      return {
        title: "Discord API Rate Limit",
        description: "Discord is temporarily limiting our requests to prevent overload.",
        icon: <Clock className="h-8 w-8 text-yellow-500" />,
        suggestion: "Please wait 1-2 minutes before trying again. This helps keep the service stable for everyone.",
      };
    }

    return {
      title: "Connection Error",
      description: error.message || "An unexpected error occurred while loading the connection.",
      icon: <AlertTriangle className="h-8 w-8 text-red-500" />,
      suggestion: "Please try refreshing the page. If the problem persists, contact support.",
    };
  };

  const errorInfo = getErrorMessage();

  return (
    <div className="min-h-[400px] flex items-center justify-center p-4">
      <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm max-w-md w-full">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            {errorInfo.icon}
          </div>
          <CardTitle className="text-xl">{errorInfo.title}</CardTitle>
          <CardDescription className="text-gray-400">
            {errorInfo.description}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-sm text-gray-500 bg-gray-800/50 p-3 rounded-lg">
            <p className="font-medium mb-2">What can you do?</p>
            <p>{errorInfo.suggestion}</p>
          </div>

          {error.status === 429 && (
            <div className="text-xs text-gray-600 bg-yellow-900/20 border border-yellow-800/30 p-3 rounded-lg">
              <p className="font-medium text-yellow-400 mb-1">Why does this happen?</p>
              <p>Discord limits how often we can fetch server information to keep their service running smoothly. This is normal and temporary.</p>
            </div>
          )}

          <div className="flex flex-col gap-2">
            {error.status !== 403 && error.status !== 404 && (
              <Button
                onClick={handleRetry}
                disabled={isRetrying}
                className="w-full"
                variant={error.status === 429 ? "outline" : "default"}
              >
                {isRetrying ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Retrying...
                  </>
                ) : (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Try Again {retryCount > 0 && `(${retryCount})`}
                  </>
                )}
              </Button>
            )}

            {(error.status === 403 || error.status === 404) && (
              <Button
                onClick={() => window.location.href = "/dashboard"}
                className="w-full"
                variant="outline"
              >
                Back to Dashboard
              </Button>
            )}

            {error.status === 429 && (
              <p className="text-xs text-center text-gray-500">
                Recommended wait time: 1-2 minutes
              </p>
            )}
          </div>

          {retryCount >= 3 && (
            <div className="text-xs text-gray-600 bg-gray-800/50 p-3 rounded-lg border border-gray-700">
              <p className="font-medium mb-1">Still having issues?</p>
              <p>If you&apos;ve tried multiple times and it&apos;s still not working, you can:</p>
              <ul className="list-disc list-inside mt-2 space-y-1">
                <li>Wait a few more minutes and try again</li>
                <li>Use the Discord bot commands as an alternative</li>
                <li>Contact support if the issue persists</li>
              </ul>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Option to switch back to beginner-friendly view */}
      <div className="mt-4 text-center">
        <Button
          onClick={() => setShowLegacyView(false)}
          variant="ghost"
          size="sm"
          className="text-gray-500 hover:text-gray-300"
        >
          ← Back to simplified view
        </Button>
      </div>
    </div>
  );
}
