'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

interface ConnectionData {
  id: string;
  serverId: string;
  connected: boolean;
  compact: boolean;
  createdAt: Date;
  lastActive: Date | null;
  server: {
    id: string;
    name: string | null;
    iconUrl: string | null;
  } | null;
}

interface ConnectionsClientWrapperProps {
  initialConnections: ConnectionData[];
  children: (
    connections: ConnectionData[],
    updateConnections: (updates: ConnectionData[]) => void,
  ) => React.ReactNode;
}

export function ConnectionsClientWrapper({
  initialConnections,
  children,
}: ConnectionsClientWrapperProps) {
  const [connections, setConnections] = useState<ConnectionData[]>(initialConnections);
  const router = useRouter();

  const updateConnections = (updates: ConnectionData[]) => {
    setConnections(updates);

    // Refresh the server data to keep it in sync
    router.refresh();
  };

  // Sync with server data when initialConnections changes
  useEffect(() => {
    setConnections(initialConnections);
  }, [initialConnections]);

  return <>{children(connections, updateConnections)}</>;
}
