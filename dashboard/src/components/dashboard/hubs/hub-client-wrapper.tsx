'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

interface HubData {
  id: string;
  name: string;
  description: string;
  iconUrl: string;
  bannerUrl: string | null;
  private: boolean;
  nsfw: boolean;
  connectionCount: number;
}

interface HubClientWrapperProps {
  initialHub: HubData;
  children: (hub: HubData, updateHub: (updates: Partial<HubData>) => void) => React.ReactNode;
}

export function HubClientWrapper({ initialHub, children }: HubClientWrapperProps) {
  const [hub, setHub] = useState<HubData>(initialHub);
  const router = useRouter();

  const updateHub = (updates: Partial<HubData>) => {
    setHub(prev => ({
      ...prev,
      ...updates
    }));

    // Refresh the server data to keep it in sync
    router.refresh();
  };

  // Sync with server data when initialHub changes
  useEffect(() => {
    setHub(initialHub);
  }, [initialHub]);

  return <>{children(hub, updateHub)}</>;
}
