"use client";

import {
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { motion } from "motion/react";
import { useInView } from "react-intersection-observer";
import { MessageSquare, Plus } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { formatDistanceToNow } from "date-fns";

interface Hub {
  id: string;
  name: string;
  iconUrl: string;
  lastActive: Date;
  connections?: { id: string }[];
}

interface AnimatedActivityCardProps {
  hubs: Hub[];
}

export function AnimatedActivityCard({ hubs }: AnimatedActivityCardProps) {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3,
      },
    },
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0, transition: { duration: 0.5 } },
  };

  return (
    <motion.div
      ref={ref}
      initial="hidden"
      animate={inView ? "show" : "hidden"}
      variants={container}
      className="h-full"
    >
      <Card className="border-gray-800 bg-gradient-to-b from-gray-900/50 to-gray-900/30 backdrop-blur-sm h-full">
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <div>
            <CardTitle className="text-xl font-bold">Recent Activity</CardTitle>
            <CardDescription>Latest events across your hubs</CardDescription>
          </div>
          <Link
            href="/dashboard"
            className="text-xs text-primary hover:underline"
          >
            View all
          </Link>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {hubs.length > 0 ? (
              // Sort hubs by lastActive date
              [...hubs]
                .sort(
                  (a, b) =>
                    new Date(b.lastActive).getTime() -
                    new Date(a.lastActive).getTime(),
                )
                .slice(0, 4)
                .map((hub) => {
                  const timeAgo = formatDistanceToNow(
                    new Date(hub.lastActive),
                    { addSuffix: true },
                  );
                  const connectionCount = hub.connections?.length || 0;

                  return (
                    <motion.div key={hub.id} variants={item}>
                      <Link href={`/dashboard/hubs/${hub.id}`}>
                        <div className="flex items-center space-x-4 rounded-md p-3 hover:bg-gray-800/50 cursor-pointer transition-colors duration-200 border border-gray-800/50 hover:border-gray-700">
                          <div className="relative h-12 w-12 rounded-full bg-gray-800 flex items-center justify-center overflow-hidden">
                            {hub.iconUrl ? (
                              <Image
                                src={hub.iconUrl}
                                alt={hub.name}
                                className="h-full w-full object-cover"
                                width={48}
                                height={48}
                              />
                            ) : (
                              <MessageSquare className="h-5 w-5 text-gray-400" />
                            )}
                            <div className="absolute inset-0 bg-gradient-to-tr from-black/20 to-transparent"></div>
                          </div>
                          <div className="flex-1 space-y-1">
                            <p className="text-base font-medium">{hub.name}</p>
                            <p className="text-sm text-gray-400">
                              {connectionCount} connection
                              {connectionCount !== 1 ? "s" : ""} • Active{" "}
                              {timeAgo}
                            </p>
                          </div>
                        </div>
                      </Link>
                    </motion.div>
                  );
                })
            ) : (
              <motion.div variants={item} className="text-center py-8">
                <MessageSquare className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                <p className="text-lg text-gray-300 mb-4">No hubs yet</p>
                <Link href="/dashboard/hubs/create">
                  <Button size="lg" className="w-full">
                    <Plus className="h-4 w-4 mr-2" />
                    Create Your First Hub
                  </Button>
                </Link>
              </motion.div>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
