"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { Home, Menu, Scale, Settings } from "lucide-react";
import type { User } from "next-auth";
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useState } from "react";
import { UserNav } from "../user-nav";
import { MobileSidebar } from "./mobile-sidebar";
import { NotificationDropdown } from "./notifications/notification-dropdown";
import { OnboardingHelpMenu } from "./onboarding/onboarding-help-menu";

export function DashboardTopBar({ user }: { user: User }) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const pathname = usePathname();

  return (
    <div className="sticky top-0 z-40 flex h-16 flex-shrink-0 border-b border-gray-800/50 bg-gradient-to-r from-gray-900 to-gray-950 backdrop-blur-sm">
      <div className="flex flex-1 items-center px-4">
        {/* Logo and Brand */}
        <div className="flex items-center gap-3">
          <Link href="/dashboard" className="flex items-center gap-3 group">
            <Image
              alt="InterChat"
              src="/interchat.png"
              height={32}
              width={32}
              className="rounded-full border border-gray-700/50 group-hover:border-indigo-500/50 transition-colors"
            />
            <span className="font-bold text-lg bg-clip-text text-transparent bg-gradient-to-r from-indigo-400 to-purple-400 hidden sm:block">
              InterChat
            </span>
          </Link>
        </div>

        {/* Navigation Links - Hidden on mobile */}
        <div className="hidden lg:flex items-center gap-2 ml-8">
          <Link
            href="/dashboard"
            className={cn(
              "flex items-center gap-2 px-3 py-2 text-sm rounded-lg transition-colors",
              pathname === "/dashboard"
                ? "bg-indigo-500/20 text-indigo-400"
                : "text-gray-400 hover:text-white hover:bg-gray-800/50"
            )}
          >
            <Home className="h-4 w-4" />
            Dashboard
          </Link>
          <Link
            href="/dashboard/my-appeals"
            className={cn(
              "flex items-center gap-2 px-3 py-2 text-sm rounded-lg transition-colors",
              pathname.startsWith("/dashboard/my-appeals")
                ? "bg-indigo-500/20 text-indigo-400"
                : "text-gray-400 hover:text-white hover:bg-gray-800/50"
            )}
          >
            <Scale className="h-4 w-4" />
            My Appeals
          </Link>
          <Link
            href="/dashboard/settings"
            className={cn(
              "flex items-center gap-2 px-3 py-2 text-sm rounded-lg transition-colors",
              pathname.startsWith("/dashboard/settings")
                ? "bg-indigo-500/20 text-indigo-400"
                : "text-gray-400 hover:text-white hover:bg-gray-800/50"
            )}
          >
            <Settings className="h-4 w-4" />
            Settings
          </Link>
        </div>

        {/* Mobile menu button */}
        <div className="flex items-center lg:hidden ml-auto">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="text-gray-400 hover:text-white"
          >
            <Menu className="h-6 w-6" />
            <span className="sr-only">Open menu</span>
          </Button>
        </div>

        {/* Right side actions */}
        <div className="hidden lg:flex items-center gap-4 ml-auto">
          {/* Notifications */}
          <div data-tour="notifications">
            <NotificationDropdown />
          </div>

          {/* Help */}
          <OnboardingHelpMenu />

          {/* User dropdown */}
          <div className="flex items-center" data-tour="user-menu">
            <UserNav
              user={user}
              firstPage={{ name: "Home", icon: Home, href: "/" }}
            />
          </div>
        </div>

        {/* Mobile sidebar using portal to render outside the DOM hierarchy */}
        <MobileSidebar
          isOpen={isMobileMenuOpen}
          onClose={() => setIsMobileMenuOpen(false)}
          user={user}
        />
      </div>
    </div>
  );
}
