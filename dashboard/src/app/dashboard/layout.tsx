import { auth } from "@/auth";
import { DashboardLayoutProvider } from "@/components/dashboard/layout-provider";
import { EnhancedOnboardingModal } from "@/components/dashboard/onboarding/enhanced-onboarding-modal";
import { GuidedTourProvider } from "@/components/dashboard/onboarding/guided-tour-provider";
import { OnboardingProvider } from "@/components/dashboard/onboarding/onboarding-provider";
import { DashboardTopBar } from "@/components/dashboard/topbar";
import { getUserHubs } from "@/lib/permissions";
import { db } from "@/lib/prisma";
import { redirect } from "next/navigation";
import type { ReactNode } from "react";
import "./dashboard.css";

export default async function DashboardLayout({
  children,
}: {
  children: ReactNode;
}) {
  const session = await auth();

  // Redirect to login if not authenticated
  if (!session?.user) {
    redirect("/login?callbackUrl=/dashboard");
  }

  // Get user's hubs and connections for onboarding state
  const userHubs = await getUserHubs(session.user.id);
  const connections = await db.connection.count({
    where: {
      hubId: { in: userHubs.map((hub) => hub.id) },
      connected: true,
    },
  });

  return (
    <DashboardLayoutProvider>
      <OnboardingProvider
        userHubCount={userHubs.length}
        userConnectionCount={connections}
      >
        <GuidedTourProvider>
          <div className="min-h-screen bg-[#0a0a0c]">
            {/* Simplified dashboard layout without sidebar */}
            <div className="flex flex-col h-screen overflow-hidden">
              {/* Top bar */}
              <DashboardTopBar user={session.user} />

              {/* Main content area */}
              <main className="flex-1 overflow-y-auto dashboard-scrollbar bg-gradient-to-b from-gray-900 via-gray-900/95 to-gray-950 p-6 relative">
                {/* Background pattern */}
                <div className="fixed inset-0 z-0 opacity-5 pointer-events-none">
                  <div
                    className="absolute inset-0 bg-grid-white bg-[size:30px_30px] [mask-image:radial-gradient(ellipse_80%_80%_at_50%_50%,#000_20%,transparent_120%)]"
                    style={{ zIndex: -1 }}
                  />
                </div>

                {/* Content */}
                <div className="relative z-10">
                  {children}
                </div>
              </main>
            </div>

            {/* Enhanced Onboarding Modal */}
            <EnhancedOnboardingModal />
          </div>
        </GuidedTourProvider>
      </OnboardingProvider>
    </DashboardLayoutProvider>
  );
}
