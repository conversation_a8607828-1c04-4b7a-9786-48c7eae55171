#!/bin/bash

# Script to fix command handlers that need SentryService injection

# Array of files that need to be fixed
files=(
  "src/presentation/commands/config/BadgesCommandHandler.ts"
  "src/presentation/commands/config/SetCommandHandler.ts"
  "src/presentation/commands/config/SetupCommandHandler.ts"
  "src/presentation/commands/connection/DisconnectCommandHandler.ts"
  "src/presentation/commands/donation/CreateDonationCommandHandler.ts"
  "src/presentation/commands/donation/PremiumStatusCommandHandler.ts"
  "src/presentation/commands/hub/HubCreateCommandHandler.ts"
  "src/presentation/commands/hub/HubDeleteCommandHandler.ts"
  "src/presentation/commands/hub/HubEditCommandHandler.ts"
  "src/presentation/commands/hub/HubInfoCommandHandler.ts"
  "src/presentation/commands/hub/HubListCommandHandler.ts"
  "src/presentation/commands/hub/HubLoggingCommandHandler.ts"
  "src/presentation/commands/information/MessageInfoCommandHandler.ts"
  "src/presentation/commands/moderation/DeleteMessageCommandHandler.ts"
  "src/presentation/commands/moderation/EditMessageCommandHandler.ts"
  "src/presentation/commands/moderation/ReportCommandHandler.ts"
  "src/presentation/commands/staff/BadgeCommandHandler.ts"
  "src/presentation/commands/staff/BanCommandHandler.ts"
  "src/presentation/commands/staff/FindCommandHandler.ts"
  "src/presentation/commands/utilities/InboxCommandHandler.ts"
)

echo "Fixing command handlers to inject SentryService..."

for file in "${files[@]}"; do
  echo "Processing $file..."

  # Check if file exists
  if [ -f "$file" ]; then
    # Add SentryService import if not present
    if ! grep -q "SentryService" "$file"; then
      # Find the line with TYPES import and add SentryService import after it
      if grep -q "import { TYPES }" "$file"; then
        sed -i '/import { TYPES }/a import { SentryService } from '"'"'../../../infrastructure/observability/SentryService.js'"'"';' "$file"
      elif grep -q "import.*TYPES.*from" "$file"; then
        sed -i '/import.*TYPES.*from/a import { SentryService } from '"'"'../../../infrastructure/observability/SentryService.js'"'"';' "$file"
      fi
    fi

    # Fix constructor to inject SentryService and pass to super()
    # Look for constructor patterns and add SentryService injection
    if grep -q "super();" "$file"; then
      # Replace super(); with proper SentryService injection
      sed -i 's/super();/super(sentry);/' "$file"

      # Add SentryService parameter to constructor if not present
      if ! grep -q "@inject(TYPES.SentryService)" "$file"; then
        # Find the constructor line and add SentryService parameter
        sed -i '/constructor(/,/)/ {
          /constructor(/ {
            s/)/ @inject(TYPES.SentryService) sentry: SentryService,)/
          }
          /).*{/ {
            s/)/ @inject(TYPES.SentryService) sentry: SentryService,)/
          }
        }' "$file"
      fi
    fi

    echo "✅ Fixed $file"
  else
    echo "❌ File not found: $file"
  fi
done

echo "🎉 All command handlers have been updated!"
